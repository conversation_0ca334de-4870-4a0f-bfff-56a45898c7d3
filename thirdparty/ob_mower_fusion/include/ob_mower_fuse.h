/*****************************************************************************
 *  Orbbec Vision Core Library
 *  Copyright (C) 2017 by ORBBEC Technology., Inc.
 *
 *  This file is part of Orbbec Vision Core Library 2.0
 *
 *  This file belongs to ORBBEC Technology., Inc.
 *  It is considered a trade secret, and is not to be divulged or used by
 *  parties who have NOT received written authorization from the owner.
 ****************************************************************************/

#ifndef __OB_MOWER_FUSE_H__
#define __OB_MOWER_FUSE_H__

#include "common_defs.h"
#include "ob_mower_fuse_errcodes.h"
#include "segmentation_fusion_defs.h"

#include <iostream>
#include <string>
#include <vector>


#ifdef __cplusplus
extern "C" {

#endif

#ifdef _WIN32
#ifdef VCAL_EXPORTS
#define VCAL_API __declspec(dllexport)
#else
#define VCAL_API __declspec(dllimport)
#endif
#else
#define VCAL_API
#endif

/* 草区域最多单元格数量，65205 = 255 * 255 */
#define MAX_CELL_NUM 65205
#define IMAGE_WIDTH 640  // 输入图像宽度
#define IMAGE_HEIGHT 360 // 输入图像高度

/** define the avoid obstacle handle */
#define FUSEOBS_HANDLE void *

// 去畸变参数结构体
typedef struct
{
    int cameraModel = 1;                                                                       // CameraModel: Brown = 0; KB = 1; MEI = 2; DS = 3
    std::vector<double> intrinsics = {545.685, 545.602, 622.641, 385.252};                     // fx fy cx fy
    std::vector<double> distortion_coeffs = {-0.039713, 0.00383158, 0.000867125, -0.00116478}; // K6 model(Brown): k1 ~ k6 p1 p2;   KB model: k1 k2 k3 k4
    std::vector<int> resolution = {1280, 720};                                                 // width height

} undistortParams;

// 输入参数结构体
typedef struct
{
    int debug = 0;                    // 0: 关闭调试模式,
                                      // 1: 开启调试模式仅输出打印,
                                      // 2: 开启调试模式在foxglove中结果可视化,
    int img_width = IMAGE_WIDTH;      // 输入图片的宽度.
    int img_height = IMAGE_HEIGHT;    // 输入图片的高度.
    float cross_thresh = 0.25f;       // 语义分割与目标检测交叉区域阈值, 如果该轮廓在某个目标检测框内，则将此轮廓合并到目标检测框中.
    int undistort_enabled = 1;        // 是否启用去畸变，1:启用。 特别注意：此处只能设置为1，后续参数会丢弃，不再开放。
    undistortParams undistort_params; // 去畸变参数.
} FuseInitParams;

// 分割结果结构体
// obstacle_contours : 边界轮廓
typedef struct
{ // 语义分割算法输出的处于安全区域内的所有障碍物轮廓。
    // std::vector<std::vector<ObPoint>> obstacle_contours;
    std::vector<Obstacle> seg_obstacles;
    Position mowerPosition;             // 表示割草机位置。
    SegImageBuffer InversePerspectMask; // 割草机逆透视投影后的安全线内的mask。
    SegImageBuffer InversePerspectRgb;  // 割草机逆透视投影后的安全线内的rgb图片。
    Vector deadLineLeft;                // 转成BEV视图后左下角盲区的斜线向量。其中，起点（靠近割草机位置的点作为起点），终点（远离割草机位置的点作为终点）。
    Vector deadLineRight;               // 转成BEV视图后右下角盲区的斜线向量。其中，起点（靠近割草机位置的点作为起点），终点（远离割草机位置的点作为终点）。
    uint64_t inputtimestamp;            // 记录系统时间戳，单位：毫秒。注意：此处记录的是输入图片给算法时的时间戳。
    uint64_t outputtimestamp;           // 记录系统时间戳，单位：毫秒。注意：此处记录的时间是分割算法处理完成当前图片后，记录当前系统时间戳。
    int BoundaryStatus;                 // 0:表示没有检测到草地，1:表示检测到草地，但是草地内没有检测到边界,  2:表示检测到边界。
    int BoundaryLeft;                   // 0:表示没有检测到左向边界， 1:表示左向边界检测位。
    int BoundaryAhead;                  // 0:表示没有检测到正向边界， 1:表示正向边界检测位。
    int BoundaryRight;                  // 0:表示没有检测到右向边界， 1:表示右向边界检测位。
    ObPoint min_dist_point;             // 在BEV视角下，距离割草机最近的障碍物坐标点。
    float left_min_distance;            // 表示割草机距离左侧障碍物mask的y方向距离（单位：米）。只计算y方向的距离，并且非草地内无效。
    float ahead_min_distance;           // 表示割草机距离正前方障碍物mask的y方向距离（单位：米）。只计算y方向的距离，并且非草地内无效。
    float right_min_distance;           // 表示割草机距离右侧障碍物mask的y方向距离（单位：米）。只计算y方向的距离，并且非草地内无效。

    // 左边界向量
    Vector leftBoundaryVector;

    // 右边界向量
    Vector rightBoundaryVector;

    // 栅格图
    std::vector<std::vector<unsigned char>> grid_mask;
    // 草区域栅格图BEV视角
    GrassRegionResult bev_grass_region;

} SegmentResult;

// 障碍物识别结果结构体
// classID			: 障碍物类别ID
// objectContour	: 障碍物轮廓
typedef struct
{
    int classID;
    std::vector<ObPoint> objectContour;
} DetectionResult;

typedef enum
{
    SYSTEM_ERROR = -1,
    SYSTEM_NO_OBSTACLE = 0,
    SYSTEM_IS_OBSTACLE = 1,
} ObstacleStatus;

typedef struct
{
    int POSITION_LEFT;
    int POSITION_AHEAD;
    int POSITION_RIGHT;

} ObstaclePosition;

// 融合结果结构体
// classID			: 障碍物类别ID
// obstaclePosition	: 障碍物位置
// objectContour	: 障碍物轮廓
typedef struct
{
    int classID;
    ObstaclePosition obstaclePosition;
    std::vector<ObPoint> objectContour;
} FuseObstacleInfo;

// 分割和障碍物识别融合结果结构体
// obstacleStatus	: 障碍物检测状态
// obstacleNum		: 障碍物数量
// obstacleInfo		: 障碍物信息(类别ID、方位、轮廓)
// debug_img_info	: 调试图像信息
typedef struct
{
    ObstacleStatus obstacleStatus = ObstacleStatus::SYSTEM_NO_OBSTACLE;
    int obstacleNum;
    std::vector<FuseObstacleInfo> obstacleInfo;
    SegImageBuffer debug_img_info;

    // 添加分割结果相关
    Position mowerPosition;             // 表示割草机位置。
    SegImageBuffer InversePerspectMask; // 割草机逆透视投影后的安全线内的mask。
    SegImageBuffer InversePerspectRgb;  // 割草机逆透视投影后的安全线内的rgb图片。
    Vector deadLineLeft;                // 转成BEV视图后左下角盲区的斜线向量。其中，起点（靠近割草机位置的点作为起点），终点（远离割草机位置的点作为终点）。
    Vector deadLineRight;               // 转成BEV视图后右下角盲区的斜线向量。其中，起点（靠近割草机位置的点作为起点），终点（远离割草机位置的点作为终点）。
    uint64_t inputtimestamp;            // 记录系统时间戳，单位：毫秒。注意：此处记录的是输入图片给算法时的时间戳。
    uint64_t outputtimestamp;           // 记录系统时间戳，单位：毫秒。注意：此处记录的时间是分割算法处理完成当前图片后，记录当前系统时间戳。
    int BoundaryStatus;                 // 0:表示没有检测到草地，1:表示检测到草地，但是草地内没有检测到边界,  2:表示检测到边界。
    int BoundaryLeft;                   // 0:表示没有检测到左向边界， 1:表示左向边界检测位。
    int BoundaryAhead;                  // 0:表示没有检测到正向边界， 1:表示正向边界检测位。
    int BoundaryRight;                  // 0:表示没有检测到右向边界， 1:表示右向边界检测位。
    ObPoint min_dist_point;             // 在BEV视角下，距离割草机最近的障碍物坐标点。
    float left_min_distance;            // 表示割草机距离左侧障碍物mask的y方向距离（单位：米）。只计算y方向的距离，并且非草地内无效。
    float ahead_min_distance;           // 表示割草机距离正前方障碍物mask的y方向距离（单位：米）。只计算y方向的距离，并且非草地内无效。
    float right_min_distance;           // 表示割草机距离右侧障碍物mask的y方向距离（单位：米）。只计算y方向的距离，并且非草地内无效。

    // 左边界向量
    Vector leftBoundaryVector;

    // 右边界向量
    Vector rightBoundaryVector;

    // 栅格图
    std::vector<std::vector<unsigned char>> grid_mask;
    // 草区域栅格图BEV视角
    GrassRegionResult bev_grass_region;

    // 安全区域内有“非草地”时，输出所有“非草地”的轮廓。
    std::vector<std::vector<ObPoint>> obstacle_contours;
    // 重置函数，重置成在草地上，没有障碍物的状态。
    void reset()
    {
        obstacleStatus = ObstacleStatus::SYSTEM_NO_OBSTACLE;
        obstacleNum = 0;
        obstacleInfo.clear();

        BoundaryStatus = 1; // 1:表示检测到草地，但是草地内没有检测到边界
        BoundaryLeft = 0;
        BoundaryAhead = 0;
        BoundaryRight = 0;

        // 清空非草地轮廓
        obstacle_contours.clear();
    }

} FuseObstacleResult;

/**
 * @brief 融合算法初始化函数
 *
 * @param fuseObsHandle 融合算法句柄
 * @param config_path 配置文件路径
 * @param SegInitResult 语义分割初始化结果
 * @return 返回错误码，一个整数值。含义如下：
 * - 0                     0：表示成功。
 * - 1                     1：配置文件读取失败。
 * - 2                     2：结构化参数初始化失败。
 * - 3                     3：安全区域坐标点无效。
 * - 4                     4：畸变校正初始化失败。
 * - 5                     5：语义分割初始化失败。
 */
VCAL_API int fuseObsCreat(FUSEOBS_HANDLE *fuseObsHandle, char *config_path, SegRgbInitResult &SegInitResult);

// ********** 初始化时，调用完fuseObsCreatFromStruct后，务必调用fuseObsCreatFromSegInit *************** //
/**
 * @brief 通过结构体方式初始化融合算法参数。
 *
 * @param fuseObsHandle 融合算法句柄
 * @param initParams 初始化参数结构体
 * @return 返回错误码，一个整数值。含义如下：
 * - 0                     0：表示成功。
 * - 2                     2：结构化参数初始化失败。
 * - 3                     3：安全区域坐标点无效。
 * - 4                     4：畸变校正初始化失败。
 * - 5                     5：语义分割初始化失败。
 */
VCAL_API int fuseObsCreatFromStruct(FUSEOBS_HANDLE *fuseObsHandle, FuseInitParams &initParams);

// fuseObsCreatFromSegInit 函数，使用语义分割初始化结果，初始化融合算法参数。
// 特别注意：当语义分割初始化参数变换时，需要使用fuseObsCreatFromSegInit重新初始化一次。

/**
 * @brief 通过语义分割初始化结果初始化融合算法参数。
 *
 * @param fuseObsHandle 融合算法句柄
 * @param SegInitResult 语义分割初始化结果
 * @return 返回错误码，一个整数值。含义如下：
 * - 0                     0：表示成功。
 * - 3                     3：安全区域坐标点无效。
 * - 4                     4：畸变校正初始化失败。
 * - 5                     5：标定点坐标去畸变错误。
 *
 */
VCAL_API int fuseObsCreatFromSegInit(FUSEOBS_HANDLE *fuseObsHandle, SegRgbInitResult &SegInitResult);
// *************************************************************************************************** //

VCAL_API void fuseObsRelease(FUSEOBS_HANDLE fuseObsHandle);

/**
 * @brief 融合算法执行函数
 *
 * @param fuseObsHandle 融合算法句柄
 * @param segmentResult 语义分割结果
 * @param detectionResult 目标检测结果
 * @param fuseObstacleResult 融合结果
 * @return 返回错误码，一个整数值。含义如下：
 * - 0                     0：表示成功。
 * - 10                     10：处理分割算法输入的轮廓过程中失败。
 * - 11                     11：处理目标检测输入的结果过程中失败。
 * - 12                     12：在处理最终的融合过程中失败。
 * - 13                     13：在处理最终的融合过程中失败。
 * - 14                     14：在生成栅格图过程中失败。	 *
 */
VCAL_API int fuseObsExecute(FUSEOBS_HANDLE fuseObsHandle, SegmentResult &segmentResult,
                            std::vector<DetectionResult> &detectionResult, FuseObstacleResult &fuseObstacleResult);
// 获取初始化参数
/**
 * @brief 获取初始化参数
 *
 * @param fuseObsHandle 融合算法句柄
 * @return 返回初始化参数
 */
VCAL_API FuseInitParams fuseObsGetParams(FUSEOBS_HANDLE *fuseObsHandle);
// 设置初始化参数
/**
 * @brief 设置初始化参数
 *
 * @param fuseObsHandle 融合算法句柄
 * @param initParams 初始化参数
 * @return 返回错误码，一个整数值。含义如下：
 * - 0                     0：表示成功。
 * - 2                     2：结构化参数初始化失败。
 * - 3                     3：安全区域坐标点无效。
 * - 4                     4：畸变校正初始化失败。
 * - 5                     5：语义分割初始化失败。
 */
VCAL_API int fuseObsSetParams(FUSEOBS_HANDLE *fuseObsHandle, FuseInitParams &initParams);

VCAL_API const char *GetFuseVersion();

#ifdef __cplusplus
}
#endif

#endif //__OB_MOWER_FUSE_H__
