#pragma once

#include "iox/string.hpp"
#include "iox/vector.hpp"

#include <cstdint>

namespace fescue_iox::ob_mower_srvs
{

struct DeviceSelfCheckData
{
    float linear{0};
    float angular{0};
    uint64_t duration_ms{0};
};

struct SetDeviceSelfCheckRequest
{
    DeviceSelfCheckData data;
};

struct SetDeviceSelfCheckResponse
{
    bool success{false};
};

} // namespace fescue_iox::ob_mower_srvs