#pragma once

#include "iox/vector.hpp"

#include <chrono>
#include <cstdint>

#ifdef __cplusplus
extern "C" {
#endif

#define IOX_MAX_MARK_BOX_NUM 5

/**
 * @brief 规控最终回充的结果
 */
typedef struct fescue_msgs__msg__NavRechargeFinalResult
{
    /* 时间戳 */
    uint64_t timestamp;
    /* 完成状态 */
    bool completed;
    /* 回充结果，false - 失败，true - 成功 */
    bool result;
} fescue_msgs__msg__NavRechargeFinalResult;

#ifdef __cplusplus
}
#endif
