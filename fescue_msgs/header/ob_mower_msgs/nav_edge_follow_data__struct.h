#pragma once

#include "geometry_msgs/pose__struct.h"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "nav_msgs/path__struct.h"

#include <chrono>
#include <cstdint>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 延边数据结构体
 */
typedef struct fescue_msgs__msg__NavigationEdgeFollowData
{
    /* 像素与米之间的比例 */
    float pixels_to_meters;
    /* 机器人位置 */
    geometry_msgs__msg__Pose_iox robot_pose;
    /*  路径点 */
    nav_msgs__msg__Path_iox path;
} fescue_msgs__msg__NavigationEdgeFollowData;

#ifdef __cplusplus
}
#endif
