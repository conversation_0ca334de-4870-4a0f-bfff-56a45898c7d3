// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from builtin_interfaces:msg/Time.idl
// generated code does not contain a copyright notice

#pragma once

#include "iceoryx_hoofs/cxx/vector.hpp"

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// Constants defined in the message

// Struct defined in msg/Time in the package builtin_interfaces.
typedef struct builtin_interfaces__msg__Time_iox
{
    uint64_t sec = 0;
    uint64_t nanosec = 0;
} builtin_interfaces__msg__Time_iox;

// Struct for a sequence of builtin_interfaces__msg__Time_iox.
typedef struct builtin_interfaces__msg__Time__Sequence_iox
{
    iox::cxx::vector<builtin_interfaces__msg__Time_iox, 32> data;
    /// The number of valid items in data
    size_t size;
    /// The number of allocated items in data
    size_t capacity;
} builtin_interfaces__msg__Time__Sequence_iox;

#ifdef __cplusplus
}
#endif
