#pragma once

#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "lawnmower_pose_api.h"
#include "mower_msgs/msg/mcu_imu.hpp"
#include "mower_msgs/msg/slope_detection_result.hpp"
#include "mower_msgs/msg/soc_imu.hpp"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "ob_mower_msgs/perception_localization_alg_ctrl.h"
#include "utils/logger.hpp"
#include "utils/utils.hpp"

#include <condition_variable>
#include <mutex>
#include <string>
#include <thread>

namespace fescue_iox
{

class LocalizationSlopeDetectionAlg
{
public:
    using iox_slope_detection_result_publisher = iox::popo::Publisher<mower_msgs::msg::LawnmowerSlopeStatus>;

public:
    LocalizationSlopeDetectionAlg(const std::string &conf_file);
    ~LocalizationSlopeDetectionAlg();
    void DoSlopeDetection(const mower_msgs::msg::McuImu &data);
    void DoSlopeDetection(const mower_msgs::msg::SocImu &data);
    const char *GetAlgVersion();

private:
    void InitAlgo();
    void DeinitAlgo();
    void InitAlgoParam();
    void InitPublisher();
    static void SlopeStatusCallback(const ob_lawnmower_slope_status *result);

private:
    // publisher
    inline static std::unique_ptr<iox_slope_detection_result_publisher> pub_slope_detection_result_{nullptr};
    std::string conf_file_{""};
    LAWNMOWER_POSE_HANDLE slope_detection_handle_{nullptr};
};

} // namespace fescue_iox