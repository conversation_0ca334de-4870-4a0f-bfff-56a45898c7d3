#include "localization_slope_detection_node.hpp"

#include "mower_sdk_version.h"
#include "utils/dir.hpp"
#include "utils/file.hpp"
#include "utils/logger.hpp"
#include "utils/utils.hpp"

namespace fescue_iox
{

LocalizationSlopeDetectionNode::LocalizationSlopeDetectionNode(const std::string &node_name)
    : node_name_(node_name)
{
    InitWorkingDirectory();
    InitParam();
    InitLogger();
    InitHeartbeat();
    InitAlgorithms();
    InitSubscriber();
    InitService();
}

LocalizationSlopeDetectionNode::~LocalizationSlopeDetectionNode()
{
    LOG_WARN("LocalizationSlopeDetectionNode exit success!");
}

void LocalizationSlopeDetectionNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void LocalizationSlopeDetectionNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

void LocalizationSlopeDetectionNode::InitParam()
{
    const std::string conf_file{"conf/localization_slope_detection_node/localization_slope_detection_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("localization_slope_detection_node create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("localization_slope_detection_node create config path failed!!!");
        }
    }
    if (!Config<LocalizationSlopeDetectionNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init localization_slope_detection_node config parameters failed!");
    }
    LocalizationSlopeDetectionNodeConfig config = Config<LocalizationSlopeDetectionNodeConfig>::GetConfig();

    LOG_INFO("[localization_slope_detection_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[localization_slope_detection_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[localization_slope_detection_node] compile time: {}", _COMPILE_TIME_);
    LOG_INFO("{}", config.toString().c_str());

    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    slope_detection_conf_file_ = config.slope_detection_conf_file;

    if (!Config<LocalizationSlopeDetectionNodeConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set localization_slope_detection_node config parameters failed!");
    }

    CreateDirectories(log_dir_);
}

void LocalizationSlopeDetectionNode::InitLogger()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void LocalizationSlopeDetectionNode::InitAlgorithms()
{
    slope_detection_alg_ = std::make_unique<LocalizationSlopeDetectionAlg>(slope_detection_conf_file_);
}

void LocalizationSlopeDetectionNode::InitSubscriber()
{
#if 0
    sub_mcu_imu_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuImu>>(
        "mcu_imu", 1, [this](const mower_msgs::msg::McuImu &data, const std::string &event) {
            (void)event;
            DealImuData(data);
        });
#else
    sub_soc_imu_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::SocImu>>(
        "soc_imu", 1, [this](const mower_msgs::msg::SocImu &data, const std::string &event) {
            (void)event;
            DealImuData(data);
        });
#endif
    sub_algo_ctrl_ = std::make_unique<IceoryxSubscriberMower<ob_mower_msgs::PerceptionLocalizationAlgCtrl>>(
        "perception_localization_alg_ctrl", 5, [this](const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data, const std::string &event) {
            (void)event;
            DealAlgCtrl(data);
        });
}

void LocalizationSlopeDetectionNode::InitService()
{
    service_get_node_param_ = std::make_unique<IceoryxServerMower<get_node_param_request, get_node_param_response>>(
        "get_localization_slope_detection_node_param", 1, [this](const get_node_param_request &request, get_node_param_response &response) {
            (void)request;
            response.success = DealGetNodeParam(response.data);
            LOG_INFO("Get localization slope detection node param execute {}", response.success);
        });

    service_set_node_param_ = std::make_unique<IceoryxServerMower<set_node_param_request, set_node_param_response>>(
        "set_localization_slope_detection_node_param", 1, [this](const set_node_param_request &request, set_node_param_response &response) {
            response.success = DealSetNodeParam(request.data);
            LOG_INFO("Set localization slope detection node param execute {}", response.success);
        });

    service_get_alg_version_ = std::make_unique<IceoryxServerMower<get_alg_version_request, get_alg_version_response>>(
        "get_localization_slope_detection_algo_version", 1, [this](const get_alg_version_request &request, get_alg_version_response &response) {
            (void)request;
            response.success = DealGetAlgVersion(response.data);
            response.timestamp = GetTimestampMs();
            LOG_INFO("Get localization slope detection algo version execute {}", response.success);
        });
}

void LocalizationSlopeDetectionNode::DealImuData(const mower_msgs::msg::McuImu &data)
{
    if (!slope_detection_alg_enable_)
    {
        LOG_DEBUG("Localization slope detection algo is disabled by config!");
        return;
    }

    if (slope_detection_alg_)
    {
        slope_detection_alg_->DoSlopeDetection(data);
    }
}

void LocalizationSlopeDetectionNode::DealImuData(const mower_msgs::msg::SocImu &data)
{
    if (!slope_detection_alg_enable_)
    {
        LOG_DEBUG("Localization slope detection algo is disabled by config!");
        return;
    }

    if (slope_detection_alg_)
    {
        slope_detection_alg_->DoSlopeDetection(data);
    }
}

bool LocalizationSlopeDetectionNode::DealGetAlgVersion(ob_mower_srvs::AlgoVersionDataVect &data)
{
    if (slope_detection_alg_)
    {
        ob_mower_srvs::AlgoVersionData alg_version_data;
        alg_version_data.algo_name.unsafe_assign("localization_slope_detection");
        alg_version_data.version.unsafe_assign(slope_detection_alg_->GetAlgVersion());
        data.version_data_vect.push_back(alg_version_data);
    }
    return true;
}

void LocalizationSlopeDetectionNode::DealAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data)
{
    using namespace ob_mower_msgs;
    for (size_t i = 0; i < data.ctrl_list.size(); i++)
    {
        if (data.ctrl_list[i].alg_type == PerceptionLocalizationAlgType::LOCALIZATION_SLOPE_DETECTION)
        {
            slope_detection_alg_enable_ = (data.ctrl_list[i].alg_state == PerceptionLocalizationAlgState::ENABLE ? true : false);
            LOG_WARN("{} {}", asStringLiteral(data.ctrl_list[i].alg_type).c_str(), asStringLiteral(data.ctrl_list[i].alg_state).c_str());
            break;
        }
    }
}

bool LocalizationSlopeDetectionNode::DealGetNodeParam(ob_mower_srvs::NodeParamData &data)
{
    LocalizationSlopeDetectionNodeConfig config = Config<LocalizationSlopeDetectionNodeConfig>::GetConfig();
    data.console_log_level.unsafe_assign(config.common_conf.console_log_level.c_str());
    data.file_log_level.unsafe_assign(config.common_conf.file_log_level.c_str());
    return true;
}

bool LocalizationSlopeDetectionNode::DealSetNodeParam(const ob_mower_srvs::NodeParamData &data)
{
    console_log_level_ = std::string(data.console_log_level.c_str());
    file_log_level_ = std::string(data.file_log_level.c_str());
    InitLogger();
    LocalizationSlopeDetectionNodeConfig config = Config<LocalizationSlopeDetectionNodeConfig>::GetConfig();
    config.common_conf.console_log_level = console_log_level_;
    config.common_conf.file_log_level = file_log_level_;
    Config<LocalizationSlopeDetectionNodeConfig>::SetConfig(config);
    LOG_INFO("New LocalizationSlopeDetectionNodeConfig params: {}", config.toString().c_str());
    return true;
}

} // namespace fescue_iox