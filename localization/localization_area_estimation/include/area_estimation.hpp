#pragma once

#include "area_estimation_api.h"
#include "area_estimation_config.hpp"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "mower_msgs/msg/mcu_imu.hpp"
#include "mower_msgs/msg/mcu_motor_speed.hpp"
#include "mower_msgs/msg/soc_exception.hpp"
#include "mower_msgs/msg/soc_imu.hpp"
#include "utils/iceoryx_publisher_mower.hpp"

#include <condition_variable>
#include <cstdint>
#include <mutex>
#include <string>
#include <thread>

namespace fescue_iox
{

class LocalizationAreaEstimationAlg
{
public:
    LocalizationAreaEstimationAlg(const std::string &conf_file);
    ~LocalizationAreaEstimationAlg();
    void DoMotorSpeedData(const mower_msgs::msg::McuMotorSpeed &data);
    void DoImuData(const mower_msgs::msg::McuImu &data);
    void DoImuData(const mower_msgs::msg::SocImu &data);

    ob_area_estimation_result GetAreaEstimationResult() { return alg_result_; };
    void StartAlgo();
    void StopAlgo();
    void InitAreaEstimation();
    bool GetAlgParam(Area_estimation_config &param);
    bool SetAlgParam(Area_estimation_config &param);
    const char *GetAlgVersion();
    bool IsStarted() { return started_.load(); };

private:
    void InitAlgo();
    void DeinitAlgo();
    void InitAlgoParam();
    void InitPublisher();
    void PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);

    static void AreaEstimationResultCallback(const ob_area_estimation_result *result);

private:
    inline static ob_area_estimation_result alg_result_{};
    std::unique_ptr<IceoryxPublisherMower<mower_msgs::msg::SocException>> pub_exception_{nullptr};

    std::string conf_file_{""};
    std::atomic<bool> started_{false};
    AREA_ESTIMATION_HANDLE alg_handle_{nullptr};
};

} // namespace fescue_iox