#include "localization_recharge_node.hpp"

#include "localization_recharge_node_config.hpp"
#include "mower_sdk_version.h"
#include "utils/algo_ctrl.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <cmath>      // for std::labs()
#include <filesystem> //c++17

namespace fescue_iox
{

LocalizationRechargeNode::LocalizationRechargeNode(const std::string &node_name)
    : node_name_(node_name)
{
    InitWorkingDirectory();
    InitParams();
    InitSpdLog();
    InitAlgorithm();
    InitSubscriber();
    InitService();
    InitHeartbeat();
}

LocalizationRechargeNode::~LocalizationRechargeNode()
{
    LOG_WARN("LocalizationRechargeNode stop success!");
}

void LocalizationRechargeNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void LocalizationRechargeNode::InitParams()
{
    const std::string conf_file{"conf/localization_recharge_node/localization_recharge_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("localization_recharge_node create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("localization_recharge_node create config path failed!!!");
        }
    }
    if (!Config<LocalizationRechargeNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init localization_recharge_node config parameters failed!");
    }
    LocalizationRechargeNodeConfig config = Config<LocalizationRechargeNodeConfig>::GetConfig();

    LOG_INFO("[localization_recharge_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[localization_recharge_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[localization_recharge_node] compile time: {}", _COMPILE_TIME_);

    LOG_INFO("{}", config.toString().c_str());

    log_dir_ = config.log_dir;
    console_log_level_ = config.console_log_level;
    file_log_level_ = config.file_log_level;
    alg_conf_file_ = config.alg_conf_file;

    if (!Config<LocalizationRechargeNodeConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set localization_recharge_node config parameters failed!");
    }

    CreateDirectories(log_dir_);
}

void LocalizationRechargeNode::InitSpdLog()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void LocalizationRechargeNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

void LocalizationRechargeNode::InitAlgorithm()
{
    Recharge_camera_intrinsic intrinsic_param;
    int err_count = 0;
    bool result = true;

    while (!GetCameraIntrinsicsParam(intrinsic_param))
    {
        err_count++;
        LOG_WARN("localization_recharge_node get camera intrinsics param fail {} times, try again!", err_count);
        if (err_count >= max_err_time_)
        {
            LOG_ERROR("localization_recharge_node get camera intrinsics param fail {} times, use default param!", max_err_time_);
            result = false;
            break;
        }
        sleep(1);
    }

    if (result)
    {
        LOG_INFO("localization_recharge_node get camera intrinsics param successful!");
        ShowIntrinsicParam(intrinsic_param);
    }

    charge_loc_alg_ = std::make_unique<ChargeStationLocalizationAlg>(alg_conf_file_, intrinsic_param, result);
    algo_version_map_["localization_recharge"] = std::string(charge_loc_alg_->GetQRCodeLocationAlgoVersion());
}

void LocalizationRechargeNode::ShowIntrinsicParam(const Recharge_camera_intrinsic &param)
{
    LOG_INFO("----------- Camera intrinsics param ----------------");
    LOG_INFO("  model_: {}", param.model_);
    LOG_INFO("  img_width_: {}", param.img_width_);
    LOG_INFO("  img_height_: {}", param.img_height_);
    LOG_INFO("  focal_x_: {:.6f}", param.focal_x_);
    LOG_INFO("  focal_y_: {:.6f}", param.focal_y_);
    LOG_INFO("  cx_: {:.6f}", param.cx_);
    LOG_INFO("  cy_: {:.6f}", param.cy_);
    LOG_INFO("  k1_: {:.6f}", param.k1_);
    LOG_INFO("  k2_: {:.6f}", param.k2_);
    LOG_INFO("  k3_: {:.6f}", param.k3_);
    LOG_INFO("  k4_: {:.6f}", param.k4_);
    LOG_INFO("  k5_: {:.6f}", param.k5_);
    LOG_INFO("  k6_: {:.6f}", param.k6_);
    LOG_INFO("  p1_: {:.6f}", param.p1_);
    LOG_INFO("  p2_: {:.6f}", param.p2_);
    LOG_INFO("----------- Camera intrinsics param ----------------");
}

void LocalizationRechargeNode::InitSubscriber()
{
    sub_rgb_1280x720_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "camera_color_1280x720_result", 1, [this](const sensor_msgs__msg__Image_iox &data, const std::string &event) {
            (void)event;
            DealChargeLocalization(data);
        });
    sub_charge_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__ChargeResult>>(
        "detect_charge_result", 1, [this](const fescue_msgs__msg__ChargeResult &data, const std::string &event) {
            (void)event;
            DealChargeDetectionResult(data);
        });
    sub_algo_ctrl_ = std::make_unique<IceoryxSubscriberMower<ob_mower_msgs::PerceptionLocalizationAlgCtrl>>(
        "perception_localization_alg_ctrl", 5, [this](const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data, const std::string &event) {
            (void)event;
            DealAlgCtrl(data);
        });
}

void LocalizationRechargeNode::InitService()
{
    service_get_node_param_ = std::make_unique<IceoryxServerMower<get_node_param_request, get_node_param_response>>(
        "get_localization_recharge_node_param_request", 10U, [this](const get_node_param_request &request, get_node_param_response &response) {
            (void)request;
            response.success = DealGetNodeParam(response.data);
            LOG_INFO("Get localization_recharge_node params execute {}", response.success);
        });
    service_set_node_param_ = std::make_unique<IceoryxServerMower<set_node_param_request, set_node_param_response>>(
        "set_localization_recharge_node_param_request", 10U, [this](const set_node_param_request &request, set_node_param_response &response) {
            response.success = DealSetNodeParam(request.data);
            LOG_INFO("Set localization_recharge_node params execute {}", response.success);
        });

    service_get_alg_param_ = std::make_unique<IceoryxServerMower<get_alg_param_request, get_alg_param_response>>(
        "get_qrcode_location_param_request", 10U, [this](const get_alg_param_request &request, get_alg_param_response &response) {
            (void)request;
            response.success = DealGetAlgParam(response.data);
            LOG_INFO("Get perception recharge localization alg params execute {}", response.success);
        });
    service_set_alg_param_ = std::make_unique<IceoryxServerMower<set_alg_param_request, set_alg_param_response>>(
        "set_qrcode_location_param_request", 10U, [this](const set_alg_param_request &request, set_alg_param_response &response) {
            response.success = DealSetAlgParam(request.data);
            LOG_INFO("Set perception recharge localization alg params execute {}", response.success);
        });

    service_get_algo_version_ = std::make_unique<IceoryxServerMower<get_alg_version_request, get_alg_version_response>>(
        "get_localization_algo_version", 10U, [this](const get_alg_version_request &request, get_alg_version_response &response) {
            (void)request;
            response.success = DealGetAlgorithmsVersion(response);
            LOG_INFO("get_localization_algo_version execute {}", response.success);
        });
}

void LocalizationRechargeNode::DealAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data)
{
    for (size_t i = 0; i < data.ctrl_list.size(); i++)
    {
        if (data.ctrl_list[i].alg_type == ob_mower_msgs::PerceptionLocalizationAlgType::LOCALIZATION_RECHARGE_QRCODE_LOCALIZATION)
        {
            alg_enable_ = (data.ctrl_list[i].alg_state == ob_mower_msgs::PerceptionLocalizationAlgState::ENABLE ? true : false);
            LOG_WARN("{} {}", asStringLiteral(data.ctrl_list[i].alg_type).c_str(), asStringLiteral(data.ctrl_list[i].alg_state).c_str());
            break;
        }
    }
}

bool LocalizationRechargeNode::DealSetNodeParam(const ob_mower_srvs::NodeParamData &data)
{
    console_log_level_ = std::string(data.console_log_level.c_str());
    file_log_level_ = std::string(data.file_log_level.c_str());
    InitSpdLog();
    LocalizationRechargeNodeConfig config = Config<LocalizationRechargeNodeConfig>::GetConfig();
    config.console_log_level = console_log_level_;
    config.file_log_level = file_log_level_;
    Config<LocalizationRechargeNodeConfig>::SetConfig(config);
    LOG_INFO("New localization_recharge_node params: {}", config.toString().c_str());
    return true;
}

bool LocalizationRechargeNode::DealGetNodeParam(ob_mower_srvs::NodeParamData &data)
{
    LocalizationRechargeNodeConfig config = Config<LocalizationRechargeNodeConfig>::GetConfig();
    data.console_log_level.unsafe_assign(config.console_log_level.c_str());
    data.file_log_level.unsafe_assign(config.file_log_level.c_str());
    return true;
}

bool LocalizationRechargeNode::DealGetAlgorithmsVersion(get_alg_version_response &response)
{
    GetLocalizationAlgVersion("get_localization_area_estimation_algo_version");
    GetLocalizationAlgVersion("get_localization_cross_region_algo_version");
    GetLocalizationAlgVersion("get_localization_motion_detection_algo_version");
    GetLocalizationAlgVersion("get_localization_slope_detection_algo_version");
    response.module_version.unsafe_assign(std::string(_GIT_TAG_).c_str());
    for (auto it = algo_version_map_.begin(); it != algo_version_map_.end(); ++it)
    {
        fescue_msgs__msg__AlgorithmVersionData version_data;
        version_data.name.unsafe_assign(it->first.c_str());
        version_data.version.unsafe_assign(it->second.c_str());
        response.data.push_back(version_data);
    }
    return true;
}

bool LocalizationRechargeNode::DealSetAlgParam(const fescue_msgs__msg__QRCodeLocationAlgParam &data)
{
    if (charge_loc_alg_)
    {
        Recharge_config param;
        charge_loc_alg_->GetQRCodeLocationParam(param);
        param.verbosity = data.verbosity;
        param.showImg = data.showImg;
        param.only_use_perception = data.only_use_perception;
        param.percept_time_diff_thre = data.percept_time_diff_thre;
        param.writeImg = data.writeImg;
        param.Perceptual_window_ratio = data.Perceptual_window_ratio;
        param.plane_distance_threshold = data.plane_distance_threshold;
        param.pnp_method = data.pnp_method;
        param.aprilTagMinClusterPixels = data.aprilTagMinClusterPixels;
        param.minMarkerPerimeterRate = data.minMarkerPerimeterRate;
        param.maxMarkerPerimeterRate = data.maxMarkerPerimeterRate;
        param.outputRollAng = data.outputRollAng;
        param.cornerRefinementMethod = data.cornerRefinementMethod;
        param.use_bilateral_filter = data.use_bilateral_filter;
        param.use_nearby_speed_up = data.use_nearby_speed_up;                   // default: 0
        param.minCornerDistanceRate = data.minCornerDistanceRate;               // default: 0.1
        param.edgeThresholdRate = data.edgeThresholdRate;                       // default 0.1
        param.detection_area_size = data.detection_area_size;                   // default 40000
        param.nearbyMinMarkerPerimeterRate = data.nearbyMinMarkerPerimeterRate; // default 0.3
        LOG_INFO("SetQrCodeAlgoParam: verbosity {}, showImg {}, only_use_perception {} percept_time_diff_thre {} writeImg {} Perceptual_window_ratio {} pnp_method {} aprilTagMinClusterPixels {} minMarkerPerimeterRate {} maxMarkerPerimeterRate {} outputRollAng {}",
                 param.verbosity, param.showImg, param.only_use_perception, param.percept_time_diff_thre,
                 param.writeImg, param.Perceptual_window_ratio, param.pnp_method, param.aprilTagMinClusterPixels,
                 param.minMarkerPerimeterRate, param.maxMarkerPerimeterRate, param.outputRollAng);
        return charge_loc_alg_->SetQRCodeLocationParam(param);
    }
    else
    {
        return false;
    }
}

bool LocalizationRechargeNode::DealGetAlgParam(fescue_msgs__msg__QRCodeLocationAlgParam &data)
{
    if (charge_loc_alg_)
    {
        Recharge_config param;
        charge_loc_alg_->GetQRCodeLocationParam(param);
        data.verbosity = param.verbosity;
        data.showImg = param.showImg;
        data.only_use_perception = param.only_use_perception;
        data.percept_time_diff_thre = param.percept_time_diff_thre;
        data.writeImg = param.writeImg;
        data.Perceptual_window_ratio = param.Perceptual_window_ratio;
        data.plane_distance_threshold = param.plane_distance_threshold;
        data.pnp_method = param.pnp_method;
        data.aprilTagMinClusterPixels = param.aprilTagMinClusterPixels;
        data.minMarkerPerimeterRate = param.minMarkerPerimeterRate;
        data.maxMarkerPerimeterRate = param.maxMarkerPerimeterRate;
        data.outputRollAng = param.outputRollAng;
        data.cornerRefinementMethod = param.cornerRefinementMethod;
        data.use_bilateral_filter = param.use_bilateral_filter;
        data.use_nearby_speed_up = param.use_nearby_speed_up;                   // default: 0
        data.minCornerDistanceRate = param.minCornerDistanceRate;               // default: 0.1
        data.edgeThresholdRate = param.edgeThresholdRate;                       // default 0.1
        data.detection_area_size = param.detection_area_size;                   // default 40000
        data.nearbyMinMarkerPerimeterRate = param.nearbyMinMarkerPerimeterRate; // default 0.3
        LOG_INFO("GetQrCodeAlgoParam: verbosity {}, showImg {}, only_use_perception {} percept_time_diff_thre {} writeImg {} Perceptual_window_ratio {} pnp_method {} aprilTagMinClusterPixels {} minMarkerPerimeterRate {} maxMarkerPerimeterRate {} outputRollAng {}",
                 param.verbosity, param.showImg, param.only_use_perception, param.percept_time_diff_thre,
                 param.writeImg, param.Perceptual_window_ratio, param.pnp_method, param.aprilTagMinClusterPixels,
                 param.minMarkerPerimeterRate, param.maxMarkerPerimeterRate, param.outputRollAng);
        return true;
    }
    else
    {
        return false;
    }
}

void LocalizationRechargeNode::DealChargeDetectionResult(const fescue_msgs__msg__ChargeResult &data)
{
    if (!alg_enable_)
    {
        LOG_DEBUG("localization_recharge_node qr localization is disabled!");
        return;
    }

    if (charge_loc_alg_)
    {
        PerceptionInfo_recharge station_info;
        station_info.timestamp = data.timestamp * 1e6;
        station_info.is_recharge_mark = data.is_head;
        station_info.recharge_mark_direction = data.direction;
        station_info.width = data.width;
        station_info.height = data.height;
        station_info.confidence = data.head_box[1];
        for (int i = 0; i < 4; i++)
        {
            station_info.x1y1_x2y2[i] = data.head_box[i + 2];
        }
        charge_loc_alg_->AddChargeDetectStationInfo(station_info);
    }
}

void LocalizationRechargeNode::DealChargeLocalization(const sensor_msgs__msg__Image_iox &data)
{
    if (!alg_enable_)
    {
        LOG_DEBUG("localization_recharge_node qr localization is disabled!");
        return;
    }

    if (charge_loc_alg_)
    {
        charge_loc_alg_->DoQRCodeLocation(data);
    }
}

bool LocalizationRechargeNode::GetCameraIntrinsicsParam(Recharge_camera_intrinsic &param)
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::CameraIntrinsicRequest,
                                                      mower_msgs::srv::CameraIntrinsicResponse>>("get_union_rgb_camera_intrinsic");

    auto response_handler = [](const mower_msgs::srv::CameraIntrinsicResponse &response_receive,
                               mower_msgs::srv::CameraIntrinsicResponse &response_output) -> bool {
        memcpy(&response_output.camera_intrinsic, &response_receive.camera_intrinsic, sizeof(response_output.camera_intrinsic));
        response_output.success = response_receive.success;
        return response_output.success;
    };

    mower_msgs::srv::CameraIntrinsicRequest request_input;
    mower_msgs::srv::CameraIntrinsicResponse response_output;
    if (!client->SendRequest(request_input, response_output, nullptr, response_handler))
    {
        return false;
    }

    param.model_ = response_output.camera_intrinsic.model_;
    param.img_width_ = response_output.camera_intrinsic.img_width_;
    param.img_height_ = response_output.camera_intrinsic.img_height_;
    param.focal_x_ = response_output.camera_intrinsic.focal_x_;
    param.focal_y_ = response_output.camera_intrinsic.focal_y_;
    param.cx_ = response_output.camera_intrinsic.cx_;
    param.cy_ = response_output.camera_intrinsic.cy_;
    param.k1_ = response_output.camera_intrinsic.k1_;
    param.k2_ = response_output.camera_intrinsic.k2_;
    param.k3_ = response_output.camera_intrinsic.k3_;
    param.k4_ = response_output.camera_intrinsic.k4_;
    if (param.model_ == 0) // K6
    {
        param.k5_ = response_output.camera_intrinsic.k5_;
        param.k6_ = response_output.camera_intrinsic.k6_;
        param.p1_ = response_output.camera_intrinsic.p1_;
        param.p2_ = response_output.camera_intrinsic.p2_;
    }
    else // KB
    {
        param.k5_ = 0.0;
        param.k6_ = 0.0;
        param.p1_ = 0.0;
        param.p2_ = 0.0;
    }

    return true;
}

bool LocalizationRechargeNode::GetLocalizationAlgVersion(const std::string &alg_name)
{
    auto client = std::make_unique<IceoryxClientMower<ob_mower_srvs::GetAlgoVersionRequest,
                                                      ob_mower_srvs::GetAlgoVersionResponse>>(alg_name);
    auto response_handler = [](const ob_mower_srvs::GetAlgoVersionResponse &response_receive,
                               ob_mower_srvs::GetAlgoVersionResponse &response_output) -> bool {
        response_output = response_receive;
        return response_output.success;
    };
    ob_mower_srvs::GetAlgoVersionRequest request_input;
    ob_mower_srvs::GetAlgoVersionResponse response_output;
    if (!client->SendRequest(request_input, response_output, nullptr, response_handler))
    {
        return false;
    }
    for (size_t i = 0; i < response_output.data.version_data_vect.size(); i++)
    {
        std::string name = std::string(response_output.data.version_data_vect[i].algo_name.c_str());
        std::string version = std::string(response_output.data.version_data_vect[i].version.c_str());
        algo_version_map_[name] = version;
    }
    return true;
}

} // namespace fescue_iox
