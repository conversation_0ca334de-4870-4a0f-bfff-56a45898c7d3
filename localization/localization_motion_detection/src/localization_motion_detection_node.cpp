#include "localization_motion_detection_node.hpp"

#include "localization_motion_detection_node_config.hpp"
#include "mower_sdk_version.h"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <cmath>      // for std::labs()
#include <filesystem> //c++17

namespace fescue_iox
{
LocalizationMotionDetectionNode::LocalizationMotionDetectionNode(const std::string &node_name)
    : node_name_(node_name)
{
    InitWorkingDirectory();
    InitParam();
    InitLogger();
    InitAlg();
    InitService();
    InitSubscriber();
    InitHeartbeat();
}

LocalizationMotionDetectionNode::~LocalizationMotionDetectionNode()
{
    LOG_WARN("LocalizationMotionDetectionNode stop success!");
}

void LocalizationMotionDetectionNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void LocalizationMotionDetectionNode::InitParam()
{
    const std::string conf_file{"conf/localization_motion_detection_node/localization_motion_detection_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("localization_motion_detection_node create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("localization_motion_detection_node create config path failed!!!");
        }
    }
    if (!Config<LocalizationMotionDetectionNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init localization_motion_detection_node config parameters failed!");
    }
    LocalizationMotionDetectionNodeConfig config = Config<LocalizationMotionDetectionNodeConfig>::GetConfig();

    LOG_INFO("[localization_motion_detection_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[localization_motion_detection_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[localization_motion_detection_node] compile time: {}", _COMPILE_TIME_);
    LOG_INFO("{}", config.toString().c_str());

    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    motion_detection_conf_file_ = config.motion_detection_conf_file;

    if (!Config<LocalizationMotionDetectionNodeConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set localization_motion_detection_node config parameters failed!");
    }

    CreateDirectories(log_dir_);
}

void LocalizationMotionDetectionNode::InitLogger()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void LocalizationMotionDetectionNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

void LocalizationMotionDetectionNode::InitAlg()
{
    motion_detection_alg_ = std::make_unique<LocalizationMotionDetectionAlg>(motion_detection_conf_file_);
}

void LocalizationMotionDetectionNode::InitSubscriber()
{
    sub_rgb_image_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "camera_color_640x360_result", 1, [this](const sensor_msgs__msg__Image_iox &img, const std::string &event) {
            (void)event;
            DealMotionDetect(img);
        });
    sub_segment_result_image_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "segmenter_img_result", 1, [this](const sensor_msgs__msg__Image_iox &img, const std::string &event) {
            (void)event;
            DealSegmentResultImg(img);
        });
    sub_algo_ctrl_ = std::make_unique<IceoryxSubscriberMower<ob_mower_msgs::PerceptionLocalizationAlgCtrl>>(
        "perception_localization_alg_ctrl", 5, [this](const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data, const std::string &event) {
            (void)event;
            DealAlgCtrl(data);
        });
}

void LocalizationMotionDetectionNode::InitService()
{
    service_get_node_param_ = std::make_unique<IceoryxServerMower<get_node_param_request, get_node_param_response>>(
        "get_localization_motion_detection_node_param", 1, [this](const get_node_param_request &request, get_node_param_response &response) {
            (void)request;
            response.success = DealGetNodeParam(response.data);
            LOG_INFO("Get localization motion detection node param execute {}", response.success);
        });

    service_set_node_param_ = std::make_unique<IceoryxServerMower<set_node_param_request, set_node_param_response>>(
        "set_localization_motion_detection_node_param", 1, [this](const set_node_param_request &request, set_node_param_response &response) {
            response.success = DealSetNodeParam(request.data);
            LOG_INFO("Set localization motion detection node param execute {}", response.success);
        });

    service_get_alg_param_ = std::make_unique<IceoryxServerMower<get_alg_param_request, get_alg_param_response>>(
        "get_localization_motion_detection_param", 1, [this](const get_alg_param_request &request, get_alg_param_response &response) {
            (void)request;
            response.success = DealGetAlgParam(response.data);
            response.timestamp_ms = GetTimestampMs();
            LOG_INFO("Get localization motion detection param execute {}", response.success);
        });

    service_set_alg_param_ = std::make_unique<IceoryxServerMower<set_alg_param_request, set_alg_param_response>>(
        "set_localization_motion_detection_param", 1, [this](const set_alg_param_request &request, set_alg_param_response &response) {
            response.success = DealSetAlgParam(request.data);
            response.timestamp_ms = GetTimestampMs();
            LOG_INFO("Set localization motion detection param execute {}", response.success);
        });

    service_get_algo_version_ = std::make_unique<IceoryxServerMower<get_alg_version_request, get_alg_version_response>>(
        "get_localization_motion_detection_algo_version", 1, [this](const get_alg_version_request &request, get_alg_version_response &response) {
            (void)request;
            response.success = DealGetAlgVersion(response.data);
            response.timestamp = GetTimestampMs();
            LOG_INFO("Get localization motion detection algo version execute {}", response.success);
        });
}

bool LocalizationMotionDetectionNode::DealGetNodeParam(ob_mower_srvs::NodeParamData &data)
{
    LocalizationMotionDetectionNodeConfig config = Config<LocalizationMotionDetectionNodeConfig>::GetConfig();
    data.console_log_level.unsafe_assign(config.common_conf.console_log_level.c_str());
    data.file_log_level.unsafe_assign(config.common_conf.file_log_level.c_str());
    return true;
}

bool LocalizationMotionDetectionNode::DealSetNodeParam(const ob_mower_srvs::NodeParamData &data)
{
    console_log_level_ = std::string(data.console_log_level.c_str());
    file_log_level_ = std::string(data.file_log_level.c_str());
    InitLogger();
    LocalizationMotionDetectionNodeConfig config = Config<LocalizationMotionDetectionNodeConfig>::GetConfig();
    config.common_conf.console_log_level = console_log_level_;
    config.common_conf.file_log_level = file_log_level_;
    Config<LocalizationMotionDetectionNodeConfig>::SetConfig(config);
    LOG_INFO("New LocalizationMotionDetectionNodeConfig params: {}", config.toString().c_str());
    return true;
}

bool LocalizationMotionDetectionNode::DealGetAlgVersion(ob_mower_srvs::AlgoVersionDataVect &data)
{
    if (motion_detection_alg_)
    {
        ob_mower_srvs::AlgoVersionData alg_version_data;
        alg_version_data.algo_name.unsafe_assign("localization_motion_detection");
        alg_version_data.version.unsafe_assign(motion_detection_alg_->GetAlgVersion());
        data.version_data_vect.push_back(alg_version_data);
    }
    return true;
}

bool LocalizationMotionDetectionNode::DealGetAlgParam(ob_mower_srvs::LocMotionDetectAlgParam &param)
{
    if (!motion_detection_alg_)
    {
        return false;
    }

    Motion_detection_config config;
    motion_detection_alg_->GetAlgParam(config);
    param.verbosity = config.verbosity;
    param.showImg = config.showImg;
    param.frameSkip = config.frameSkip;
    param.maxCorners = config.maxCorners;
    param.minCorners = config.minCorners;
    param.qualityLevel = config.qualityLevel;
    param.minDistance = config.minDistance;
    param.blockSize = config.blockSize;
    param.useHarrisDetector = config.useHarrisDetector;
    param.Harris_k = config.Harris_k;
    param.maxLevel = config.maxLevel;
    param.winSize = config.winSize;
    param.down_sample = config.down_sample;
    param.minDisplacement = config.minDisplacement;
    return true;
}

bool LocalizationMotionDetectionNode::DealSetAlgParam(const ob_mower_srvs::LocMotionDetectAlgParam &param)
{
    if (!motion_detection_alg_)
    {
        return false;
    }

    Motion_detection_config config;
    config.verbosity = param.verbosity;
    config.showImg = param.showImg;
    config.frameSkip = param.frameSkip;
    config.maxCorners = param.maxCorners;
    config.minCorners = param.minCorners;
    config.qualityLevel = param.qualityLevel;
    config.minDistance = param.minDistance;
    config.blockSize = param.blockSize;
    config.useHarrisDetector = param.useHarrisDetector;
    config.Harris_k = param.Harris_k;
    config.maxLevel = param.maxLevel;
    config.winSize = param.winSize;
    config.down_sample = param.down_sample;
    config.minDisplacement = param.minDisplacement;
    return motion_detection_alg_->SetAlgParam(config);
}

void LocalizationMotionDetectionNode::DealMotionDetect(const sensor_msgs__msg__Image_iox &img)
{
    if (!motion_detection_enable_)
    {
        LOG_DEBUG("Motion detection alg is disabled!");
        return;
    }
    if (motion_detection_alg_)
    {
        motion_detection_alg_->DoMotionDetection(img);
    }
}

void LocalizationMotionDetectionNode::DealSegmentResultImg(const sensor_msgs__msg__Image_iox &img)
{
    if (!motion_detection_enable_)
    {
        LOG_DEBUG("Motion detection alg is disabled!");
        return;
    }

    if (motion_detection_alg_)
    {
        motion_detection_alg_->DoSegmentResultImg(img);
    }
}

void LocalizationMotionDetectionNode::DealAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data)
{
    using namespace ob_mower_msgs;
    for (size_t i = 0; i < data.ctrl_list.size(); i++)
    {
        if (data.ctrl_list[i].alg_type == PerceptionLocalizationAlgType::LOCALIZATION_MOTION_DETECTION)
        {
            motion_detection_enable_ = (data.ctrl_list[i].alg_state == PerceptionLocalizationAlgState::ENABLE ? true : false);
            LOG_WARN("{} {}", asStringLiteral(data.ctrl_list[i].alg_type).c_str(), asStringLiteral(data.ctrl_list[i].alg_state).c_str());
            break;
        }
    }
}

} // namespace fescue_iox
