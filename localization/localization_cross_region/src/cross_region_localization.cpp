#include "cross_region_localization.hpp"

#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"

using namespace mower_msgs::msg;

namespace fescue_iox
{

// 静态成员变量初始化
std::mutex MarkLocation::result_mtx_;
std::condition_variable MarkLocation::result_cv_;
ob_mark_location_result MarkLocation::mark_loc_result_;

std::mutex MarkLocation::img_mtx_;
MarkLocationImage MarkLocation::mark_loc_img_;

void MarkLocation::MarkLocationResultCallback(const ob_mark_location_result *result)
{
    LOG_DEBUG("MarkLocationResult: err_code: {} {} {} ({:.3f} {:.3f} {:.3f}) ({:.3f} {:.3f} {:.3f})",
              result->ERROR_CODE,
              result->timestamp / 1000, result->markID,
              result->pos_x, result->pos_y, result->pos_z,
              result->roll, result->pitch, result->yaw);
    std::lock_guard<std::mutex> lock(result_mtx_);
    // memcpy((void *)&mark_loc_result_, result, sizeof(ob_mark_location_result));
    mark_loc_result_ = *result;
    result_cv_.notify_one();
}

void MarkLocation::MarkLocationImgCallback(const uint64_t timestamp, const uint32_t width, const uint32_t height,
                                           uint8_t *data, const uint64_t size)
{
    (void)size;
    img_mtx_.lock();
    mark_loc_img_.sec = timestamp / 1000;
    mark_loc_img_.nanosec = (timestamp % 1000) * 1000 * 1000;
    mark_loc_img_.frame_id = "mark_location";
    mark_loc_img_.width = width / 2;
    mark_loc_img_.height = height / 2;
    mark_loc_img_.encoding = "bgr8";
    cv::Mat img(height, width, CV_8UC3, data);
    cv::resize(img, mark_loc_img_.img, cv::Size(mark_loc_img_.width, mark_loc_img_.height));
    img_mtx_.unlock();
}

MarkLocation::MarkLocation(const std::string &conf_file, Cross_region_camera_intrinsic &intrinsic_param, bool result)
    : conf_file_(conf_file)
{
    InitPublisher();
    InitAlg(intrinsic_param, result);
}

MarkLocation::~MarkLocation()
{
    LOG_WARN("MarkLocation start stop!");
    thread_running_.store(false);
    if (pub_result_thread_.joinable())
    {
        pub_result_thread_.join();
    }
    result_cv_.notify_all();
    OML_stop(mark_location_handle_);
    OML_release(mark_location_handle_);
    LOG_WARN("MarkLocation stop success!");
}

void MarkLocation::ShowCameraIntrinsicParam(const Cross_region_camera_intrinsic &param)
{
    LOG_INFO("----------- MarkLocation Camera intrinsics param ----------------");
    LOG_INFO("  model_: {}", param.model_);
    LOG_INFO("  img_width_: {}", param.img_width_);
    LOG_INFO("  img_height_: {}", param.img_height_);
    LOG_INFO("  focal_x_: {:.6f}", param.focal_x_);
    LOG_INFO("  focal_y_: {:.6f}", param.focal_y_);
    LOG_INFO("  cx_: {:.6f}", param.cx_);
    LOG_INFO("  cy_: {:.6f}", param.cy_);
    LOG_INFO("  k1_: {:.6f}", param.k1_);
    LOG_INFO("  k2_: {:.6f}", param.k2_);
    LOG_INFO("  k3_: {:.6f}", param.k3_);
    LOG_INFO("  k4_: {:.6f}", param.k4_);
    LOG_INFO("  k5_: {:.6f}", param.k5_);
    LOG_INFO("  k6_: {:.6f}", param.k6_);
    LOG_INFO("  p1_: {:.6f}", param.p1_);
    LOG_INFO("  p2_: {:.6f}", param.p2_);
    LOG_INFO("----------- MarkLocation Camera intrinsics param ----------------");
}

bool MarkLocation::InitAlg(Cross_region_camera_intrinsic &intrinsic_param, bool intrinsic_param_valid)
{
    if (!intrinsic_param_valid)
    {
        SocExceptionValue error_code = SocExceptionValue::ALG_CROSS_REGION_LOCALIZATION_GET_INTRINSIC_PARAM_ERROR_EXCEPTION;
        LOG_ERROR("Localization cross region algorithm get intrinsic param fail, error code: {:X}", static_cast<uint16_t>(error_code));
        PublishException(SocExceptionLevel::ERROR, error_code);
        mark_location_handle_ = nullptr;
        return false;
    }

    InitAlgParam();
    MarkLocationAlgConfig config = Config<MarkLocationAlgConfig>::GetConfig();
    int result = OML_create(&mark_location_handle_, config.inner_config_path.c_str());
    if (result != 0)
    {
        LOG_ERROR("Localization cross region algorithm initialization fail, error code: {:X}", result);
        PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                         mower_msgs::msg::SocExceptionValue::ALG_CROSS_REGION_LOCALIZATION_INIT_EXCEPTION);
        return false;
    }

    Cross_region_config param;
    ConfigParam2AlgParam(config, param);
    if (intrinsic_param_valid)
    {
        LOG_WARN("************ Perception cross region localization using camera intrinsic parameter **********************");
        ShowCameraIntrinsicParam(intrinsic_param);
        OML_set_camera_intrinsic_parameter(mark_location_handle_, &intrinsic_param);
    }
    else
    {
        LOG_WARN("************ Perception cross region localization using config file intrinsic parameter **********************");
    }
    OML_start(mark_location_handle_);
    LOG_INFO("************** bucketID {} **********************", param.bucketID);
    OML_set_config_parameter(mark_location_handle_, &param);
    OML_register_result_callback(mark_location_handle_, &MarkLocation::MarkLocationResultCallback);
    OML_register_image_show_callback(mark_location_handle_, &MarkLocation::MarkLocationImgCallback);
    LOG_INFO("ob_mower_mark_location alg version: {}", OML_get_version(mark_location_handle_));
    pub_result_thread_ = std::thread(&MarkLocation::PublishMarkLocationResultThread, this);
    return true;
}

void MarkLocation::InitAlgParam()
{
    std::string conf_path = GetDirectoryPath(conf_file_);
    if (!conf_path.empty())
    {
        LOG_INFO("MarkLocation create alg config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("MarkLocation create alg config path failed!!!");
        }
    }
    if (!Config<MarkLocationAlgConfig>::Init(conf_file_))
    {
        LOG_WARN("Init MarkLocation config parameters failed!");
    }
    MarkLocationAlgConfig config = Config<MarkLocationAlgConfig>::GetConfig();
    LOG_INFO("{}", config.toString().c_str());
    if (!Config<MarkLocationAlgConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set MarkLocation config parameters failed!");
    }
}

void MarkLocation::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;

    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    /** 二维码定位算法debug可视化结果 */
    pub_mark_loc_img_ = std::make_unique<iox_image_publisher>(
        iox::capro::ServiceDescription{kLocationMarkLocationImageIox[0],
                                       kLocationMarkLocationImageIox[1],
                                       kLocationMarkLocationImageIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);

    /** 二维码定位算法结果 */
    pub_mark_loc_result_ = std::make_unique<iox_mark_location_result_publisher>(
        iox::capro::ServiceDescription{kLocationMarkLocationResultIox[0],
                                       kLocationMarkLocationResultIox[1],
                                       kLocationMarkLocationResultIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);

    pub_exception_ = std::make_unique<IceoryxPublisherMower<mower_msgs::msg::SocException>>("soc_exception");
}

bool MarkLocation::DoMarkLocation(const sensor_msgs__msg__Image_iox &image)
{
    PreMarkLocation(image);
    OML_add_image(mark_location_handle_, timestamp_ms_, img_width_, img_height_, img_data_, img_size_);
    PublishMarkLocationImage();
    return true;
}

void MarkLocation::AddPerceptionResult(const PerceptionInfo &info)
{
    uint64_t timestamp_ms = info.timestamp / 1e6;
    OML_add_perception(mark_location_handle_, timestamp_ms, (PerceptionInfo *)&info);
}

bool MarkLocation::SetDetectMarkId(uint32_t mark_id)
{
    LOG_INFO("Set mark location detect mark id: {}", mark_id);
    OML_set_bucket_id(mark_location_handle_, mark_id);
    return true;
}

bool MarkLocation::SetMarkLocationAlgoParam(Cross_region_config &param)
{
    LOG_INFO("************** SetMarkLocationAlgoParam bucketID {} **********************", param.bucketID);
    OML_set_config_parameter(mark_location_handle_, &param);
    LOG_INFO("Set SetMarkLocation alg params success!");
    MarkLocationAlgConfig config = Config<MarkLocationAlgConfig>::GetConfig();
    AlgParam2ConfigParam(param, config);
    Config<MarkLocationAlgConfig>::SetConfig(config);
    LOG_INFO("New SetMarkLocation alg params: {}", config.toString().c_str());
    return true;
}

bool MarkLocation::GetMarkLocationAlgoParam(Cross_region_config &param)
{
    OML_get_config_parameter(mark_location_handle_, &param);
    LOG_INFO("************** GetMarkLocationAlgoParam bucketID {} **********************", param.bucketID);
    return true;
}

const char *MarkLocation::GetMarkLocationAlgoVersion()
{
    return OML_get_version(mark_location_handle_);
}

void MarkLocation::PreMarkLocation(const sensor_msgs__msg__Image_iox &image)
{
    sec_ = image.header.stamp.sec;
    nanosec_ = image.header.stamp.nanosec;
    timestamp_ms_ = sec_ * 1000 + nanosec_ / 1000000;
    frame_id_ = std::string(image.header.frame_id.c_str());
    encoding_ = std::string(image.encoding.c_str());

    NV12ToMat(bgr_img_, image.data.data(), image.width, image.height);

    img_width_ = bgr_img_.cols;
    img_height_ = bgr_img_.rows;
    img_data_ = bgr_img_.data;
    img_size_ = bgr_img_.channels();
}

void MarkLocation::PublishMarkLocationResult()
{
    if (mark_loc_result_.ERROR_CODE == CROSS_INIT_FAIL ||
        mark_loc_result_.ERROR_CODE == CROSS_SET_CAM_FAIL ||
        mark_loc_result_.ERROR_CODE == CROSS_SET_DICT_FAIL)
    {
        LOG_ERROR("Cross region localization algorithm execute failed, error code: {:X}", mark_loc_result_.ERROR_CODE);
        PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                         mower_msgs::msg::SocExceptionValue::ALG_CROSS_REGION_LOCALIZATION_EXECUTE_ERROR_EXCEPTION);
    }

    if (pub_mark_loc_result_->hasSubscribers())
    {
        auto loan = pub_mark_loc_result_->loan();
        if (!loan.has_error())
        {
            auto &msg = loan.value();
            // ns -> ms
            uint64_t timestamp_ms = mark_loc_result_.timestamp / 1e6;
            msg->header.stamp.sec = timestamp_ms / 1000; // ms->s
            msg->header.stamp.nanosec = (timestamp_ms % 1000) * 1000 * 1000;
            msg->timestamp_ms = timestamp_ms;
            msg->mark_perception_status = mark_loc_result_.mark_perception_status;
            msg->mark_perception_direction = mark_loc_result_.mark_perception_direction;
            msg->detect_status = static_cast<fescue_msgs__enum__DetectStatus>(mark_loc_result_.detect_status);
            int mark_dis_size = std::min(static_cast<int>(mark_loc_result_.v_markID_dis.size()), IOX_MAX_MARK_NUM);
            for (int i = 0; i < mark_dis_size; i++)
            {
                fescue_msgs__msg__MarkIdDistance mark_id_dis;
                mark_id_dis.id = mark_loc_result_.v_markID_dis[i].first;
                mark_id_dis.distance = mark_loc_result_.v_markID_dis[i].second;
                msg->mark_id_dis.push_back(mark_id_dis);
            }
            msg->roi_confidence = mark_loc_result_.roi_confidence;
            msg->target_direction = mark_loc_result_.target_direction;
            msg->mark_id = mark_loc_result_.markID;
            msg->pose.position.x = mark_loc_result_.pos_x;
            msg->pose.position.y = mark_loc_result_.pos_y;
            msg->pose.position.z = mark_loc_result_.pos_z;
            msg->pose.orientation.x = mark_loc_result_.quaternion_x;
            msg->pose.orientation.y = mark_loc_result_.quaternion_y;
            msg->pose.orientation.z = mark_loc_result_.quaternion_z;
            msg->pose.orientation.w = mark_loc_result_.quaternion_w;
            msg->roll = mark_loc_result_.roll;
            msg->pitch = mark_loc_result_.pitch;
            msg->yaw = mark_loc_result_.yaw;
            msg.publish();
        }
    }
}

void MarkLocation::PublishMarkLocationImage()
{
    MarkLocationImage image;
    img_mtx_.lock();
    image = mark_loc_img_;
    mark_loc_img_.img.release();
    img_mtx_.unlock();

    if (!image.img.empty() && pub_mark_loc_img_->hasSubscribers())
    {
        auto loan = pub_mark_loc_img_->loan();
        if (!loan.has_error())
        {
            auto &msg = loan.value();
            msg->header.stamp.sec = image.sec;
            msg->header.stamp.nanosec = image.nanosec;
            msg->header.frame_id.unsafe_assign(image.frame_id.c_str());
            msg->width = image.width;
            msg->height = image.height;
            msg->is_bigendian = false;
            msg->encoding.unsafe_assign(image.encoding.c_str());
            msg->step = msg->width * image.img.channels();
            size_t img_size = (msg->step * msg->height > IOX_IMAGE_DATA_MAX)
                                  ? IOX_IMAGE_DATA_MAX
                                  : (msg->step * msg->height);
            msg->data.resize(img_size);
            memcpy(msg->data.data(), image.img.data, img_size);
            msg.publish();
        }
    }
}

void MarkLocation::PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.timestamp = GetTimestampMs();
        exception.node_name = "localization_cross_region_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception);
    }
}

/**
 * @brief 发送定位结果线程
 */
void MarkLocation::PublishMarkLocationResultThread()
{
    LOG_INFO("PublishMarkLocationResultThread is running!");

    while (thread_running_.load())
    {
        std::unique_lock<std::mutex> lock(result_mtx_);
        result_cv_.wait(lock);
        if (!thread_running_)
        {
            break;
        }
        PublishMarkLocationResult();
    }

    LOG_INFO("PublishMarkLocationResultThread is stoped!");
}

void MarkLocation::ConfigParam2AlgParam(const MarkLocationAlgConfig &config, Cross_region_config &param)
{
    param.verbosity = config.verbosity;
    param.showImg = config.showImg;
    param.only_use_perception = config.only_use_perception;
    param.percept_time_diff_thre = config.percept_time_diff_thre;
    param.writeImg = config.writeImg;
    param.Perceptual_window_ratio = config.Perceptual_window_ratio;

    param.pnp_method = config.pnp_method;
    param.aprilTagMinClusterPixels = config.aprilTagMinClusterPixels;
    param.minMarkerPerimeterRate = config.minMarkerPerimeterRate;
    param.maxMarkerPerimeterRate = config.maxMarkerPerimeterRate;
    param.outputRollAng = config.outputRollAng;                   // default: 0.1
    param.cornerRefinementMethod = config.cornerRefinementMethod; // default 0, range: 0~3
    param.use_bilateral_filter = config.use_bilateral_filter;     // default: false

    param.use_nearby_speed_up = config.use_nearby_speed_up;
    param.minCornerDistanceRate = config.minCornerDistanceRate;
    param.edgeThresholdRate = config.edgeThresholdRate;
    param.detection_area_size = config.detection_area_size;
    param.nearbyMinMarkerPerimeterRate = config.nearbyMinMarkerPerimeterRate;

    param.markModel = config.markModel;
    param.bucketID = config.bucketID;
    param.evaluation = config.evaluation;
    param.outputFrameOffset_x = config.outputFrameOffset_x;
    param.outputFrameOffset_y = config.outputFrameOffset_y;
    param.outputFrameOffset_z = config.outputFrameOffset_z;
    param.outputFrame_theta = config.outputFrame_theta;
    param.outputDisThre = config.outputDisThre;
}

void MarkLocation::AlgParam2ConfigParam(const Cross_region_config &param, MarkLocationAlgConfig &config)
{
    config.verbosity = param.verbosity;
    config.showImg = param.showImg;
    config.only_use_perception = param.only_use_perception;
    config.percept_time_diff_thre = param.percept_time_diff_thre;
    config.writeImg = param.writeImg;
    config.Perceptual_window_ratio = param.Perceptual_window_ratio;

    config.pnp_method = param.pnp_method;
    config.aprilTagMinClusterPixels = param.aprilTagMinClusterPixels;
    config.minMarkerPerimeterRate = param.minMarkerPerimeterRate;
    config.maxMarkerPerimeterRate = param.maxMarkerPerimeterRate;
    config.outputRollAng = param.outputRollAng;                   // default: 0.1
    config.cornerRefinementMethod = param.cornerRefinementMethod; // default 0, range: 0~3
    config.use_bilateral_filter = param.use_bilateral_filter;     // default: false

    config.use_nearby_speed_up = param.use_nearby_speed_up;
    config.minCornerDistanceRate = param.minCornerDistanceRate;
    config.edgeThresholdRate = param.edgeThresholdRate;
    config.detection_area_size = param.detection_area_size;
    config.nearbyMinMarkerPerimeterRate = param.nearbyMinMarkerPerimeterRate;

    config.markModel = param.markModel;
    config.bucketID = param.bucketID;
    config.evaluation = param.evaluation;
    config.outputFrameOffset_x = param.outputFrameOffset_x;
    config.outputFrameOffset_y = param.outputFrameOffset_y;
    config.outputFrameOffset_z = param.outputFrameOffset_z;
    config.outputFrame_theta = param.outputFrame_theta;
    config.outputDisThre = param.outputDisThre;
}

} // namespace fescue_iox
