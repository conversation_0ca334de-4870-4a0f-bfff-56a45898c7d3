import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.collections import LineCollection
from matplotlib.colors import LinearSegmentedColormap
import os

# 读取CSV文件
home_dir = os.path.expanduser("~")
df = pd.read_csv(os.path.join(home_dir, "world_pose.csv"))

# 提取位置和朝向数据
x = df['world_x'].values
y = df['world_y'].values
theta = df['world_yaw'].values

# 创建颜色映射
points = np.array([x, y]).T.reshape(-1, 1, 2)
segments = np.concatenate([points[:-1], points[1:]], axis=1)
n_segments = len(segments)

# 创建从蓝色到红色的渐变色映射
colors = plt.cm.viridis(np.linspace(0, 1, n_segments))

# 创建图形
plt.figure(figsize=(10, 10))
ax = plt.gca()

# 绘制轨迹（带颜色渐变）
lc = LineCollection(segments, colors=colors, linewidth=2)
ax.add_collection(lc)

# 添加箭头表示朝向（每隔一定数量的点添加一个箭头）
arrow_interval = max(n_segments // 20, 1)  # 控制箭头数量，至少为1
for i in range(0, n_segments, arrow_interval):
    # 计算箭头长度（可以根据需要调整）
    arrow_length = 0.2
    dx = arrow_length * np.cos(theta[i])
    dy = arrow_length * np.sin(theta[i])
    
    # 绘制箭头
    ax.arrow(x[i], y[i], dx, dy,
             head_width=0.1, head_length=0.1, fc=colors[i], ec=colors[i],
             alpha=0.7)

# 设置坐标轴范围
margin = 1.0  # 边距
ax.set_xlim(min(x) - margin, max(x) + margin)
ax.set_ylim(min(y) - margin, max(y) + margin)

# 设置坐标轴标签
ax.set_xlabel('X (m)')
ax.set_ylabel('Y (m)')
ax.set_title('Robot Trajectory with Orientation')

# 保持纵横比相等
ax.set_aspect('equal')

# 添加网格
ax.grid(True)

# 保存图片
plt.savefig(os.path.join(home_dir, "trajectory.png"), dpi=300, bbox_inches='tight')
plt.close()

print("Trajectory plot has been saved to " + os.path.join(home_dir, "trajectory.png")) 