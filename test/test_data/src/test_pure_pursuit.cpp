#include "pure_pursuit_tracker.hpp"
#include "utils/logger.hpp"
#include "process_fusion.hpp"

using namespace fescue_iox;

void TestForward() {
    std::vector<TrajectoryPose> traj;
    TrajectoryPose traj_pose;

    traj_pose.x = 0;
    traj_pose.y = 0;
    traj_pose.theta = 0;
    traj_pose.linear_velocity = 0.2;
    traj.push_back(traj_pose);

    traj_pose.x = 0.05;
    traj_pose.y = 0.01;
    traj_pose.theta = 0.1;
    traj_pose.linear_velocity = 0.2;
    traj.push_back(traj_pose);

    traj_pose.x = 0.1;
    traj_pose.y = 0.02;
    traj_pose.theta = 0.2;
    traj_pose.linear_velocity = 0.2;
    traj.push_back(traj_pose);

    traj_pose.x = 0.15;
    traj_pose.y = 0.05;
    traj_pose.theta = 0.25;
    traj_pose.linear_velocity = 0.2;
    traj.push_back(traj_pose);

    traj_pose.x = 0.2;
    traj_pose.y = 0.07;
    traj_pose.theta = 0.3;
    traj_pose.linear_velocity = 0.2;
    traj.push_back(traj_pose);

    traj_pose.x = 0.25;
    traj_pose.y = 0.1;
    traj_pose.theta = 0.35;
    traj_pose.linear_velocity = 0.2;
    traj.push_back(traj_pose);

    traj_pose.x = 0.3;
    traj_pose.y = 0.15;
    traj_pose.theta = 0.4;
    traj_pose.linear_velocity = 0.2;
    traj.push_back(traj_pose);

    traj_pose.x = 0.35;
    traj_pose.y = 0.17;
    traj_pose.theta = 0.45;
    traj_pose.linear_velocity = 0.2;
    traj.push_back(traj_pose);

    traj_pose.x = 0.4;
    traj_pose.y = 0.2;
    traj_pose.theta = 0.5;
    traj_pose.linear_velocity = 0.2;
    traj.push_back(traj_pose);

    PurePursuitConfig config;
    PurePursuitTracker tracker(traj, config);
    OdomResult odom_result;
    TrajectoryPose pose;

    pose.x = 0.05;
    pose.y = 0.07;
    pose.theta = 0;
    tracker.Update(pose, odom_result);
    auto result = tracker.GetResult();
    LOG_INFO("Linear velocity: {} Angular velocity: {} result: {}", tracker.GetLinearVelocity(), tracker.GetAngularVelocity(), fescue_iox::TrackerResultString(result));

    pose.x = 0.1;
    pose.y = 0.01;
    pose.theta = 0.15;
    tracker.Update(pose, odom_result);
    result = tracker.GetResult();
    LOG_INFO("Linear velocity: {} Angular velocity: {} result: {}", tracker.GetLinearVelocity(), tracker.GetAngularVelocity(), fescue_iox::TrackerResultString(result));

    pose.x = 0.15;
    pose.y = 0.04;
    pose.theta = 0.2;
    tracker.Update(pose, odom_result);
    result = tracker.GetResult();
    LOG_INFO("Linear velocity: {} Angular velocity: {} result: {}", tracker.GetLinearVelocity(), tracker.GetAngularVelocity(), fescue_iox::TrackerResultString(result));

    pose.x = 0.35;
    pose.y = 0.15;
    pose.theta = 0.4;
    tracker.Update(pose, odom_result);
    result = tracker.GetResult();
    LOG_INFO("Linear velocity: {} Angular velocity: {} result: {}", tracker.GetLinearVelocity(), tracker.GetAngularVelocity(), fescue_iox::TrackerResultString(result));

    pose.x = 0.39;
    pose.y = 0.2;
    pose.theta = 0.47;
    tracker.Update(pose, odom_result);
    result = tracker.GetResult();
    LOG_INFO("Linear velocity: {} Angular velocity: {} result: {}", tracker.GetLinearVelocity(), tracker.GetAngularVelocity(), fescue_iox::TrackerResultString(result));

    pose.x = 0.42;
    pose.y = 0.21;
    pose.theta = 0.5;
    tracker.Update(pose, odom_result);
    result = tracker.GetResult();
    LOG_INFO("Linear velocity: {} Angular velocity: {} result: {}", tracker.GetLinearVelocity(), tracker.GetAngularVelocity(), fescue_iox::TrackerResultString(result));
}

void TestForward2() {
    std::vector<TrajectoryPose> traj;
    TrajectoryPose traj_pose;

    for (double x = 0; x < 0.4 + 1e-6; x += 0.02) {
        traj_pose.x = x;
        traj_pose.y = 0;
        traj_pose.theta = 0;
        traj_pose.linear_velocity = 0.2;
        traj.push_back(traj_pose);
    }

    PurePursuitConfig config;
    PurePursuitTracker tracker(traj, config);
    OdomResult odom_result;
    TrajectoryPose pose;

    pose.x = 0.05;
    pose.y = 0.07;
    pose.theta = 0;
    tracker.Update(pose, odom_result);
    auto result = tracker.GetResult();
    LOG_INFO("Linear velocity: {} Angular velocity: {} result: {}", tracker.GetLinearVelocity(), tracker.GetAngularVelocity(), fescue_iox::TrackerResultString(result));

    pose.x = 0.1;
    pose.y = 0.01;
    pose.theta = 0;
    tracker.Update(pose, odom_result);
    result = tracker.GetResult();
    LOG_INFO("Linear velocity: {} Angular velocity: {} result: {}", tracker.GetLinearVelocity(), tracker.GetAngularVelocity(), fescue_iox::TrackerResultString(result));

    pose.x = 0.15;
    pose.y = -0.04;
    pose.theta = 0;
    tracker.Update(pose, odom_result);
    result = tracker.GetResult();
    LOG_INFO("Linear velocity: {} Angular velocity: {} result: {}", tracker.GetLinearVelocity(), tracker.GetAngularVelocity(), fescue_iox::TrackerResultString(result));

    pose.x = 0.35;
    pose.y = -0.05;
    pose.theta = 0;
    tracker.Update(pose, odom_result);
    result = tracker.GetResult();
    LOG_INFO("Linear velocity: {} Angular velocity: {} result: {}", tracker.GetLinearVelocity(), tracker.GetAngularVelocity(), fescue_iox::TrackerResultString(result));

    pose.x = 0.39;
    pose.y = -0.01;
    pose.theta = 0;
    tracker.Update(pose, odom_result);
    result = tracker.GetResult();
    LOG_INFO("Linear velocity: {} Angular velocity: {} result: {}", tracker.GetLinearVelocity(), tracker.GetAngularVelocity(), fescue_iox::TrackerResultString(result));

    pose.x = 0.42;
    pose.y = 0.03;
    pose.theta = 0;
    tracker.Update(pose, odom_result);
    result = tracker.GetResult();
    LOG_INFO("Linear velocity: {} Angular velocity: {} result: {}", tracker.GetLinearVelocity(), tracker.GetAngularVelocity(), fescue_iox::TrackerResultString(result));
}

void TestBackForward() {
    std::vector<TrajectoryPose> traj;
    TrajectoryPose traj_pose;

    for (double x = 0; x < 0.4 + 1e-6; x += 0.02) {
        traj_pose.x = -x;
        traj_pose.y = 0;
        traj_pose.theta = 0;
        traj_pose.linear_velocity = -0.2;
        traj.push_back(traj_pose);
    }

    PurePursuitConfig config;
    PurePursuitTracker tracker(traj, config);
    OdomResult odom_result;
    TrajectoryPose pose;

    pose.x = -0.05;
    pose.y = 0.07;
    pose.theta = 0;
    tracker.Update(pose, odom_result);
    auto result = tracker.GetResult();
    LOG_INFO("Linear velocity: {} Angular velocity: {} result: {}", tracker.GetLinearVelocity(), tracker.GetAngularVelocity(), fescue_iox::TrackerResultString(result));

    pose.x = -0.1;
    pose.y = 0.01;
    pose.theta = 0;
    tracker.Update(pose, odom_result);
    result = tracker.GetResult();
    LOG_INFO("Linear velocity: {} Angular velocity: {} result: {}", tracker.GetLinearVelocity(), tracker.GetAngularVelocity(), fescue_iox::TrackerResultString(result));

    pose.x = -0.15;
    pose.y = -0.04;
    pose.theta = 0;
    tracker.Update(pose, odom_result);
    result = tracker.GetResult();
    LOG_INFO("Linear velocity: {} Angular velocity: {} result: {}", tracker.GetLinearVelocity(), tracker.GetAngularVelocity(), fescue_iox::TrackerResultString(result));

    pose.x = -0.35;
    pose.y = -0.05;
    pose.theta = 0;
    tracker.Update(pose, odom_result);
    result = tracker.GetResult();
    LOG_INFO("Linear velocity: {} Angular velocity: {} result: {}", tracker.GetLinearVelocity(), tracker.GetAngularVelocity(), fescue_iox::TrackerResultString(result));

    pose.x = -0.39;
    pose.y = -0.01;
    pose.theta = 0;
    tracker.Update(pose, odom_result);
    result = tracker.GetResult();
    LOG_INFO("Linear velocity: {} Angular velocity: {} result: {}", tracker.GetLinearVelocity(), tracker.GetAngularVelocity(), fescue_iox::TrackerResultString(result));

    pose.x = -0.42;
    pose.y = 0.03;
    pose.theta = 0;
    tracker.Update(pose, odom_result);
    result = tracker.GetResult();
    LOG_INFO("Linear velocity: {} Angular velocity: {} result: {}", tracker.GetLinearVelocity(), tracker.GetAngularVelocity(), fescue_iox::TrackerResultString(result));
}

void TestSDFMap() {
    OccupancyResult occupancy_result;
    occupancy_result.width = 100;
    occupancy_result.height = 100;
    occupancy_result.resolution = 0.01;
    for (int i = 0; i < occupancy_result.height; i++) {
        occupancy_result.grid.push_back(std::vector<uint8_t>());
        for (int j = 0; j < occupancy_result.width; j++) {
            if (i == 0 && j == 0) {
                occupancy_result.grid[i].push_back(1);
            } else {
                occupancy_result.grid[i].push_back(0);
            }
        }
    }
    Pose2f cur_pose(1.0, 0, 0);
    auto sdf_map = GetSDFMap(occupancy_result, cur_pose);
    Point2f point(2.05, 0.5);
    auto grid = sdf_map->ConvertToGrid(point);
    double dist = sdf_map->GetPreciseDist(grid.x, grid.y);
    LOG_INFO("point x: {}, y: {} grid x: {}, y: {} inside: {} resolution: {} dist: {}", point.x, point.y, grid.x, grid.y, sdf_map->IsInside(grid), sdf_map->GetResolution(), dist);

    point.x = 2.0;
    point.y = 0.5;
    grid = sdf_map->ConvertToGrid(point);
    dist = sdf_map->GetPreciseDist(grid.x, grid.y);
    LOG_INFO("point x: {}, y: {} grid x: {}, y: {} inside: {} resolution: {} dist: {}", point.x, point.y, grid.x, grid.y, sdf_map->IsInside(grid), sdf_map->GetResolution(), dist);

    point.x = 2.3;
    point.y = 1.1;
    grid = sdf_map->ConvertToGrid(point);
    dist = sdf_map->GetPreciseDist(grid.x, grid.y);
    LOG_INFO("point x: {}, y: {} grid x: {}, y: {} inside: {} resolution: {} dist: {}", point.x, point.y, grid.x, grid.y, sdf_map->IsInside(grid), sdf_map->GetResolution(), dist);
}

int main(int argc, char** argv) {
    TestSDFMap();
    return 0;
}
