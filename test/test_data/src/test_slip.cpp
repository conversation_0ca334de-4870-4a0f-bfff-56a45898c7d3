#include <iostream>
#include <vector>
#include <fstream>
#include <string>
#include <cmath>
#include <cstdlib>

#include "Eigen/Dense"
#include "json.hpp"
#include "utils/imu_filter.hpp"
#include "utils/slip_detector.hpp"
#include "utils/time.hpp"

using namespace Eigen;

std::pair<double, double> GetRobotSpeed(double motor_speed_left, double motor_speed_right) {
    double wheel_radius = 0.1f;
    double wheel_base = 0.335f;
    float w_left = motor_speed_left * 2 * M_PI / 60.0f;
    float w_right = motor_speed_right * 2 * M_PI / 60.0f;
    float v_left = w_left * wheel_radius;
    float v_right = w_right * wheel_radius;
    float theoretical_linear = (v_right + v_left) / 2.0f;
    float theoretical_angular = (v_right - v_left) / wheel_base;
    return std::make_pair(theoretical_linear, theoretical_angular);
}

int main(int argc, char *argv[])
{
    if (argc < 2) {
        std::cerr << "Usage: " << argv[0] << " <input_file>" << std::endl;
        return 1;
    }
    double ax_calib_val = 0;
    double ay_calib_val = 0;
    if (argc == 2) {
        ax_calib_val = 0.0;
        ay_calib_val = 0.0;

        // // 9号机标定数据
        // ax_calib_val = 0.35754;
        // ay_calib_val = 0.202077;
        
        // // 7号机标定数据
        // ax_calib_val = 0.141317;
        // ay_calib_val = 0.0606284;

        // // 10号机标定数据
        // ax_calib_val = 0.0918275;
        // ay_calib_val = 0.204952;
    }
    std::vector<double> pitch;
    std::vector<double> yaw;
    std::vector<double> roll;
    std::vector<double> ax;
    std::vector<double> ax_calib;
    double ax_calib_sum = 0.0;
    std::vector<double> ay;
    std::vector<double> ay_calib;
    double ay_calib_sum = 0.0;
    std::vector<std::string> time_strs;
    std::vector<double> az;
    std::vector<double> wx;
    std::vector<double> wy;
    std::vector<double> wz;
    std::vector<double> motor_speed_left;
    std::vector<double> motor_speed_right;
    std::vector<double> is_motion;
    std::vector<double> time_diff;
    std::vector<double> linear_speed;
    std::vector<double> angular_speed;
    std::vector<double> turning_slip_ratio;
    std::vector<double> moving_slip_ratio;
    std::vector<double> odom_imu_angle_diff;
    std::vector<double> slope_pitch;
    std::vector<double> slope_roll;
    std::vector<double> slope_yaw;
    std::vector<double> imu_displacement;
    std::vector<double> odom_displacement;
    std::vector<double> displacement_diff;
    std::vector<std::vector<std::pair<double, double>>> fft_gx_data;
    std::vector<double> is_freq_wheel_slip;
    std::vector<double> control_linear_velocity;
    std::vector<double> control_angular_velocity;
    // std::vector<std::vector<std::pair<double, double>>> fft_gy_data;
    // std::vector<std::vector<std::pair<double, double>>> fft_gz_data;

    std::ifstream file(argv[1]);
    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << argv[1] << std::endl;
        return 1;
    }

    fescue_iox::AccelerationProcessor processor;
    bool is_acceleration_processor_init = false;

    std::string line;
    std::shared_ptr<fescue_iox::IMUFilter> imu_filter = nullptr;
    std::shared_ptr<fescue_iox::SlipDetector> slip_detector = std::make_shared<fescue_iox::SlipDetector>();
    double first_time = -1;
    while (std::getline(file, line)) {
        try {
            nlohmann::json j = nlohmann::json::parse(line);

            if (!j.contains("imu") || !j.contains("motor_speed") || !j.contains("motion_detection_result")) {
                continue;
            }
            const auto& imu = j["imu"];
            double timestamp = (double)imu["system_timestamp"] / 1000;
            if (first_time < 0) {
                first_time = timestamp;
            }
            if (imu_filter == nullptr) {
                imu_filter = std::make_shared<fescue_iox::IMUFilter>(timestamp);
                continue;
            }
            // Parse IMU data
            wx.push_back(imu["angular_velocity_x"]);
            wy.push_back(imu["angular_velocity_y"]);
            double wz_value = imu["angular_velocity_z"];
            wz.push_back(-wz_value);
            double ax_value = (double)imu["linear_acceleration_x"] - ax_calib_val;
            double ay_value = (double)imu["linear_acceleration_y"] - ay_calib_val;

            if (!is_acceleration_processor_init) {
                is_acceleration_processor_init = true;
                if (j.contains("acceleration_filter_data")) {
                    const auto& acceleration_filter_data = j["acceleration_filter_data"];
                    processor.SetAxFilterValue(acceleration_filter_data["ax_filter_val"]);
                    processor.SetAyFilterValue(acceleration_filter_data["ay_filter_val"]);
                    std::deque<double> ax_window;
                    std::deque<double> ay_window;
                    for (const auto& data : acceleration_filter_data["ax_window"]) {
                        ax_window.push_back(data);
                    }
                    for (const auto& data : acceleration_filter_data["ay_window"]) {
                        ay_window.push_back(data);
                    }
                    processor.SetAxWindow(ax_window);
                    processor.SetAyWindow(ay_window);
                }
            }

            processor.Update(ax_value, ay_value);
            ax_value = processor.GetAx();
            ay_value = processor.GetAy();

            ax.push_back(ax_value);
            ay.push_back(ay_value);
            az.push_back(imu["linear_acceleration_z"]);
            ax_calib_sum += ax.back();
            ay_calib_sum += ay.back();
            double cur_time_diff = timestamp - first_time;
            time_diff.push_back(cur_time_diff);
            
            imu_filter->Update(timestamp, ax.back(), ay.back(), az.back(), wx.back(), wy.back(), wz.back());
            Vector3d angles = imu_filter->GetAngles();
            pitch.push_back(angles(0));
            roll.push_back(angles(1));
            yaw.push_back(angles(2));

            // Parse slope result data
            if (j.contains("slope_result")) {
                const auto& slope = j["slope_result"];
                slope_pitch.push_back(slope["pitch"]);
                slope_roll.push_back(slope["roll"]);
                slope_yaw.push_back(slope["yaw"]);
            } else {
                slope_pitch.push_back(0.0);
                slope_roll.push_back(0.0);
                slope_yaw.push_back(0.0);
            }

            // Parse motion detection result
            const auto& motion = j["motion_detection_result"];
            bool is_motion_value = motion["is_motion"].get<bool>();
            is_motion.push_back(is_motion_value ? 1.0 : 0.0);

            // Parse motor speed data
            const auto& motor = j["motor_speed"];
            motor_speed_left.push_back(motor["motor_speed_left"]);
            motor_speed_right.push_back(motor["motor_speed_right"]);

            slip_detector->Update(timestamp, ax.back(), ay.back(), az.back(), 
                                  wx.back(), wy.back(), wz.back(), 
                                  motor_speed_left.back(), motor_speed_right.back(), is_motion_value);
            turning_slip_ratio.push_back(slip_detector->GetTurningSlipRatio());
            odom_imu_angle_diff.push_back(slip_detector->GetOdomImuAngleDiff());
            moving_slip_ratio.push_back(slip_detector->GetMovingSlipRatio());
            imu_displacement.push_back(slip_detector->GetImuDisplacement());
            odom_displacement.push_back(slip_detector->GetOdomDisplacement());
            displacement_diff.push_back(slip_detector->GetDisplacementDiff());
            const auto& fft_gx = slip_detector->GetFFTGxData();
            fft_gx_data.push_back(fft_gx);
            // const auto& fft_gy = slip_detector->GetFFTGyData();
            // fft_gy_data.push_back(fft_gy);
            // const auto& fft_gz = slip_detector->GetFFTGzData();
            // fft_gz_data.push_back(fft_gz);
            is_freq_wheel_slip.push_back(slip_detector->IsFreqWheelSlip() ? 1.0 : 0.0);
            std::pair<double, double> speed = GetRobotSpeed(motor_speed_left.back(), motor_speed_right.back());
            linear_speed.push_back(speed.first);
            angular_speed.push_back(speed.second);
            std::string time_str;
            if (j.contains("aa_system_time")) {
                time_str = j["aa_system_time"].get<std::string>();
            }
            double linear_velocity_value = 0.0;
            double angular_velocity_value = 0.0;
            if (j.contains("velocity_data")) {
                const auto& velocity_data = j["velocity_data"];
                linear_velocity_value = velocity_data["linear_velocity"];
                angular_velocity_value = velocity_data["angular_velocity"];
            }
            control_linear_velocity.push_back(linear_velocity_value);
            control_angular_velocity.push_back(angular_velocity_value);
            time_strs.push_back(time_str);
            std::cout << "cur_time_diff: " << cur_time_diff 
                      << " time_str: " << time_str
                      << " wx: " << wx.back()
                      << " wy: " << wy.back()
                      << " wz: " << wz.back()
                      << " ax: " << ax.back()
                      << " ay: " << ay.back()
                      << " az: " << az.back()
                      << " pitch: " << pitch.back()
                      << " roll: " << roll.back()
                      << " yaw: " << yaw.back()
                      << " is_motion: " << is_motion.back()
                      << " linear_speed: " << linear_speed.back()
                      << " angular_speed: " << angular_speed.back()
                      << " imu_displacement: " << imu_displacement.back()
                      << " turning_slip_ratio: " << turning_slip_ratio.back()
                      << " moving_slip_ratio: " << moving_slip_ratio.back()
                      << " odom_displacement: " << odom_displacement.back()
                      << " displacement_diff: " << displacement_diff.back()
                      << std::endl;
        } catch (const nlohmann::json::parse_error& e) {
            std::cerr << "JSON parse error: " << e.what() << std::endl;
            continue;
        } catch (const nlohmann::json::exception& e) {
            std::cerr << "JSON error: " << e.what() << std::endl;
            continue;
        }
    }

    if (ax.size() == 0) {
        std::cout << "ax.size() == 0" << std::endl;
        return 0;
    }

    double ax_calib_mean = ax_calib_sum / ax.size();
    double ay_calib_mean = ay_calib_sum / ay.size();
    for (size_t i = 0; i < ax.size(); ++i) {
        ax_calib.push_back(ax[i] - ax_calib_mean);
        ay_calib.push_back(ay[i] - ay_calib_mean);
    }

    std::cout << "Loaded data wx size: " << wx.size() 
              << " ax_size: " << ax.size()
              << " ay_size: " << ay.size()
              << " ax_calib_mean: " << ax_calib_mean 
              << " ay_calib_mean: " << ay_calib_mean 
              << std::endl;

    // Save data to CSV file
    std::string home_path(getenv("HOME"));
    std::string sensor_data_path = home_path + "/sensor_data.csv";
    std::ofstream outfile(sensor_data_path);
    if (!outfile.is_open()) {
        std::cerr << "Failed to create output file" << std::endl;
        return 1;
    }

    // Write CSV header
    outfile << "time_str,time_diff,pitch,roll,yaw,slope_pitch,slope_roll,slope_yaw,"
            << "wx,wy,wz,ax,ay,az,ax_calib,ay_calib,"
            << "motor_speed_left,motor_speed_right,linear_speed,angular_speed,control_linear_velocity,control_angular_velocity,"
            << "is_motion,imu_displacement,odom_displacement,displacement_diff,odom_imu_angle_diff,"
            << "moving_slip_ratio,turning_slip_ratio,"
            << "is_freq_wheel_slip,"
            << "frequency,amplitude,axis\n";  // 添加axis列

    // Write data rows
    for (size_t i = 0; i < wx.size(); ++i) {
        const auto& fft_gx = fft_gx_data[i];  
        // const auto& fft_gy = fft_gy_data[i]; 
        // const auto& fft_gz = fft_gz_data[i]; 
        
        // 计算这个时刻要写入的行数（取三个轴中最大的FFT数据点数）
        size_t max_fft_size = fft_gx.size();
        
        // 如果这个时刻没有FFT数据，至少写入一行基本数据
        if (max_fft_size == 0) {
            outfile << time_strs[i] << "," << time_diff[i] << ","
                    << pitch[i] << "," << roll[i] << "," << yaw[i] << ","
                    << slope_pitch[i] << "," << slope_roll[i] << "," << slope_yaw[i] << ","
                    << wx[i] << "," << wy[i] << "," << wz[i] << ","
                    << ax[i] << "," << ay[i] << "," << az[i] << ","
                    << ax_calib[i] << "," << ay_calib[i] << ","
                    << motor_speed_left[i] << "," << motor_speed_right[i] << ","
                    << linear_speed[i] << "," << angular_speed[i] << ","
                    << control_linear_velocity[i] << "," << control_angular_velocity[i] << ","
                    << is_motion[i] << ","
                    << imu_displacement[i] << "," << odom_displacement[i] << "," << displacement_diff[i] << "," << odom_imu_angle_diff[i] << ","
                    << moving_slip_ratio[i] << "," << turning_slip_ratio[i] << ","
                    << is_freq_wheel_slip[i] << ","
                    << ",,\n";  // 空的frequency和amplitude和axis
            continue;
        }
        
        // 写入X轴的FFT数据
        for (const auto& data : fft_gx) {
            outfile << time_strs[i] << "," << time_diff[i] << ","
                    << pitch[i] << "," << roll[i] << "," << yaw[i] << ","
                    << slope_pitch[i] << "," << slope_roll[i] << "," << slope_yaw[i] << ","
                    << wx[i] << "," << wy[i] << "," << wz[i] << ","
                    << ax[i] << "," << ay[i] << "," << az[i] << ","
                    << ax_calib[i] << "," << ay_calib[i] << ","
                    << motor_speed_left[i] << "," << motor_speed_right[i] << ","
                    << linear_speed[i] << "," << angular_speed[i] << ","
                    << control_linear_velocity[i] << "," << control_angular_velocity[i] << ","
                    << is_motion[i] << ","
                    << imu_displacement[i] << "," << odom_displacement[i] << "," << displacement_diff[i] << "," << odom_imu_angle_diff[i] << ","
                    << moving_slip_ratio[i] << "," << turning_slip_ratio[i] << ","
                    << is_freq_wheel_slip[i] << ","
                    << data.first << "," << data.second << ",x\n";  // X轴的FFT数据
        }
    }

    outfile.close();
    std::cout << "Data saved to sensor_data.csv" << std::endl;

    return 0;
}