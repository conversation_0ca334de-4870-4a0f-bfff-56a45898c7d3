#include <iostream>
#include <vector>
#include <fstream>
#include <string>
#include <cmath>
#include <cstdlib>

#include "Eigen/Dense"
#include "json.hpp"
#include "utils/pose_fusion.hpp"
#include "utils/time.hpp"

using namespace Eigen;

std::pair<double, double> GetRobotSpeed(double motor_speed_left, double motor_speed_right) {
    double wheel_radius = 0.1f;
    double wheel_base = 0.335f;
    float w_left = motor_speed_left * 2 * M_PI / 60.0f;
    float w_right = motor_speed_right * 2 * M_PI / 60.0f;
    float v_left = w_left * wheel_radius;
    float v_right = w_right * wheel_radius;
    float theoretical_linear = (v_right + v_left) / 2.0f;
    float theoretical_angular = (v_right - v_left) / wheel_base;
    return std::make_pair(theoretical_linear, theoretical_angular);
}

int main(int argc, char *argv[])
{
    if (argc < 2) {
        std::cerr << "Usage: " << argv[0] << " <input_file>" << std::endl;
        return 1;
    }
    std::vector<double> world_x;
    std::vector<double> world_y;
    std::vector<double> world_yaw;
    std::vector<std::string> time_strs;
    std::vector<double> turning_slip_ratio;
    std::vector<double> moving_slip_ratio;
    std::vector<double> is_wheel_slip;
    std::vector<double> time_diff;
    std::vector<double> is_motion;
    std::vector<double> linear_velocity;
    std::vector<double> angular_velocity;

    std::ifstream file(argv[1]);
    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << argv[1] << std::endl;
        return 1;
    }

    std::string line;
    std::shared_ptr<fescue_iox::PoseFusion> pose_fusion = nullptr;
    double first_time = -1;
    while (std::getline(file, line)) {
        try {
            nlohmann::json j = nlohmann::json::parse(line);

            if (!j.contains("imu") || !j.contains("motor_speed") || !j.contains("motion_detection_result")) {
                continue;
            }
            const auto& imu = j["imu"];
            const auto& motor_speed = j["motor_speed"];
            const auto& motion_detection_result = j["motion_detection_result"];
            bool is_motion_value = motion_detection_result["is_motion"].get<bool>();
            is_motion.push_back(is_motion_value ? 1.0 : 0.0);
            double timestamp = (double)imu["system_timestamp"] / 1000;
            if (first_time < 0) {
                first_time = timestamp;
            }
            if (pose_fusion == nullptr) {
                pose_fusion = std::make_shared<fescue_iox::PoseFusion>();
            }
            // Parse IMU data
            double ax = imu["linear_acceleration_x"];
            double ay = imu["linear_acceleration_y"];
            double az = imu["linear_acceleration_z"];
            double gx = imu["angular_velocity_x"];
            double gy = imu["angular_velocity_y"];
            double gz = imu["angular_velocity_z"];

            double ax_calib_val = 0.0;
            double ay_calib_val = 0.0;

            // 9号机标定数据
            // double ax_calib_val = 0.35754;
            // double ay_calib_val = 0.202077;

            // // 7号机标定数据
            // double ax_calib_val = 0.141317;
            // double ay_calib_val = 0.0606284;

            // // 10号机标定数据
            // double ax_calib_val = 0.0918275;
            // double ay_calib_val = 0.204952;

            ax = ax - ax_calib_val;
            ay = ay - ay_calib_val;
            gz = -gz;
            double left_motor_speed = motor_speed["motor_speed_left"];
            double right_motor_speed = motor_speed["motor_speed_right"];
            pose_fusion->Update(timestamp, ax, ay, az, gx, gy, gz, left_motor_speed, right_motor_speed, is_motion_value);
            world_x.push_back(pose_fusion->GetX());
            world_y.push_back(pose_fusion->GetY());
            world_yaw.push_back(pose_fusion->GetYaw());
            std::string time_str;
            if (j.contains("aa_system_time")) {
                time_str = j["aa_system_time"].get<std::string>();
            }
            time_strs.push_back(time_str);
            turning_slip_ratio.push_back(pose_fusion->GetTurningSlipRatio());
            moving_slip_ratio.push_back(pose_fusion->GetMovingSlipRatio());
            is_wheel_slip.push_back(pose_fusion->GetIsWheelSlip() ? 1.0 : 0.0);
            time_diff.push_back(timestamp - first_time);
            linear_velocity.push_back(pose_fusion->GetLinearVelocity());
            angular_velocity.push_back(pose_fusion->GetAngularVelocity());
        } catch (const nlohmann::json::parse_error& e) {
            std::cerr << "JSON parse error: " << e.what() << std::endl;
            continue;
        } catch (const nlohmann::json::exception& e) {
            std::cerr << "JSON error: " << e.what() << std::endl;
            continue;
        }
    }
    std::string home_path(getenv("HOME"));
    std::cout << "world_x: " << world_x.size() << " home_path: " << home_path  << std::endl;

    // 保存数据到CSV文件
    std::string world_pose_path = home_path + "/world_pose.csv";
    std::ofstream outfile(world_pose_path);
    if (!outfile.is_open()) {
        std::cerr << "Failed to open output file" << std::endl;
        return 1;
    }

    // 写入CSV头（添加time_diff字段）
    outfile << "time_str,time_diff,world_x,world_y,world_yaw,turning_slip_ratio,moving_slip_ratio,is_wheel_slip,is_motion,linear_velocity,angular_velocity\n";

    // 写入数据（添加time_diff字段）
    for (size_t i = 0; i < world_x.size(); ++i) {
        outfile << time_strs[i] << ","
                << time_diff[i] << ","
                << world_x[i] << ","
                << world_y[i] << ","
                << world_yaw[i] << ","
                << turning_slip_ratio[i] << ","
                << moving_slip_ratio[i] << ","
                << is_wheel_slip[i] << ","
                << is_motion[i] << ","
                << linear_velocity[i] << ","
                << angular_velocity[i] << "\n";
    }

    outfile.close();
    std::cout << "Data saved to " << world_pose_path << std::endl;

    return 0;
}
