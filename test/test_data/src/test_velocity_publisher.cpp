#include "velocity_publisher.hpp"

int main(int argc, char **argv)
{
    auto velocity_publisher = std::make_shared<fescue_iox::VelocityPublisher>("test_velocity_publisher");

    fescue_iox::ob_mower_msgs::NavFusionPose fusion_pose;
    fusion_pose.x = 1.0;
    fusion_pose.y = 1.0;
    fusion_pose.yaw = 0.0;
    velocity_publisher->SetFusionPose(fusion_pose);

    double turning_angle = 1.57;
    velocity_publisher->AdjustHeading(turning_angle);

    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    fusion_pose.x = 1.05;
    fusion_pose.y = 1.0;
    fusion_pose.yaw = 0.0;
    velocity_publisher->SetFusionPose(fusion_pose);

    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    fusion_pose.x = 1.1;
    fusion_pose.y = 1.05;
    fusion_pose.yaw = 0.3;
    velocity_publisher->SetFusionPose(fusion_pose);

    return 0;
}