import pandas as pd
import plotly.subplots as sp
import plotly.graph_objects as go
import numpy as np
import os

# 读取CSV文件
home_dir = os.path.expanduser("~")
df = pd.read_csv(os.path.join(home_dir, "sensor_data.csv"))

# 时间偏移计算
source_time = pd.to_datetime('2018-08-08_21-10-16', format='%Y-%m-%d_%H-%M-%S')
target_time = pd.to_datetime('2018-08-08_21-10-16', format='%Y-%m-%d_%H-%M-%S')
time_delta = target_time - source_time

# 为了提高效率，先创建一个基础数据副本，包含所有需要的列
columns_to_keep = [
    'time_diff', 'time_str',
    'pitch', 'roll', 'yaw',
    'slope_pitch', 'slope_roll', 'slope_yaw',
    'wx', 'wy', 'wz',
    'ax', 'ay', 'az',
    'ax_calib', 'ay_calib',
    'motor_speed_left', 'motor_speed_right',
    'linear_speed', 'angular_speed',
    'control_linear_velocity', 'control_angular_velocity',
    'is_motion',
    'imu_displacement', 'odom_displacement', 'displacement_diff', 'odom_imu_angle_diff',
    'moving_slip_ratio', 'turning_slip_ratio',
    'is_freq_wheel_slip'  # 新增列
]
base_df = df[columns_to_keep].drop_duplicates('time_diff')

# 处理时间字符串
# 首先去除毫秒部分，只保留主要时间部分
base_df['time_str_main'] = base_df['time_str'].str.split('.').str[0]
base_df['original_time'] = pd.to_datetime(base_df['time_str_main'], format='%Y-%m-%d_%H-%M-%S')
base_df['adjusted_time'] = base_df['original_time'] + time_delta
base_df['time_str'] = base_df['adjusted_time'].dt.strftime('%Y-%m-%d_%H-%M-%S')

# 如果需要保留原始的毫秒部分
base_df['milliseconds'] = base_df['time_str'].str.split('.').str[1]
base_df['time_str'] = base_df.apply(lambda x: f"{x['time_str']}.{x['milliseconds']}" if pd.notna(x['milliseconds']) else x['time_str'], axis=1)

# 清理临时列
base_df = base_df.drop(['time_str_main', 'milliseconds', 'original_time', 'adjusted_time'], axis=1)

# 创建14个子图的图表（13个传感器数据 + 1个FFT）
fig = sp.make_subplots(
    rows=14, cols=1,
    subplot_titles=(
        'Euler Angles Comparison (IMUFilter vs Slope)',
        'Angular Velocity (rad/s)',
        'Linear Acceleration (m/s²)',
        'Calibrated vs Raw Acceleration (m/s²)',
        'Motor Speed (rpm)',
        'Robot Speed',
        'Control Velocity',  # 新增子图标题
        'Motion Detection',
        'Displacement Comparison',
        'Moving Slip Ratio',
        'Turning Slip Ratio',
        'Frequency-based Wheel Slip Detection',
        'Odom-IMU Angle Difference',
        'Angular Velocity X-axis FFT Waterfall Plot'
    ),
    vertical_spacing=0.015,  # 减小垂直间距
    row_heights=[0.06] * 13 + [0.22]  # 前13个图占6%，FFT图占22%
)

# 1. 添加欧拉角对比数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['pitch'], name='IMUFilter Pitch', 
               line=dict(color='red'),
               hovertemplate='Time: %{customdata}<br>Pitch: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=1, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['slope_pitch'], name='Slope Pitch', 
               line=dict(color='red', dash='dash'),
               hovertemplate='Time: %{customdata}<br>Slope Pitch: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=1, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['roll'], name='IMUFilter Roll', 
               line=dict(color='green'),
               hovertemplate='Time: %{customdata}<br>Roll: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=1, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['slope_roll'], name='Slope Roll', 
               line=dict(color='green', dash='dash'),
               hovertemplate='Time: %{customdata}<br>Slope Roll: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=1, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['yaw'], name='IMUFilter Yaw', 
               line=dict(color='blue'),
               hovertemplate='Time: %{customdata}<br>Yaw: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=1, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['slope_yaw'], name='Slope Yaw', 
               line=dict(color='blue', dash='dash'),
               hovertemplate='Time: %{customdata}<br>Slope Yaw: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=1, col=1
)

# 2. 添加角速度数据 (wx, wy, wz)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['wx'], name='Angular Velocity X', 
               line=dict(color='red'),
               hovertemplate='Time: %{customdata}<br>ωx: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=2, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['wy'], name='Angular Velocity Y', 
               line=dict(color='green'),
               hovertemplate='Time: %{customdata}<br>ωy: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=2, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['wz'], name='Angular Velocity Z', 
               line=dict(color='blue'),
               hovertemplate='Time: %{customdata}<br>ωz: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=2, col=1
)

# 3. 添加线性加速度数据 (ax, ay, az)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['ax'], name='Linear Acceleration X', 
               line=dict(color='red'),
               hovertemplate='Time: %{customdata}<br>ax: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=3, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['ay'], name='Linear Acceleration Y', 
               line=dict(color='green'),
               hovertemplate='Time: %{customdata}<br>ay: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=3, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['az'], name='Linear Acceleration Z', 
               line=dict(color='blue'),
               hovertemplate='Time: %{customdata}<br>az: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=3, col=1
)

# 4. 添加校准后的加速度对比数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['ax'], name='Raw Acceleration X', 
               line=dict(color='red', dash='dash'),
               hovertemplate='Time: %{customdata}<br>Raw ax: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=4, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['ax_calib'], name='Calibrated Acceleration X', 
               line=dict(color='red'),
               hovertemplate='Time: %{customdata}<br>Calibrated ax: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=4, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['ay'], name='Raw Acceleration Y', 
               line=dict(color='green', dash='dash'),
               hovertemplate='Time: %{customdata}<br>Raw ay: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=4, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['ay_calib'], name='Calibrated Acceleration Y', 
               line=dict(color='green'),
               hovertemplate='Time: %{customdata}<br>Calibrated ay: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=4, col=1
)

# 5. 添加电机速度数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['motor_speed_left'], name='Left Motor Speed', 
               line=dict(color='orange'),
               hovertemplate='Time: %{customdata}<br>Left Motor: %{y:.1f} RPM<extra></extra>',
               customdata=base_df['time_str']),
    row=5, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['motor_speed_right'], name='Right Motor Speed', 
               line=dict(color='purple'),
               hovertemplate='Time: %{customdata}<br>Right Motor: %{y:.1f} RPM<extra></extra>',
               customdata=base_df['time_str']),
    row=5, col=1
)

# 6. 添加机器人速度数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['linear_speed'], name='Linear Speed', 
               line=dict(color='cyan'),
               hovertemplate='Time: %{customdata}<br>Linear Speed: %{y:.3f} m/s<extra></extra>',
               customdata=base_df['time_str']),
    row=6, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['angular_speed'], name='Angular Speed', 
               line=dict(color='magenta'),
               hovertemplate='Time: %{customdata}<br>Angular Speed: %{y:.3f} rad/s<extra></extra>',
               customdata=base_df['time_str']),
    row=6, col=1
)

# 7. 添加控制速度数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['control_linear_velocity'], name='Control Linear Velocity', 
               line=dict(color='blue'),
               hovertemplate='Time: %{customdata}<br>Control Linear: %{y:.3f} m/s<extra></extra>',
               customdata=base_df['time_str']),
    row=7, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['control_angular_velocity'], name='Control Angular Velocity', 
               line=dict(color='red'),
               hovertemplate='Time: %{customdata}<br>Control Angular: %{y:.3f} rad/s<extra></extra>',
               customdata=base_df['time_str']),
    row=7, col=1
)

# 8. 添加运动检测结果
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['is_motion'], name='Motion Detection', 
               line=dict(color='black'),
               hovertemplate='Time: %{customdata}<br>Motion: %{y}<extra></extra>',
               customdata=base_df['time_str']),
    row=8, col=1
)

# 9. 添加位移对比数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['imu_displacement'], name='IMU Displacement', 
               line=dict(color='red'),
               hovertemplate='Time: %{customdata}<br>IMU Disp: %{y:.3f} m<extra></extra>',
               customdata=base_df['time_str']),
    row=9, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['odom_displacement'], name='Odom Displacement', 
               line=dict(color='blue'),
               hovertemplate='Time: %{customdata}<br>Odom Disp: %{y:.3f} m<extra></extra>',
               customdata=base_df['time_str']),
    row=9, col=1
)
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['displacement_diff'], name='Displacement Difference', 
               line=dict(color='purple'),
               hovertemplate='Time: %{customdata}<br>Disp Diff: %{y:.3f} m<extra></extra>',
               customdata=base_df['time_str']),
    row=9, col=1
)

# 10. 添加移动滑移率
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['moving_slip_ratio'], name='Moving Slip Ratio', 
               line=dict(color='orange'),
               hovertemplate='Time: %{customdata}<br>Moving Slip: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=10, col=1
)

# 11. 添加转向滑移率
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['turning_slip_ratio'], name='Turning Slip Ratio', 
               line=dict(color='brown'),
               hovertemplate='Time: %{customdata}<br>Turning Slip: %{y:.3f}<extra></extra>',
               customdata=base_df['time_str']),
    row=11, col=1
)

# 12. 添加频率检测的轮滑数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['is_freq_wheel_slip'], name='Frequency-based Wheel Slip', 
               line=dict(color='purple'),
               hovertemplate='Time: %{customdata}<br>Wheel Slip: %{y}<extra></extra>',
               customdata=base_df['time_str']),
    row=12, col=1
)

# 13. 添加odom-imu角度差数据
fig.add_trace(
    go.Scatter(x=base_df['time_diff'], y=base_df['odom_imu_angle_diff'], name='Odom-IMU Angle Diff', 
               line=dict(color='green'),
               hovertemplate='Time: %{customdata}<br>Angle Diff: %{y:.3f} rad<extra></extra>',
               customdata=base_df['time_str']),
    row=13, col=1
)

# 处理FFT数据
fft_data = df[['time_diff', 'frequency', 'amplitude', 'axis']].dropna()

# 创建自定义颜色刻度
custom_colorscale = [
    [0, 'rgb(0,0,100)'],      # 深蓝色
    [0.1, 'rgb(0,0,255)'],    # 蓝色
    [0.2, 'rgb(0,255,255)'],  # 青色
    [0.3, 'rgb(0,255,0)'],    # 绿色
    [0.4, 'rgb(255,255,0)'],  # 黄色
    [0.5, 'rgb(255,165,0)'],  # 橙色
    [0.7, 'rgb(255,69,0)'],   # 红橙色
    [1.0, 'rgb(255,0,0)']     # 红色
]

if not fft_data.empty:
    # 筛选X轴的数据
    x_axis_data = fft_data[fft_data['axis'] == 'x']
    
    # 使用pivot_table快速创建时间-频率矩阵
    amplitude_matrix = x_axis_data.pivot_table(
        values='amplitude',
        index='frequency',
        columns='time_diff',
        aggfunc='first'
    )
    
    # 获取时间和频率数组
    times = amplitude_matrix.columns.values
    freqs = amplitude_matrix.index.values
    
    # 添加热力图
fig.add_trace(
    go.Heatmap(
        x=times,
        y=freqs,
        z=amplitude_matrix.values,
        colorscale=custom_colorscale,
        name='Angular Velocity X-axis FFT',
        zmin=0,        # 设置最小值
        zmax=1.0,      # 设置最大值
        hoverongaps=False,
        colorbar=dict(
            title='Amplitude',
            ticktext=['0', '0.1', '0.2', '0.3', '0.4', '0.5', '0.7', '1.0'],
            tickvals=[0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.7, 1.0],
            tickmode='array',
            len=0.3,    # 调整颜色条的长度
            yanchor="bottom",  # 颜色条的锚点
            y=0,        # 颜色条的位置
            ypad=0      # 颜色条的内边距
        ),
        hovertemplate='Time: %{x:.2f}s<br>Frequency: %{y:.2f}Hz<br>Amplitude: %{z:.3f}<extra></extra>'
        ),
        row=14, col=1
    )

# 更新布局
fig.update_layout(
    title_text='Sensor Data Visualization',
    showlegend=True,
    height=3500,  # 调整总高度以适应新增的子图
    legend=dict(
        orientation="h",
        yanchor="bottom",
        y=1.02,
        xanchor="right",
        x=1
    ),
    hoverlabel=dict(
        bgcolor="white",
        font_size=12,
        font_family="Rockwell"
    )
)

# 更新Y轴标签
fig.update_yaxes(title_text="Angle (rad)", row=1, col=1)
fig.update_yaxes(title_text="rad/s", row=2, col=1)
fig.update_yaxes(title_text="m/s²", row=3, col=1)
fig.update_yaxes(title_text="m/s²", row=4, col=1)
fig.update_yaxes(title_text="RPM", row=5, col=1)
fig.update_yaxes(title_text="Speed (m/s, rad/s)", row=6, col=1)
fig.update_yaxes(title_text="Control Speed (m/s, rad/s)", row=7, col=1)
fig.update_yaxes(title_text="Motion (0/1)", row=8, col=1)
fig.update_yaxes(title_text="Displacement (m)", row=9, col=1)
fig.update_yaxes(title_text="Moving Slip Ratio", row=10, col=1)
fig.update_yaxes(title_text="Turning Slip Ratio", row=11, col=1)
fig.update_yaxes(title_text="Wheel Slip (0/1)", row=12, col=1)
fig.update_yaxes(title_text="Angle Diff (rad)", row=13, col=1)
fig.update_yaxes(title_text="Frequency (Hz)", row=14, col=1)

# 更新X轴标签
for i in range(1, 15):  # 更新循环范围到15
    fig.update_xaxes(title_text="Time (s)", row=i, col=1)

# 创建HTML内容，添加滚动条样式
plot_div = fig.to_html(full_html=False)
html_content = f'''
<html>
<head>
    <style>
        body {{
            margin: 0;
            padding: 20px;
            overflow-y: scroll;
        }}
        .plot-container {{
            width: 100%;
            height: 100vh;
            overflow-y: auto;
        }}
    </style>
</head>
<body>
    <div class="plot-container">
        {plot_div}
    </div>
</body>
</html>
'''

# 保存为HTML文件（可交互）
with open(os.path.join(home_dir, "sensor_visualization.html"), 'w') as f:
    f.write(html_content)

print("Visualization has been saved to sensor_visualization.html")
