import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import time
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import os

print("Starting the visualization process...")
start_time = time.time()

# 读取CSV文件
print("Reading CSV file...")
home_dir = os.path.expanduser("~")
df = pd.read_csv(os.path.join(home_dir, "world_pose.csv"))

# 提取数据
x = df['world_x'].values
y = df['world_y'].values
theta = df['world_yaw'].values
time_diff = df['time_diff'].values
turning_slip = df['turning_slip_ratio'].values
moving_slip = df['moving_slip_ratio'].values
wheel_slip = df['is_wheel_slip'].values
is_motion = df['is_motion'].values
linear_vel = df['linear_velocity'].values
angular_vel = df['angular_velocity'].values

# 第一部分：轨迹动画（保持不变）
print("\nCreating trajectory animation...")

# 如果数据点太多，可以进行降采样
sample_rate = max(len(x) // 500, 1)  # 最多使用500个点
x = x[::sample_rate]
y = y[::sample_rate]
theta = theta[::sample_rate]

print(f"Processing {len(x)} data points for trajectory...")

# 创建图形和轴
fig, ax = plt.subplots(figsize=(10, 10))

# 设置坐标轴范围
margin = 1.0
ax.set_xlim(min(x) - margin, max(x) + margin)
ax.set_ylim(min(y) - margin, max(y) + margin)

# 设置坐标轴标签和标题
ax.set_xlabel('X (m)')
ax.set_ylabel('Y (m)')
ax.set_title('Robot Trajectory Animation')
ax.grid(True)
ax.set_aspect('equal')

# 初始化绘图对象
line, = ax.plot([], [], 'b-', linewidth=2, label='Trajectory')  # 轨迹线
point, = ax.plot([], [], 'ro', markersize=10, label='Robot')    # 机器人当前位置

# 初始化箭头
arrow_length = 0.2
quiver = ax.quiver(0, 0, 0, 0, color='r', scale=5, label='Orientation')

# 添加图例
ax.legend()

# 定义动画更新函数
def update(frame):
    # 更新轨迹线
    line.set_data(x[:frame+1], y[:frame+1])
    
    # 更新机器人位置点
    point.set_data([x[frame]], [y[frame]])
    
    # 更新朝向箭头
    dx = arrow_length * np.cos(theta[frame])
    dy = arrow_length * np.sin(theta[frame])
    quiver.set_offsets([[x[frame], y[frame]]])  # 设置箭头位置
    quiver.set_UVC(dx, dy)  # 设置箭头方向
    
    # 打印进度
    if frame % 50 == 0:
        print(f"Processing frame {frame}/{len(x)} ({frame/len(x)*100:.1f}%)")
    
    return line, point, quiver

print("Creating animation...")

# 创建动画
frames = len(x)
interval = 20  # 减少间隔时间到20毫秒
ani = animation.FuncAnimation(fig, update, frames=frames,
                            interval=interval, blit=True)

print("Saving animation to GIF...")
# 保存动画为GIF文件，使用较低的dpi来减少文件大小和处理时间
ani.save(os.path.join(home_dir, "trajectory_animation.gif"), writer='pillow', dpi=100)
plt.close()

end_time = time.time()
print(f"Trajectory animation completed in {end_time - start_time:.1f} seconds")
print("Animation has been saved to " + os.path.join(home_dir, "trajectory_animation.gif"))

# 第二部分：更新滑移率可视化（使用time_diff作为x轴）
print("\nCreating slip visualization with Plotly...")

# 创建Plotly图表
fig = make_subplots(rows=6, cols=1, 
                   subplot_titles=("Moving Slip Ratio", 
                                  "Turning Slip Ratio", 
                                  "Wheel Slip Status",
                                  "Motion Status",
                                  "Linear Velocity",
                                  "Angular Velocity"),
                   vertical_spacing=0.1,
                   shared_xaxes=True)  # 共享x轴

# 添加移动滑移率曲线（使用time_diff作为x轴）
fig.add_trace(
    go.Scatter(x=time_diff, y=moving_slip, name="Moving Slip Ratio"),
    row=1, col=1
)

# 添加转向滑移率曲线（使用time_diff作为x轴）
fig.add_trace(
    go.Scatter(x=time_diff, y=turning_slip, name="Turning Slip Ratio"),
    row=2, col=1
)

# 添加车轮打滑状态曲线（使用time_diff作为x轴）
fig.add_trace(
    go.Scatter(x=time_diff, y=wheel_slip, name="Wheel Slip", mode='lines+markers'),
    row=3, col=1
)

# 添加运动状态曲线（使用time_diff作为x轴）
fig.add_trace(
    go.Scatter(x=time_diff, y=is_motion, name="Motion", mode='lines+markers'),
    row=4, col=1
)

# 更新图表布局
fig.update_layout(
    height=900,
    title_text="Robot Slip Analysis (Time-based)",
    showlegend=True,
    hovermode="x unified"
)

# 添加线速度曲线
fig.add_trace(
    go.Scatter(x=time_diff, y=linear_vel, name="Linear Velocity"),
    row=5, col=1
)

# 添加角速度曲线
fig.add_trace(
    go.Scatter(x=time_diff, y=angular_vel, name="Angular Velocity"),
    row=6, col=1
)

# 更新Y轴标签
fig.update_yaxes(title_text="Slip Ratio", row=1, col=1)
fig.update_yaxes(title_text="Slip Ratio", row=2, col=1)
fig.update_yaxes(title_text="Slip Status (1=slip)", row=3, col=1)
fig.update_yaxes(title_text="Motion Status (1=motion)", row=4, col=1)
fig.update_yaxes(title_text="Velocity (m/s)", row=5, col=1)
fig.update_yaxes(title_text="Velocity (rad/s)", row=6, col=1)

# 更新X轴标签（只在最下面的子图显示）
fig.update_xaxes(title_text="Time Difference (s)", row=6, col=1)

# 保存为HTML文件
print("Saving slip visualization to HTML...")
fig.write_html(os.path.join(home_dir, "slip_visualization.html"))
print("Slip visualization has been saved to " + os.path.join(home_dir, "slip_visualization.html"))

total_time = time.time() - start_time
print(f"\nAll visualizations completed in {total_time:.1f} seconds")
