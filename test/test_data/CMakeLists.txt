cmake_minimum_required(VERSION 3.16)
project(test_data C CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC -O3")
set(CMAKE_C_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG}  -fPIC -g")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC -O3")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -fPIC -g")
set(CMAKE_BUILD_TYPE "Release")

find_package(OpenCV REQUIRED)
find_package(iceoryx_binding_c REQUIRED)

include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include/
    ${CMAKE_SOURCE_DIR}/thirdparty/json/include/
    ${CMAKE_SOURCE_DIR}/thirdparty/math/eigen3/
    ${CMAKE_SOURCE_DIR}/common/utils/include/
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/include
    ${iceoryx_binding_c_INCLUDE_DIRS}
)

set(slip_executable ${PROJECT_NAME}_slip_node)
add_executable(${slip_executable} ${CMAKE_CURRENT_SOURCE_DIR}/src/test_slip.cpp)
target_link_libraries(${slip_executable}
    pthread
    opencv_highgui
    opencv_imgcodecs
    opencv_imgproc
    opencv_core
    rt
    dl
    ob_utils
    iceoryx_binding_c::iceoryx_binding_c
)

set(pose_fusion_executable ${PROJECT_NAME}_pose_fusion_node)
add_executable(${pose_fusion_executable} ${CMAKE_CURRENT_SOURCE_DIR}/src/test_pose_fusion.cpp)
target_link_libraries(${pose_fusion_executable}
    pthread
    opencv_highgui
    opencv_imgcodecs
    opencv_imgproc
    opencv_core
    rt
    dl
    ob_utils
    iceoryx_binding_c::iceoryx_binding_c
)

set(connect_pose_executable ${PROJECT_NAME}_connect_pose_node)
add_executable(${connect_pose_executable} 
    ${CMAKE_CURRENT_SOURCE_DIR}/src/test_connect_pose.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/connect_trajectory_generator.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/process_fusion.cpp
    )
target_link_libraries(${connect_pose_executable}
    pthread
    opencv_highgui
    opencv_imgcodecs
    opencv_imgproc
    opencv_core
    rt
    dl
    ob_utils
    iceoryx_binding_c::iceoryx_binding_c
)

set(connect_pose_tracker_executable ${PROJECT_NAME}_connect_pose_tracker_node)
add_executable(${connect_pose_tracker_executable} 
    ${CMAKE_CURRENT_SOURCE_DIR}/src/test_connect_tracker.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/pure_pursuit_tracker.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/connect_pose_tracker.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/multi_trajectory_tracker.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/connect_trajectory_generator.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/process_fusion.cpp
    )
target_link_libraries(${connect_pose_tracker_executable}
    pthread
    opencv_highgui
    opencv_imgcodecs
    opencv_imgproc
    opencv_core
    rt
    dl
    ob_utils
    iceoryx_binding_c::iceoryx_binding_c
)

set(velocity_publisher_executable ${PROJECT_NAME}_velocity_publisher_node)
add_executable(${velocity_publisher_executable} 
    ${CMAKE_CURRENT_SOURCE_DIR}/src/test_velocity_publisher.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/pure_pursuit_tracker.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/connect_pose_tracker.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/multi_trajectory_tracker.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/connect_trajectory_generator.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/process_fusion.cpp
    )
target_link_libraries(${velocity_publisher_executable}
    pthread
    opencv_highgui
    opencv_imgcodecs
    opencv_imgproc
    opencv_core
    rt 
    dl
    ob_utils
    iceoryx_binding_c::iceoryx_binding_c
)

set(pure_pursuit_executable ${PROJECT_NAME}_pure_pursuit_node)
add_executable(${pure_pursuit_executable} 
    ${CMAKE_CURRENT_SOURCE_DIR}/src/test_pure_pursuit.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/pure_pursuit_tracker.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/connect_pose_tracker.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/multi_trajectory_tracker.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/connect_trajectory_generator.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/../../navigation/nav_common/src/process_fusion.cpp
    )
target_link_libraries(${pure_pursuit_executable}
    pthread
    opencv_highgui
    opencv_imgcodecs
    opencv_imgproc
    opencv_core
    rt
    dl
    ob_utils
    iceoryx_binding_c::iceoryx_binding_c
)