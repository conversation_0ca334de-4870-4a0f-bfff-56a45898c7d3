import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import numpy as np
import os

# 读取动画轨迹CSV文件
# 读取静态位姿轨迹CSV文件
home_dir = os.path.expanduser("~")
df_static = pd.read_csv(os.path.join(home_dir, "compare_pose.csv"))

# 读取动画轨迹CSV文件
df = pd.read_csv(os.path.join(home_dir, "connected_trajectories.csv"))
trajectories = {traj_id: group for traj_id, group in df.groupby('trajectory_id')}

# 计算每条轨迹的帧数
traj_lengths = [len(group) for group in trajectories.values()]
total_frames = sum(traj_lengths)

# 初始化图形（调整坐标轴方向）
fig, ax = plt.subplots(figsize=(12, 12))
# 计算包含静态轨迹的坐标范围
x_min = min(df['x'].min(), df_static['x'].min()) - 1.0
x_max = max(df['x'].max(), df_static['x'].max()) + 1.0
y_min = min(df['y'].min(), df_static['y'].min()) - 1.0
y_max = max(df['y'].max(), df_static['y'].max()) + 1.0

ax.set_xlim(y_max + 1.0, y_min - 1.0)  # Y轴反向（向左为正）
ax.set_ylim(x_min - 1.0, x_max + 1.0)  # X轴正常（向上为正）
ax.set_aspect('equal')
ax.grid(True)
ax.set_xlabel('Y (m) → Left')  # 更新标签
ax.set_ylabel('X (m) ↑ Up')    # 箭头表示方向

# 绘制静态位姿轨迹 (zorder=1 确保在底层)
static_line, = ax.plot(df_static['y'], df_static['x'], 
                      'g-', alpha=0.5, linewidth=1, 
                      label='Static Pose', zorder=1)

# 初始化动画轨迹对象（交换x/y坐标）
lines = [ax.plot([], [], 'o-', markersize=6, linewidth=2/4, 
                label=f'Trajectory {traj_id}', zorder=5)[0]
         for traj_id in trajectories]
points = [ax.plot([], [], 'ro', markersize=10/4, zorder=5)[0]
          for _ in trajectories]

# 初始化箭头（调整方向计算）
arrow_scale = 0.4
arrows = [ax.quiver([], [], [], [], 
                   color='blue',
                   scale=50,  # 减小scale使箭头整体变小
                   width=0.005,  # 减小杆宽度
                   headwidth=0.5,  # 减小头部宽度
                   headlength=0.3,  # 减小头部长度
                   headaxislength=1.5,  # 减小头部轴长
                   zorder=10
                  ) for _ in trajectories]
ax.legend(fontsize=12, loc='upper right')

# 动画更新函数（交换x/y坐标并修正箭头方向）
def update(frame):
    remaining_frames = frame
    current_traj_id = 0
    current_frame = 0
    
    for traj_id, length in enumerate(traj_lengths):
        if remaining_frames < length:
            current_traj_id = traj_id
            current_frame = remaining_frames
            break
        remaining_frames -= length
    
    for i, (traj_id, group) in enumerate(trajectories.items()):
        if traj_id < current_traj_id:
            # 交换x/y坐标
            lines[i].set_data(group['y'], group['x'])
            points[i].set_data([group['y'].iloc[-1]], [group['x'].iloc[-1]])
            # 调整角度（新坐标系：向上为X，向左为Y）
            angle = group['theta'].iloc[-1]  # 原始角度（弧度）
            dx = -arrow_scale * np.sin(angle)  # 新X方向分量（注意负号）
            dy = arrow_scale * np.cos(angle)   # 新Y方向分量
            arrows[i].set_offsets([[group['y'].iloc[-1], group['x'].iloc[-1]]])
            arrows[i].set_UVC(dx, dy)
        elif traj_id == current_traj_id:
            lines[i].set_data(group['y'].iloc[:current_frame+1], group['x'].iloc[:current_frame+1])
            points[i].set_data([group['y'].iloc[current_frame]], [group['x'].iloc[current_frame]])
            angle = group['theta'].iloc[current_frame]
            dx = -arrow_scale * np.sin(angle)
            dy = arrow_scale * np.cos(angle)
            arrows[i].set_offsets([[group['y'].iloc[current_frame], group['x'].iloc[current_frame]]])
            arrows[i].set_UVC(dx, dy)
        else:
            lines[i].set_data([], [])
            points[i].set_data([], [])
            arrows[i].set_offsets([[np.nan, np.nan]])
    
    ax.set_title(f'Trajectory Animation (Frame {frame+1}/{total_frames})', fontsize=14)
    return lines + points + arrows

# 运行动画
ani = FuncAnimation(fig, update, frames=total_frames, interval=100, blit=True)

# 保存GIF
print("Saving GIF animation...")
ani.save(os.path.join(home_dir, "trajectory_animation.gif"), 
         writer='pillow', 
         fps=15,
         dpi=200,
         progress_callback=lambda i, n: print(f'Saving frame {i}/{n}') if i % 10 == 0 else None
        )
print("GIF saved to " + os.path.join(home_dir, "trajectory_animation.gif"))

