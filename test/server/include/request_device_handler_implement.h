#pragma once

#include "fifo_map.hpp"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "iox/vector.hpp"
#include "json.hpp"
#include "mower_msgs/srv/calibration_bev.hpp"
#include "mower_msgs/srv/test_mode.hpp"
#include "ob_mower_srvs/calibration_node_param_service__struct.h"
#include "ob_mower_srvs/camera_node_param_service__struct.h"
#include "ob_mower_srvs/device_self_check_service.h"
#include "ob_mower_srvs/serialprocotol_node_params_service__struct.h"
#include "ob_mower_srvs/union_rgb_camera_param_service__struct.h"
#include "opencv2/opencv.hpp"
#include "process_resource.h"
#include "request_handler_interface.h"
#include "utils/file.hpp"
#include "utils/iceoryx_client_mower.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"

namespace fescue_iox
{

using json = nlohmann::json;
using namespace nlohmann;

using namespace mower_msgs::srv;

// A workaround to give to use fifo_map as map, we are just ignoring the 'less'
// compare
template <class K, class V, class dummy_compare, class A>
using my_workaround_fifo_map = fifo_map<K, V, fifo_map_compare<K>, A>;
using my_json = basic_json<my_workaround_fifo_map>;

using set_calibration_node_param_client = iox::popo::Client<fescue_msgs__srv__SetCalibrationNodeParam_Request, fescue_msgs__srv__SetCalibrationNodeParam_Response>;
using get_calibration_node_param_client = iox::popo::Client<fescue_msgs__srv__GetCalibrationNodeParam_Request, fescue_msgs__srv__GetCalibrationNodeParam_Response>;
using execute_calibration_bev_client = iox::popo::Client<mower_msgs::srv::CalibrationBevRequest, mower_msgs::srv::CalibrationBevResponse>;

using get_camera_node_param_client = iox::popo::Client<fescue_msgs__srv__GetCameraNodeParam_Request, fescue_msgs__srv__GetCameraNodeParam_Response>;
using set_camera_node_param_client = iox::popo::Client<fescue_msgs__srv__SetCameraNodeParam_Request, fescue_msgs__srv__SetCameraNodeParam_Response>;

using get_union_rgb_param_client = iox::popo::Client<fescue_msgs__srv__GetUnionRGBCameraParam_Request, fescue_msgs__srv__GetUnionRGBCameraParam_Response>;
using set_union_rgb_param_client = iox::popo::Client<fescue_msgs__srv__SetUnionRGBCameraParam_Request, fescue_msgs__srv__SetUnionRGBCameraParam_Response>;

using set_serialprotocol_param_client = iox::popo::Client<fescue_msgs__srv__SetSerialProtocolParams_Request, fescue_msgs__srv__SetSerialProtocolParams_Response>;
using get_serialprotocol_param_client = iox::popo::Client<fescue_msgs__srv__GetSerialProtocolParams_Request, fescue_msgs__srv__GetSerialProtocolParams_Response>;

struct CalibrationNodeParam
{
    std::string console_log_level;
    std::string file_log_level;
};

struct SerialProtocolParams
{
    std::string console_log_level;
    std::string file_log_level;
    bool perception_enable;
    bool grass_cell_enable;
    bool charge_enable;
    bool qr_position_enable;
    bool mark_loc_enable;
    int fusion_result_select;
};

class GetSerialProtocolParamHandler : public IRequestHandler
{
public:
    GetSerialProtocolParamHandler() = default;
    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequestToNode())
        {
            my_json j;
            j["console_log_level"] = params_.console_log_level;
            j["file_log_level"] = params_.file_log_level;
            j["perception_enable"] = params_.perception_enable;
            j["grass_cell_enable"] = params_.grass_cell_enable;
            j["charge_enable"] = params_.charge_enable;
            j["qr_position_enable"] = params_.qr_position_enable;
            j["mark_loc_enable"] = params_.mark_loc_enable;
            j["fusion_result_select"] = params_.fusion_result_select;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        get_serialprotocol_param_client client({kServiceGetSerialProtocolParamsRequestIox[0],
                                                kServiceGetSerialProtocolParamsRequestIox[1],
                                                kServiceGetSerialProtocolParamsRequestIox[2]},
                                               options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request.send().or_else([&](auto &error) { std::cout << "Could not send get perception node param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) {
                    ret = response->success;
                    params_.console_log_level = std::string(response->data.console_log_level.c_str());
                    params_.file_log_level = std::string(response->data.file_log_level.c_str());
                    params_.perception_enable = response->data.perception_enable;
                    params_.grass_cell_enable = response->data.grass_cell_enable;
                    params_.charge_enable = response->data.charge_enable;
                    params_.qr_position_enable = response->data.qr_position_enable;
                    params_.mark_loc_enable = response->data.mark_loc_enable;
                    params_.fusion_result_select = response->data.fusion_result_select;
                }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    SerialProtocolParams params_;
};

class SetSerialProtocolParamHandler : public IRequestHandler
{
public:
    SetSerialProtocolParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetSerialProtocolParamHandler: " << request << std::endl;
        try
        {
            nlohmann::json jsonObj = nlohmann::json::parse(request);
            params_.console_log_level = jsonObj["console_log_level"];
            params_.file_log_level = jsonObj["file_log_level"];
            params_.perception_enable = jsonObj["perception_enable"];
            params_.grass_cell_enable = jsonObj["grass_cell_enable"];
            params_.charge_enable = jsonObj["charge_enable"];
            params_.qr_position_enable = jsonObj["qr_position_enable"];
            params_.mark_loc_enable = jsonObj["mark_loc_enable"];
            params_.fusion_result_select = jsonObj["fusion_result_select"];
            if (!sendRequestToNode())
            {
                std::cerr << "send set param request To SerialProtocol Node Error!" << std::endl;
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            std::cerr << "JSON Type Error: " << ex.what() << std::endl;
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        set_serialprotocol_param_client client({kServiceSetSerialProtocolParamsRequestIox[0],
                                                kServiceSetSerialProtocolParamsRequestIox[1],
                                                kServiceSetSerialProtocolParamsRequestIox[2]},
                                               options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request->data.console_log_level.unsafe_assign(params_.console_log_level.c_str());
                         request->data.file_log_level.unsafe_assign(params_.file_log_level.c_str());
                         request->data.perception_enable = params_.perception_enable;
                         request->data.grass_cell_enable = params_.grass_cell_enable;
                         request->data.charge_enable = params_.charge_enable;
                         request->data.qr_position_enable = params_.qr_position_enable;
                         request->data.mark_loc_enable = params_.mark_loc_enable;
                         request->data.fusion_result_select = params_.fusion_result_select;
                         request.send().or_else([&](auto &error) { std::cout << "Could not send set SerialProtocol node param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) { ret = response->success; }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    SerialProtocolParams params_;
};

class GetCalibrationNodeParamHandler : public IRequestHandler
{
public:
    GetCalibrationNodeParamHandler() = default;
    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequestToNode())
        {
            my_json j;
            j["console_log_level"] = param_.console_log_level;
            j["file_log_level"] = param_.file_log_level;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 1U;
        get_calibration_node_param_client client({KServiceGetCalibrationNodeParamRequestIox[0],
                                                  KServiceGetCalibrationNodeParamRequestIox[1],
                                                  KServiceGetCalibrationNodeParamRequestIox[2]},
                                                 options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         request.send().or_else([&](auto &error) { std::cout << "Could not send get calibration node param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) {
                    ret = response->success;
                    param_.console_log_level = std::string(response->data.console_log_level.c_str());
                    param_.file_log_level = std::string(response->data.file_log_level.c_str());
                }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    CalibrationNodeParam param_;
};

class SetCalibrationNodeParamHandler : public IRequestHandler
{
public:
    SetCalibrationNodeParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetCalibrationNodeParamHandler: " << request << std::endl;
        try
        {
            nlohmann::json jsonObj = nlohmann::json::parse(request);
            params_.console_log_level = jsonObj["console_log_level"];
            params_.file_log_level = jsonObj["file_log_level"];
            if (!sendRequestToNode())
            {
                std::cerr << "send set param request To Calibration Node Error!" << std::endl;
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            std::cerr << "JSON Type Error: " << ex.what() << std::endl;
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 1U;
        set_calibration_node_param_client client({KServiceSetCalibrationNodeParamRequestIox[0],
                                                  KServiceSetCalibrationNodeParamRequestIox[1],
                                                  KServiceSetCalibrationNodeParamRequestIox[2]},
                                                 options);
        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request->data.console_log_level.unsafe_assign(params_.console_log_level.c_str());
                         request->data.file_log_level.unsafe_assign(params_.file_log_level.c_str());
                         request.send().or_else([&](auto &error) { std::cout << "Could not send set Calibration node param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) { ret = response->success; }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    CalibrationNodeParam params_;
};

struct ExcuteCalibrationBevImgResult
{
    int img_width;
    int img_height;
    int img_channel;
    int img_size;
    int img_type; // 0-jpg, 1-png
    std::string img_save_path;
};

struct ExcuteCalibrationBevResult
{
    int success;
    uint32_t img_width_;        // 像素宽1280
    uint32_t img_height_;       // 像素高720
    float scotoma_distance_;    // 车头到bev完整区域最下端的距离
    float bev_physical_width_;  // bev区域对应真实物理宽度1m
    float bev_physical_length_; // bev区域对应真实物理长度1m
    float top_left_pt_x;        // 左上点 x（unit:pixel）
    float top_left_pt_y;        // 左上点 y（unit:pixel）
    float bottom_left_pt_x;     // 左下点 x（unit:pixel）
    float bottom_left_pt_y;     // 左下点 y（unit:pixel）
    float top_right_pt_x;       // 右上点 x（unit:pixel）
    float top_right_pt_y;       // 右上点 y（unit:pixel）
    float bottom_right_pt_x;    // 右下点 x（unit:pixel）
    float bottom_right_pt_y;    // 右下点 y（unit:pixel）
    ExcuteCalibrationBevImgResult img;
};

struct ExcuteCalibrationBevRequst
{
    uint64_t timestamp;
};

class ExecuteCalibrationBevHandler : public IRequestHandler
{
public:
    ExecuteCalibrationBevHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        LOG_INFO("ExecuteCalibrationBevHandler: {}", request.c_str());
        try
        {
            request_.timestamp = GetTimestampMs();
            if (!sendRequestToNode())
            {
                LOG_ERROR("send execute calibration bev request To Calibration Node Error");
                status = "400";
            }
            else
            {
                my_json j;
                j["success"] = result_.success;
                j["img_width"] = result_.img_width_;
                j["img_height"] = result_.img_height_;
                j["scotoma_distance"] = result_.scotoma_distance_;
                j["bev_physical_width"] = result_.bev_physical_width_;
                j["bev_physical_length"] = result_.bev_physical_length_;
                j["top_left_pt_x"] = result_.top_left_pt_x;
                j["top_left_pt_y"] = result_.top_left_pt_y;
                j["bottom_left_pt_x"] = result_.bottom_left_pt_x;
                j["bottom_left_pt_y"] = result_.bottom_left_pt_y;
                j["top_right_pt_x"] = result_.top_right_pt_x;
                j["top_right_pt_y"] = result_.top_right_pt_y;
                j["bottom_right_pt_x"] = result_.bottom_right_pt_x;
                j["bottom_right_pt_y"] = result_.bottom_right_pt_y;
                j["bev_img_width"] = result_.img.img_width;
                j["bev_img_height"] = result_.img.img_height;
                j["bev_img_size"] = result_.img.img_size;
                j["bev_img_type"] = result_.img.img_type == 0 ? "jpg" : "png";
                j["bev_img_path"] = result_.img.img_save_path;
                status = "200";
                content = j.dump(2);
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            std::cerr << "JSON Type Error: " << ex.what() << std::endl;
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 1U;
        execute_calibration_bev_client client({KServiceExecuteCalibrationBevRequestIox[0],
                                               KServiceExecuteCalibrationBevRequestIox[1],
                                               KServiceExecuteCalibrationBevRequestIox[2]},
                                              options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request->timestamp = request_.timestamp;
                         request.send().or_else([&](auto &error) { std::cout << "Could not send execute calibration bev Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(5));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) {
                    ret = true;
                    result_.success = response->success;
                    result_.img_width_ = response->param.img_width_;
                    result_.img_height_ = response->param.img_height_;
                    result_.scotoma_distance_ = response->param.scotoma_distance_;
                    result_.bev_physical_width_ = response->param.bev_physical_width_;
                    result_.bev_physical_length_ = response->param.bev_physical_lenght_;
                    result_.top_left_pt_x = response->param.top_left_pt_x;
                    result_.top_left_pt_y = response->param.top_left_pt_y;
                    result_.bottom_left_pt_x = response->param.bottom_left_pt_x;
                    result_.bottom_left_pt_y = response->param.bottom_left_pt_y;
                    result_.top_right_pt_x = response->param.top_right_pt_x;
                    result_.top_right_pt_y = response->param.top_right_pt_y;
                    result_.bottom_right_pt_x = response->param.bottom_right_pt_x;
                    result_.bottom_right_pt_y = response->param.bottom_right_pt_y;
                    result_.img.img_width = response->image.width;
                    result_.img.img_height = response->image.height;
                    result_.img.img_size = response->image.size;
                    result_.img.img_type = response->image.type;
                    std::string extent_type{".jpg"};
                    if (response->image.type == 1)
                    {
                        extent_type = ".png";
                    }
                    result_.img.img_save_path = "/userdata/log/test_server_node/calibration_bev_" + std::to_string(GetTimestampMs()) + extent_type;
                    WriteFile(result_.img.img_save_path, response->image.data.data(), response->image.size);
                }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    ExcuteCalibrationBevRequst request_;
    ExcuteCalibrationBevResult result_;
};

struct CameraNodeParam
{
    std::string console_log_level;
    std::string file_log_level;
    bool save_img;
};

class GetCameraNodeParamHandler : public IRequestHandler
{
public:
    GetCameraNodeParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"400"};

        std::cout << "GetCameraNodeParamHandler: " << request << std::endl;

        if (sendRequestToNode())
        {
            my_json j;
            j["console_log_level"] = param_.console_log_level;
            j["file_log_level"] = param_.file_log_level;
            j["save_img"] = param_.save_img;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        get_camera_node_param_client client({kServiceGetCameraNodeParamRequestIox[0],
                                             kServiceGetCameraNodeParamRequestIox[1],
                                             kServiceGetCameraNodeParamRequestIox[2]},
                                            options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request.send().or_else([&](auto &error) { std::cout << "Could not send get camera node param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) {
                    // 接收从 camera node 返回的应答
                    ret = response->success;
                    param_.console_log_level = std::string(response->data.console_log_level.c_str());
                    param_.file_log_level = std::string(response->data.file_log_level.c_str());
                    param_.save_img = response->data.save_img;
                }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    CameraNodeParam param_;
};

class SetCameraNodeParamHandler : public IRequestHandler
{
public:
    SetCameraNodeParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetCameraNodeParamHandler: " << request << std::endl;
        try
        {
            nlohmann::json jsonObj = nlohmann::json::parse(request);
            param_.console_log_level = jsonObj["console_log_level"];
            param_.file_log_level = jsonObj["file_log_level"];
            param_.save_img = jsonObj["save_img"];
            if (!sendRequestToNode())
            {
                std::cerr << "send set param request To CameraNode Error!" << std::endl;
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            std::cerr << "JSON Type Error: " << ex.what() << std::endl;
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        set_camera_node_param_client client({kServiceSetCameraNodeParamRequestIox[0],
                                             kServiceSetCameraNodeParamRequestIox[1],
                                             kServiceSetCameraNodeParamRequestIox[2]},
                                            options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // 发送到 camera node
                         request->data.console_log_level.unsafe_assign(param_.console_log_level.c_str());
                         request->data.file_log_level.unsafe_assign(param_.file_log_level.c_str());
                         request->data.save_img = param_.save_img;
                         request.send().or_else([&](auto &error) { std::cout << "Could not send set camera node param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) { ret = response->success; }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    CameraNodeParam param_;
};

/*****************************************************************************************************************/

struct UnionRGBCameraParam
{
    int debug_level;
    int twi_bus;
    int mipi_host;
    int sen_fps;
    int fps_div;
    int bypass;
    int timeout;
    int rotate;
};

class GetUnionRGBCameraParamHandler : public IRequestHandler
{
public:
    GetUnionRGBCameraParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequestToNode())
        {
            my_json j;
            j["debug_level"] = param_.debug_level;
            j["twi_bus"] = param_.twi_bus;
            j["mipi_host"] = param_.mipi_host;
            j["sen_fps"] = param_.sen_fps;
            j["fps_div"] = param_.fps_div;
            j["bypass"] = param_.bypass;
            j["timeout"] = param_.timeout;
            j["rotate"] = param_.rotate;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        get_union_rgb_param_client client({kServiceGetUnionRGBCameraParamRequestIox[0],
                                           kServiceGetUnionRGBCameraParamRequestIox[1],
                                           kServiceGetUnionRGBCameraParamRequestIox[2]},
                                          options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request.send().or_else([&](auto &error) { std::cout << "Could not send get unicore rgb camera param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) {
                    // 接收从 camera node 返回的应答
                    ret = response->success;
                    param_.debug_level = response->data.debug_level;
                    param_.twi_bus = response->data.twi_bus;
                    param_.mipi_host = response->data.mipi_host;
                    param_.sen_fps = response->data.sen_fps;
                    param_.fps_div = response->data.fps_div;
                    param_.bypass = response->data.bypass;
                    param_.timeout = response->data.timeout;
                    param_.rotate = response->data.rotate;
                }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    UnionRGBCameraParam param_;
};

class SetUnionRGBCameraParamHandler : public IRequestHandler
{
public:
    SetUnionRGBCameraParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetUnionRGBCameraParamHandler: " << request << std::endl;
        try
        {
            nlohmann::json jsonObj = nlohmann::json::parse(request);
            param_.debug_level = jsonObj["debug_level"];
            param_.twi_bus = jsonObj["twi_bus"];
            param_.mipi_host = jsonObj["mipi_host"];
            param_.sen_fps = jsonObj["sen_fps"];
            param_.fps_div = jsonObj["fps_div"];
            param_.bypass = jsonObj["bypass"];
            param_.timeout = jsonObj["timeout"];
            param_.rotate = jsonObj["rotate"];
            if (!sendRequestToNode())
            {
                std::cerr << "send set param request To unicore rgb camera Error!" << std::endl;
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            std::cerr << "JSON Type Error: " << ex.what() << std::endl;
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        set_union_rgb_param_client client({kServiceSetUnionRGBCameraParamRequestIox[0],
                                           kServiceSetUnionRGBCameraParamRequestIox[1],
                                           kServiceSetUnionRGBCameraParamRequestIox[2]},
                                          options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // 发送到 camera node
                         request->data.debug_level = param_.debug_level;
                         request->data.twi_bus = param_.twi_bus;
                         request->data.mipi_host = param_.mipi_host;
                         request->data.sen_fps = param_.sen_fps;
                         request->data.fps_div = param_.fps_div;
                         request->data.bypass = param_.bypass;
                         request->data.timeout = param_.timeout;
                         request->data.rotate = param_.rotate;
                         request.send().or_else([&](auto &error) { std::cout << "Could not send set unicore rgb camera param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) { ret = response->success; }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    UnionRGBCameraParam param_;
};

class GetProcessCpuUsageHandler : public IRequestHandler
{
public:
    GetProcessCpuUsageHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        std::string perception_cros = GetCpuUsageFromTop(process_name_1);
        std::string perception_segm = GetCpuUsageFromTop(process_name_2);
        std::string perception_rech = GetCpuUsageFromTop(process_name_3);
        std::string camera_node = GetCpuUsageFromTop(process_name_4);
        my_json j;
        j["perception_cros"] = perception_cros;
        j["perception_segm"] = perception_segm;
        j["perception_rech"] = perception_rech;
        j["camera_node"] = camera_node;
        status = "200";
        content = j.dump(2);
        return std::make_pair(status, content);
    }

private:
    const std::string process_name_1{"perception_cros"};
    const std::string process_name_2{"perception_segm"};
    const std::string process_name_3{"perception_rech"};
    const std::string process_name_4{"camera_node"};
};

struct McuSensorData
{
    bool collision_left;
    bool collision_right;
    bool lift_left;
    bool lift_right;
    bool emergency_stop;
    bool charge_terminal_status;
    bool blade_status;
    uint8_t battery_value;
};

class GetMcuSensorDataHandler : public IRequestHandler
{
public:
    GetMcuSensorDataHandler(std::function<void(McuSensorData &)> callback)
    {
        mcu_sensor_data_callback_ = callback;
    }
    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        McuSensorData mcu_data;
        if (mcu_sensor_data_callback_)
        {
            mcu_sensor_data_callback_(mcu_data);
        }
        ResponseContent content{""};
        ResponseStatus status{"400"};
        my_json j;
        j["lift_left"] = mcu_data.lift_left;
        j["lift_right"] = mcu_data.lift_right;
        j["collision_left"] = mcu_data.collision_left;
        j["collision_right"] = mcu_data.collision_right;
        j["emergency_stop"] = mcu_data.emergency_stop;
        j["charge_terminal_status"] = mcu_data.charge_terminal_status;
        j["battery_value"] = mcu_data.battery_value;
        j["blade_status"] = mcu_data.blade_status;
        status = "200";
        content = j.dump(2);
        return std::make_pair(status, content);
    }

private:
    std::function<void(McuSensorData &)> mcu_sensor_data_callback_{nullptr};
};

struct McuMotorSpeedData
{
    float motor_speed_left;
    float motor_speed_right;
    float motor_current_left;
    float motor_current_right;
};

class GetMcuMotorSpeedDataHandler : public IRequestHandler
{
public:
    GetMcuMotorSpeedDataHandler(std::function<void(McuMotorSpeedData &)> callback)
    {
        motor_speed_data_callback_ = callback;
    }
    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        McuMotorSpeedData motor_speed_data;
        if (motor_speed_data_callback_)
        {
            motor_speed_data_callback_(motor_speed_data);
        }
        ResponseContent content{""};
        ResponseStatus status{"400"};
        my_json j;
        j["motor_speed_left"] = motor_speed_data.motor_speed_left;
        j["motor_speed_right"] = motor_speed_data.motor_speed_right;
        j["motor_current_left"] = motor_speed_data.motor_current_left;
        j["motor_current_right"] = motor_speed_data.motor_current_right;
        status = "200";
        content = j.dump(2);
        return std::make_pair(status, content);
    }

private:
    std::function<void(McuMotorSpeedData &)> motor_speed_data_callback_{nullptr};
};

struct TestModeData
{
    int mode;
    uint64_t timestamp_ms;
};

class SetTestModeHandler : public IRequestHandler
{
public:
    SetTestModeHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        TestModeData data;
        std::cout << "SetTestModeHandler: " << request << std::endl;
        try
        {
            nlohmann::json j = nlohmann::json::parse(request);
            data.mode = j["test_mode_type"];
            data.timestamp_ms = j["timestamp_ms"];
            if (!sendRequest(data))
            {
                LOG_ERROR("send set param request To mcu_communication_node Error!");
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            LOG_ERROR("JSON Parse Error: {}", ex.what());
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            LOG_ERROR("JSON Type Error: {}", ex.what());
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequest(const TestModeData &data)
    {
        auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::TestModeRequest,
                                                          mower_msgs::srv::TestModeResponse>>("test_mode");

        auto request_handler = [](const mower_msgs::srv::TestModeRequest &request_input,
                                  mower_msgs::srv::TestModeRequest &request_send) {
            request_send = request_input;
        };

        auto response_handler = [](const mower_msgs::srv::TestModeResponse &response_receive,
                                   mower_msgs::srv::TestModeResponse &response_output) -> bool {
            response_output = response_receive;
            return response_output.success;
        };

        mower_msgs::srv::TestModeRequest request_input;
        mower_msgs::srv::TestModeResponse response_output;
        request_input.test_mode_type = static_cast<mower_msgs::srv::TestModeType>(data.mode);
        request_input.timestamp = data.timestamp_ms;
        return client->SendRequest(request_input, response_output, request_handler, response_handler);
    }
};

struct DevSelfCheckData
{
    float vel_linear;
    float vel_angular;
    int duration_ms;
};

class SetDevSelfCheckHandler : public IRequestHandler
{
    using set_dev_selfcheck_request = fescue_iox::ob_mower_srvs::SetDeviceSelfCheckRequest;
    using set_dev_selfcheck_response = fescue_iox::ob_mower_srvs::SetDeviceSelfCheckResponse;

public:
    SetDevSelfCheckHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        DevSelfCheckData data;
        std::cout << "SetDevSelfCheckHandler: " << request << std::endl;
        try
        {
            nlohmann::json j = nlohmann::json::parse(request);
            data.vel_linear = j["vel_linear"];
            data.vel_angular = j["vel_angular"];
            data.duration_ms = j["duration_ms"];
            if (!sendRequest(data))
            {
                LOG_ERROR("send set param request To Test server node Error!");
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            LOG_ERROR("JSON Parse Error: {}", ex.what());
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            LOG_ERROR("JSON Type Error: {}", ex.what());
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequest(const DevSelfCheckData &data)
    {
        auto client = std::make_unique<IceoryxClientMower<set_dev_selfcheck_request,
                                                          set_dev_selfcheck_response>>("set_device_selfcheck_request");

        auto request_handler = [](const set_dev_selfcheck_request &request_input,
                                  set_dev_selfcheck_request &request_send) {
            request_send = request_input;
        };

        auto response_handler = [](const set_dev_selfcheck_response &response_receive,
                                   set_dev_selfcheck_response &response_output) -> bool {
            response_output = response_receive;
            return response_output.success;
        };

        set_dev_selfcheck_request request_input;
        set_dev_selfcheck_response response_output;
        request_input.data.linear = data.vel_linear;
        request_input.data.angular = data.vel_angular;
        request_input.data.duration_ms = data.duration_ms;
        return client->SendRequest(request_input, response_output, request_handler, response_handler);
    }
};

} // namespace fescue_iox