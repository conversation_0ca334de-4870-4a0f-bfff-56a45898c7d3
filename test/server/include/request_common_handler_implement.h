#pragma once

#include "fifo_map.hpp"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "iox/vector.hpp"
#include "json.hpp"
#include "ob_mower_srvs/node_common_param_service.h"
#include "request_handler_interface.h"
#include "utils/file.hpp"
#include "utils/iceoryx_client_mower.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"

namespace fescue_iox
{

using json = nlohmann::json;
using namespace nlohmann;

// A workaround to give to use fifo_map as map, we are just ignoring the 'less'
// compare
template <class K, class V, class dummy_compare, class A>
using my_workaround_fifo_map = fifo_map<K, V, fifo_map_compare<K>, A>;
using my_json = basic_json<my_workaround_fifo_map>;

////////////////////////////////////////////////////////////////////////////////////////////////////

struct NodeCommonParam
{
    std::string console_log_level;
    std::string file_log_level;
};

class GetNodeParamHandler : public IRequestHandler
{
private:
    std::string event;

public:
    explicit GetNodeParamHandler(const std::string &event)
        : event(event){};
    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        NodeCommonParam param;
        if (sendRequest(param))
        {
            my_json j;
            j["console_log_level"] = param.console_log_level;
            j["file_log_level"] = param.file_log_level;
            status = "200";
            content = j.dump(2);
        }
        std::cout << "GetNodeParamHandler " << event << ": " << content << std::endl;
        return std::make_pair(status, content);
    }
    bool sendRequest(NodeCommonParam &param)
    {
        auto client = std::make_unique<IceoryxClientMower<ob_mower_srvs::GetNodeParamRequest,
                                                          ob_mower_srvs::GetNodeParamResponse>>(event);
        auto response_handler = [](const ob_mower_srvs::GetNodeParamResponse &response_receive,
                                   ob_mower_srvs::GetNodeParamResponse &response_output) -> bool {
            response_output = response_receive;
            return response_output.success;
        };

        ob_mower_srvs::GetNodeParamRequest request_input;
        ob_mower_srvs::GetNodeParamResponse response_output;
        if (!client->SendRequest(request_input, response_output, nullptr, response_handler))
        {
            return false;
        }
        param.console_log_level = std::string(response_output.data.console_log_level.c_str());
        param.file_log_level = std::string(response_output.data.file_log_level.c_str());
        return true;
    }
};

class SetNodeParamHandler : public IRequestHandler
{
private:
    std::string event;

public:
    explicit SetNodeParamHandler(const std::string &event)
        : event(event){};

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        NodeCommonParam param;
        std::cout << "SetNodeParamHandler " << event << ": " << request << std::endl;
        try
        {
            nlohmann::json j = nlohmann::json::parse(request);
            param.console_log_level = j["console_log_level"];
            param.file_log_level = j["file_log_level"];
            if (!sendRequest(param))
            {
                LOG_ERROR("send set param request To {} Error!", event.c_str());
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            LOG_ERROR("JSON Parse Error: {}", ex.what());
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            LOG_ERROR("JSON Type Error: {}", ex.what());
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequest(const NodeCommonParam &param)
    {
        auto client = std::make_unique<IceoryxClientMower<ob_mower_srvs::SetNodeParamRequest,
                                                          ob_mower_srvs::SetNodeParamResponse>>(event);
        auto request_handler = [](const ob_mower_srvs::SetNodeParamRequest &request_input,
                                  ob_mower_srvs::SetNodeParamRequest &request_send) {
            request_send = request_input;
        };
        auto response_handler = [](const ob_mower_srvs::SetNodeParamResponse &response_receive,
                                   ob_mower_srvs::SetNodeParamResponse &response_output) -> bool {
            response_output = response_receive;
            return response_output.success;
        };
        ob_mower_srvs::SetNodeParamRequest request_input;
        ob_mower_srvs::SetNodeParamResponse response_output;
        request_input.data.console_log_level.unsafe_assign(param.console_log_level.c_str());
        request_input.data.file_log_level.unsafe_assign(param.file_log_level.c_str());
        return client->SendRequest(request_input, response_output, request_handler, response_handler);
    }
};

} // namespace fescue_iox
