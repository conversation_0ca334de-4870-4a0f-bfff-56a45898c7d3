#pragma once

#include "utils/common_config.hpp"
#include "utils/config.hpp"

#include <string>

namespace fescue_iox
{

/**
 * @brief 测试节点参数
 * @param common_conf 公共参数
 * @param server_port 测试服务器端口
 */
struct TestServerNodeConfig
{
    CommonConfig common_conf;
    int server_port{8080};
    int jpeg_quality{50};
    int max_element{1};
    TestServerNodeConfig() = default;
    TestServerNodeConfig(const TestServerNodeConfig &) = default;
    ~TestServerNodeConfig() = default;
    TestServerNodeConfig &operator=(const TestServerNodeConfig &conf);
    std::string toString() const;
};

bool operator==(const TestServerNodeConfig &lhs, const TestServerNodeConfig &rhs);
bool operator!=(const TestServerNodeConfig &lhs, const TestServerNodeConfig &rhs);

} // namespace fescue_iox
