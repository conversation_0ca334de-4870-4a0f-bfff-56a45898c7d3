#pragma once

#include "fifo_map.hpp"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "json.hpp"
#include "mower_msgs/srv/get_version_info.hpp"
#include "mower_msgs/srv/go_charge.hpp"
#include "mower_msgs/srv/go_mow.hpp"
#include "ob_mower_srvs/algorithm_version_service__struct.h"
#include "ob_mower_srvs/nav_cross_region_node_param_service__struct.h"
#include "ob_mower_srvs/nav_edge_follow_node_param_service__struct.h"
#include "ob_mower_srvs/nav_mower_node_param_service__struct.h"
#include "ob_mower_srvs/nav_random_mower_node_param_service__struct.h"
#include "ob_mower_srvs/system_version_service__struct.h"
#include "process_resource.h"
#include "request_handler_interface.h"
#include "utils/iceoryx_client.hpp"
#include "utils/utils.hpp"

#include <fstream>
#include <iostream>
#include <regex>
#include <string>

namespace fescue_iox
{

using json = nlohmann::json;
using namespace nlohmann;

using namespace mower_msgs::srv;

// A workaround to give to use fifo_map as map, we are just ignoring the 'less'
// compare
template <class K, class V, class dummy_compare, class A>
using my_workaround_fifo_map = fifo_map<K, V, fifo_map_compare<K>, A>;
using my_json = basic_json<my_workaround_fifo_map>;

using sw_go_mower_client = iox::popo::Client<GoMowRequest, GoMowResponse>;
using sw_go_charge_client = iox::popo::Client<GoChargeRequest, GoChargeResponse>;

struct SWGoMowerData
{
    int request_type{0};
};

class SWGoMowerHandler : public IRequestHandler
{
public:
    SWGoMowerHandler() = default;
    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SWGoMowerHandler: " << request << std::endl;
        try
        {
            nlohmann::json jsonObj = nlohmann::json::parse(request);
            data_.request_type = jsonObj["request_type"];
            if (!sendRequestToNavigationMowerNode())
            {
                std::cerr << "send set param request To mower Node Error!" << std::endl;
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            std::cerr << "JSON Type Error: " << ex.what() << std::endl;
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNavigationMowerNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        sw_go_mower_client client({kSWGoMowerRequestIox[0],
                                   kSWGoMowerRequestIox[1],
                                   kSWGoMowerRequestIox[2]},
                                  options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request->request_type = static_cast<GoMowRequestType>(data_.request_type);
                         request.send().or_else([&](auto &error) { std::cout << "Could not send sw go mower Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) { ret = response->success; }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    SWGoMowerData data_;
};

struct SWGoChargeData
{
    int request_type{0};
};

class SWGoChargeHandler : public IRequestHandler
{
public:
    SWGoChargeHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SWGoChargeHandler: " << request << std::endl;
        try
        {
            nlohmann::json jsonObj = nlohmann::json::parse(request);
            data_.request_type = jsonObj["request_type"];
            if (!sendRequestToNavigationMowerNode())
            {
                std::cerr << "send SW go charge To Navigation mower Node Error!" << std::endl;
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            std::cerr << "JSON Type Error: " << ex.what() << std::endl;
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNavigationMowerNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        sw_go_charge_client client({kSWGoChargeRequestIox[0],
                                    kSWGoChargeRequestIox[1],
                                    kSWGoChargeRequestIox[2]},
                                   options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request->request_type = static_cast<GoChargeRequestType>(data_.request_type);
                         request.send().or_else([&](auto &error) { std::cout << "Could not send sw go charge Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) { ret = response->success; }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    SWGoChargeData data_;
};

struct VersionInfoData
{
    std::string mcu_version;
    std::string software_version;
    std::string system_version;
    std::string algorithm_version;
    std::map<std::string, std::string> perception_alg_version;
    std::map<std::string, std::string> localization_alg_version;
    std::map<std::string, std::string> navigation_alg_version;
};

class GetVersionInfoHandler : public IRequestHandler
{
public:
    GetVersionInfoHandler() = default;
    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        VersionInfoData data;
        if (sendRequest(data))
        {
            my_json j;
            j["mcu_version"] = data.mcu_version;
            j["software_version"] = data.software_version;
            j["system_version"] = data.system_version;
            j["algorithm_version"] = data.algorithm_version;
            j["perception_alg_version"] = data.perception_alg_version;
            j["localization_alg_version"] = data.localization_alg_version;
            j["navigation_alg_version"] = data.navigation_alg_version;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }

    bool sendRequest(VersionInfoData &data)
    {
        if (sendGetSystemVersionRequest(data.system_version) &&
            sendGetPerceptionAlgVersionRequest(data.algorithm_version, data.perception_alg_version) &&
            sendGetLocalizationAlgVersionRequest(data.localization_alg_version) &&
            // sendGetNavigationAlgVersionRequest(data.navigation_alg_version) &&
            sendGetMCUAndSoftwareVersionRequest(data.mcu_version, data.software_version))
        {
            return true;
        }
        return false;
    }

    std::string getVersionFromFile(const std::string &filename)
    {
        std::ifstream file(filename);
        if (!file.is_open())
        {
            std::cerr << "Error: Cannot open file " << filename << std::endl;
            return "";
        }

        std::string line;
        std::regex version_regex(R"(Ver:V(\d+\.\d+\.\d+))"); // 正则表达式匹配版本号
        std::smatch match;

        while (std::getline(file, line))
        {
            if (std::regex_search(line, match, version_regex))
            {
                return match[1]; // 获取匹配到的版本号
            }
        }

        return "";
    }

    bool sendGetSystemVersionRequest(std::string &system_version)
    {
        system_version = getVersionFromFile("/etc/ob_issue");
        if (system_version.empty())
        {
            return false;
        }
        return true;
    }

    bool sendGetPerceptionAlgVersionRequest(std::string &algorithm_version, std::map<std::string, std::string> &perception_alg_version)
    {
        auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__GetAlgorithmVersionData_Request,
                                                          fescue_msgs__srv__GetAlgorithmVersionData_Response>>("get_perception_algo_version");
        auto response_handler = [](const fescue_msgs__srv__GetAlgorithmVersionData_Response &response_receive,
                                   fescue_msgs__srv__GetAlgorithmVersionData_Response &response_output) -> bool {
            response_output.success = response_receive.success;
            response_output.module_version = response_receive.module_version;
            response_output.data = response_receive.data;
            return response_output.success;
        };

        fescue_msgs__srv__GetAlgorithmVersionData_Request request_input;
        fescue_msgs__srv__GetAlgorithmVersionData_Response response_output;
        if (!client->SendRequest(request_input, response_output, nullptr, response_handler))
        {
            return false;
        }
        algorithm_version = std::string(response_output.module_version.c_str());
        for (size_t i = 0; i < response_output.data.size(); i++)
        {
            std::string name = std::string(response_output.data[i].name.c_str());
            std::string version = std::string(response_output.data[i].version.c_str());
            perception_alg_version[name] = std::string(version);
        }
        return true;
    }

    bool sendGetLocalizationAlgVersionRequest(std::map<std::string, std::string> &localization_alg_version)
    {
        auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__GetAlgorithmVersionData_Request,
                                                          fescue_msgs__srv__GetAlgorithmVersionData_Response>>("get_localization_algo_version");
        auto response_handler = [](const fescue_msgs__srv__GetAlgorithmVersionData_Response &response_receive,
                                   fescue_msgs__srv__GetAlgorithmVersionData_Response &response_output) -> bool {
            response_output.success = response_receive.success;
            response_output.module_version = response_receive.module_version;
            response_output.data = response_receive.data;
            return response_output.success;
        };

        fescue_msgs__srv__GetAlgorithmVersionData_Request request_input;
        fescue_msgs__srv__GetAlgorithmVersionData_Response response_output;
        if (!client->SendRequest(request_input, response_output, nullptr, response_handler))
        {
            return false;
        }
        // algorithm_version = std::string(response_output.module_version.c_str());
        for (size_t i = 0; i < response_output.data.size(); i++)
        {
            std::string name = std::string(response_output.data[i].name.c_str());
            std::string version = std::string(response_output.data[i].version.c_str());
            localization_alg_version[name] = std::string(version);
        }
        return true;
    }

    bool sendGetNavigationAlgVersionRequest(std::map<std::string, std::string> &navigation_alg_version)
    {
        auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__GetAlgorithmVersionData_Request,
                                                          fescue_msgs__srv__GetAlgorithmVersionData_Response>>("get_navigation_algo_version");
        auto response_handler = [](const fescue_msgs__srv__GetAlgorithmVersionData_Response &response_receive,
                                   fescue_msgs__srv__GetAlgorithmVersionData_Response &response_output) -> bool {
            response_output.success = response_receive.success;
            response_output.module_version = response_receive.module_version;
            response_output.data = response_receive.data;
            return response_output.success;
        };

        fescue_msgs__srv__GetAlgorithmVersionData_Request request_input;
        fescue_msgs__srv__GetAlgorithmVersionData_Response response_output;
        if (!client->SendRequest(request_input, response_output, nullptr, response_handler))
        {
            return false;
        }
        // algorithm_version = std::string(response_output.module_version.c_str());
        for (size_t i = 0; i < response_output.data.size(); i++)
        {
            std::string name = std::string(response_output.data[i].name.c_str());
            std::string version = std::string(response_output.data[i].version.c_str());
            navigation_alg_version[name] = std::string(version);
        }
        return true;
    }

    bool sendGetMCUAndSoftwareVersionRequest(std::string &mcu_version, std::string &software_version)
    {
        auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::GetVersionRequest,
                                                          mower_msgs::srv::GetVersionResponse>>("get_version_info");

        auto request_handler = [](const mower_msgs::srv::GetVersionRequest &request_input,
                                  mower_msgs::srv::GetVersionRequest &request_send) {
            request_send.timestamp = request_input.timestamp;
        };

        auto response_handler = [](const mower_msgs::srv::GetVersionResponse &response_receive,
                                   mower_msgs::srv::GetVersionResponse &response_output) -> bool {
            response_output.success = response_receive.success;
            response_output.timestamp = response_receive.timestamp;
            response_output.mcu_version = response_receive.mcu_version;
            response_output.soc_version = response_receive.soc_version;
            return response_output.success;
        };

        mower_msgs::srv::GetVersionRequest request_input;
        mower_msgs::srv::GetVersionResponse response_output;
        request_input.timestamp = GetTimestampMs();
        if (!client->SendRequest(request_input, response_output, request_handler, response_handler))
        {
            LOG_ERROR("GetMCUAndSoftwareVersionHandler send request failed");
            return false;
        }
        mcu_version = response_output.mcu_version.to_string();
        software_version = std::string(response_output.soc_version.version.c_str());
        LOG_INFO("GetMCUAndSoftwareVersionHandler send request success, {} {}", mcu_version.c_str(), software_version.c_str());
        return true;
    }
};

} // namespace fescue_iox
