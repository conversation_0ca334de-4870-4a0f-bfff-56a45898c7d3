#pragma once

#include "fifo_map.hpp"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "json.hpp"
#include "mower_msgs/msg/twist.hpp"
#include "ob_mower_srvs/nav_behavior_node_param_service__struct.h"
#include "ob_mower_srvs/nav_cross_region_alg_param_service__struct.h"
#include "ob_mower_srvs/nav_cross_region_node_param_service__struct.h"
#include "ob_mower_srvs/nav_edge_follow_alg_param_service.h"
#include "ob_mower_srvs/nav_edge_follow_node_param_service__struct.h"
#include "ob_mower_srvs/nav_escape_alg_param_service.h"
#include "ob_mower_srvs/nav_escape_node_param_service.h"
#include "ob_mower_srvs/nav_mower_alg_test_service__struct.h"
#include "ob_mower_srvs/nav_mower_node_param_service__struct.h"
#include "ob_mower_srvs/nav_random_mower_node_param_service__struct.h"
#include "ob_mower_srvs/nav_recharge_node_param_service__struct.h"
#include "ob_mower_srvs/nav_spiral_mower_alg_param_service__struct.h"
#include "ob_mower_srvs/nav_spiral_mower_node_param_service__struct.h"
#include "process_resource.h"
#include "request_handler_interface.h"
#include "utils/iceoryx_client_mower.hpp"
#include "utils/utils.hpp"

namespace fescue_iox
{

using json = nlohmann::json;
using namespace nlohmann;

// A workaround to give to use fifo_map as map, we are just ignoring the 'less'
// compare
template <class K, class V, class dummy_compare, class A>
using my_workaround_fifo_map = fifo_map<K, V, fifo_map_compare<K>, A>;
using my_json = basic_json<my_workaround_fifo_map>;

using set_mower_node_param_client = iox::popo::Client<fescue_msgs__srv__SetNavigationMowerNodeParam_Request,
                                                      fescue_msgs__srv__SetNavigationMowerNodeParam_Response>;

using get_mower_node_param_client = iox::popo::Client<fescue_msgs__srv__GetNavigationMowerNodeParam_Request,
                                                      fescue_msgs__srv__GetNavigationMowerNodeParam_Response>;

struct NavigationMowerNodeParam
{
    std::string console_log_level;
    std::string file_log_level;
};

class GetNavigationMowerNodeParamHandler : public IRequestHandler
{
public:
    GetNavigationMowerNodeParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequestToNavigationNode())
        {
            my_json j;
            j["console_log_level"] = param_.console_log_level;
            j["file_log_level"] = param_.file_log_level;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNavigationNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        get_mower_node_param_client client({kServiceGetNavigationMowerNodeParamRequestIox[0],
                                            kServiceGetNavigationMowerNodeParamRequestIox[1],
                                            kServiceGetNavigationMowerNodeParamRequestIox[2]},
                                           options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request.send().or_else([&](auto &error) { std::cout << "Could not send get mower node param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) {
                    ret = response->success;
                    param_.console_log_level = std::string(response->data.console_log_level.c_str());
                    param_.file_log_level = std::string(response->data.file_log_level.c_str());
                }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    NavigationMowerNodeParam param_;
};

class SetNavigationMowerNodeParamHandler : public IRequestHandler
{
public:
    SetNavigationMowerNodeParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetNavigationMowerCtrlHandler: " << request << std::endl;
        try
        {
            nlohmann::json jsonObj = nlohmann::json::parse(request);
            param_.console_log_level = jsonObj["console_log_level"];
            param_.file_log_level = jsonObj["file_log_level"];
            if (!sendRequestToMowerNode())
            {
                std::cerr << "send set param request To mower Node Error!" << std::endl;
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            std::cerr << "JSON Type Error: " << ex.what() << std::endl;
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToMowerNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        set_mower_node_param_client client({kServiceSetNavigationMowerNodeParamRequestIox[0],
                                            kServiceSetNavigationMowerNodeParamRequestIox[1],
                                            kServiceSetNavigationMowerNodeParamRequestIox[2]},
                                           options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request->data.console_log_level.unsafe_assign(param_.console_log_level.c_str());
                         request->data.file_log_level.unsafe_assign(param_.file_log_level.c_str());
                         request.send().or_else([&](auto &error) { std::cout << "Could not send mower ctrl node param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) { ret = response->success; }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    NavigationMowerNodeParam param_;
};

using set_recharge_node_param_client = iox::popo::Client<fescue_msgs__srv__SetNavigationRechargeNodeParam_Request,
                                                         fescue_msgs__srv__SetNavigationRechargeNodeParam_Response>;
using get_recharge_node_param_client = iox::popo::Client<fescue_msgs__srv__GetNavigationRechargeNodeParam_Request,
                                                         fescue_msgs__srv__GetNavigationRechargeNodeParam_Response>;

struct NavigationRechargeNodeParam
{
    std::string console_log_level;
    std::string file_log_level;
};

class GetNavigationRechargeNodeParamHandler : public IRequestHandler
{
public:
    GetNavigationRechargeNodeParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequestToNavigationNode())
        {
            my_json j;
            j["console_log_level"] = param_.console_log_level;
            j["file_log_level"] = param_.file_log_level;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNavigationNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        get_recharge_node_param_client client({kServiceGetNavigationRechargeNodeParamRequestIox[0],
                                               kServiceGetNavigationRechargeNodeParamRequestIox[1],
                                               kServiceGetNavigationRechargeNodeParamRequestIox[2]},
                                              options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request.send().or_else([&](auto &error) { std::cout << "Could not send get recharge node param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) {
                    ret = response->success;
                    param_.console_log_level = std::string(response->data.console_log_level.c_str());
                    param_.file_log_level = std::string(response->data.file_log_level.c_str());
                }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    NavigationRechargeNodeParam param_;
};

class SetNavigationRechargeNodeParamHandler : public IRequestHandler
{
public:
    SetNavigationRechargeNodeParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetNavigationRechargeCtrlHandler: " << request << std::endl;
        try
        {
            nlohmann::json jsonObj = nlohmann::json::parse(request);
            param_.console_log_level = jsonObj["console_log_level"];
            param_.file_log_level = jsonObj["file_log_level"];
            if (!sendRequestToRechargeNode())
            {
                std::cerr << "send set param request To recharge Node Error!" << std::endl;
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            std::cerr << "JSON Type Error: " << ex.what() << std::endl;
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToRechargeNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        set_recharge_node_param_client client({kServiceSetNavigationRechargeNodeParamRequestIox[0],
                                               kServiceSetNavigationRechargeNodeParamRequestIox[1],
                                               kServiceSetNavigationRechargeNodeParamRequestIox[2]},
                                              options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request->data.console_log_level.unsafe_assign(param_.console_log_level.c_str());
                         request->data.file_log_level.unsafe_assign(param_.file_log_level.c_str());
                         request.send().or_else([&](auto &error) { std::cout << "Could not send recharge ctrl node param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) { ret = response->success; }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    NavigationRechargeNodeParam param_;
};

using set_random_mower_node_param_client = iox::popo::Client<fescue_msgs__srv__SetNavigationRandomMowerNodeParam_Request,
                                                             fescue_msgs__srv__SetNavigationRandomMowerNodeParam_Response>;
using get_random_mower_node_param_client = iox::popo::Client<fescue_msgs__srv__GetNavigationRandomMowerNodeParam_Request,
                                                             fescue_msgs__srv__GetNavigationRandomMowerNodeParam_Response>;

struct NavigationRandomMowerNodeParam
{
    std::string console_log_level;
    std::string file_log_level;
};

class GetNavigationRandomMowerNodeParamHandler : public IRequestHandler
{
public:
    GetNavigationRandomMowerNodeParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequestToNavigationNode())
        {
            my_json j;
            j["console_log_level"] = param_.console_log_level;
            j["file_log_level"] = param_.file_log_level;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNavigationNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        get_random_mower_node_param_client client({kServiceGetNavigationRandomMowerNodeParamRequestIox[0],
                                                   kServiceGetNavigationRandomMowerNodeParamRequestIox[1],
                                                   kServiceGetNavigationRandomMowerNodeParamRequestIox[2]},
                                                  options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request.send().or_else([&](auto &error) { std::cout << "Could not send get random_mower node param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) {
                    ret = response->success;
                    param_.console_log_level = std::string(response->data.console_log_level.c_str());
                    param_.file_log_level = std::string(response->data.file_log_level.c_str());
                }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    NavigationRandomMowerNodeParam param_;
};

class SetNavigationRandomMowerNodeParamHandler : public IRequestHandler
{
public:
    SetNavigationRandomMowerNodeParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetNavigationRandomMowerCtrlHandler: " << request << std::endl;
        try
        {
            nlohmann::json jsonObj = nlohmann::json::parse(request);
            param_.console_log_level = jsonObj["console_log_level"];
            param_.file_log_level = jsonObj["file_log_level"];
            if (!sendRequestToRandomMowerNode())
            {
                std::cerr << "send set param request To random_mower Node Error!" << std::endl;
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            std::cerr << "JSON Type Error: " << ex.what() << std::endl;
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToRandomMowerNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        set_random_mower_node_param_client client({kServiceSetNavigationRandomMowerNodeParamRequestIox[0],
                                                   kServiceSetNavigationRandomMowerNodeParamRequestIox[1],
                                                   kServiceSetNavigationRandomMowerNodeParamRequestIox[2]},
                                                  options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request->data.console_log_level.unsafe_assign(param_.console_log_level.c_str());
                         request->data.file_log_level.unsafe_assign(param_.file_log_level.c_str());
                         request.send().or_else([&](auto &error) { std::cout << "Could not send random_mower ctrl node param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) { ret = response->success; }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    NavigationRandomMowerNodeParam param_;
};

using set_cross_region_node_param_client = iox::popo::Client<fescue_msgs__srv__SetNavigationCrossRegionNodeParam_Request,
                                                             fescue_msgs__srv__SetNavigationCrossRegionNodeParam_Response>;
using get_cross_region_node_param_client = iox::popo::Client<fescue_msgs__srv__GetNavigationCrossRegionNodeParam_Request,
                                                             fescue_msgs__srv__GetNavigationCrossRegionNodeParam_Response>;

struct NavigationCrossRegionNodeParam
{
    std::string console_log_level;
    std::string file_log_level;
};

class GetNavigationCrossRegionNodeParamHandler : public IRequestHandler
{
public:
    GetNavigationCrossRegionNodeParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequestToNavigationNode())
        {
            my_json j;
            j["console_log_level"] = param_.console_log_level;
            j["file_log_level"] = param_.file_log_level;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNavigationNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        get_cross_region_node_param_client client({kServiceGetNavigationCrossRegionNodeParamRequestIox[0],
                                                   kServiceGetNavigationCrossRegionNodeParamRequestIox[1],
                                                   kServiceGetNavigationCrossRegionNodeParamRequestIox[2]},
                                                  options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request.send().or_else([&](auto &error) { std::cout << "Could not send get cross_region node param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) {
                    ret = response->success;
                    param_.console_log_level = std::string(response->data.console_log_level.c_str());
                    param_.file_log_level = std::string(response->data.file_log_level.c_str());
                }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    NavigationCrossRegionNodeParam param_;
};

class SetNavigationCrossRegionNodeParamHandler : public IRequestHandler
{
public:
    SetNavigationCrossRegionNodeParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetNavigationCrossRegionCtrlHandler: " << request << std::endl;
        try
        {
            nlohmann::json jsonObj = nlohmann::json::parse(request);
            param_.console_log_level = jsonObj["console_log_level"];
            param_.file_log_level = jsonObj["file_log_level"];
            if (!sendRequestToCrossRegionNode())
            {
                std::cerr << "send set param request To cross_region Node Error!" << std::endl;
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            std::cerr << "JSON Type Error: " << ex.what() << std::endl;
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToCrossRegionNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        set_cross_region_node_param_client client({kServiceSetNavigationCrossRegionNodeParamRequestIox[0],
                                                   kServiceSetNavigationCrossRegionNodeParamRequestIox[1],
                                                   kServiceSetNavigationCrossRegionNodeParamRequestIox[2]},
                                                  options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request->data.console_log_level.unsafe_assign(param_.console_log_level.c_str());
                         request->data.file_log_level.unsafe_assign(param_.file_log_level.c_str());
                         request.send().or_else([&](auto &error) { std::cout << "Could not send cross_region ctrl node param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) { ret = response->success; }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    NavigationCrossRegionNodeParam param_;
};

using set_edge_follow_node_param_client = iox::popo::Client<fescue_msgs__srv__SetNavigationEdgeFollowNodeParam_Request,
                                                            fescue_msgs__srv__SetNavigationEdgeFollowNodeParam_Response>;
using get_edge_follow_node_param_client = iox::popo::Client<fescue_msgs__srv__GetNavigationEdgeFollowNodeParam_Request,
                                                            fescue_msgs__srv__GetNavigationEdgeFollowNodeParam_Response>;

struct NavigationEdgeFollowNodeParam
{
    std::string console_log_level;
    std::string file_log_level;
};

class GetNavigationEdgeFollowNodeParamHandler : public IRequestHandler
{
public:
    GetNavigationEdgeFollowNodeParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequestToNavigationNode())
        {
            my_json j;
            j["console_log_level"] = param_.console_log_level;
            j["file_log_level"] = param_.file_log_level;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNavigationNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        get_edge_follow_node_param_client client({kServiceGetNavigationEdgeFollowNodeParamRequestIox[0],
                                                  kServiceGetNavigationEdgeFollowNodeParamRequestIox[1],
                                                  kServiceGetNavigationEdgeFollowNodeParamRequestIox[2]},
                                                 options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request.send().or_else([&](auto &error) { std::cout << "Could not send get edge_follow node param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) {
                    ret = response->success;
                    param_.console_log_level = std::string(response->data.console_log_level.c_str());
                    param_.file_log_level = std::string(response->data.file_log_level.c_str());
                }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    NavigationEdgeFollowNodeParam param_;
};

class SetNavigationEdgeFollowNodeParamHandler : public IRequestHandler
{
public:
    SetNavigationEdgeFollowNodeParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetNavigationEdgeFollowCtrlHandler: " << request << std::endl;
        try
        {
            nlohmann::json jsonObj = nlohmann::json::parse(request);
            param_.console_log_level = jsonObj["console_log_level"];
            param_.file_log_level = jsonObj["file_log_level"];
            if (!sendRequestToEdgeFollowNode())
            {
                std::cerr << "send set param request To edge_follow Node Error!" << std::endl;
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            std::cerr << "JSON Type Error: " << ex.what() << std::endl;
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToEdgeFollowNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        set_edge_follow_node_param_client client({kServiceSetNavigationEdgeFollowNodeParamRequestIox[0],
                                                  kServiceSetNavigationEdgeFollowNodeParamRequestIox[1],
                                                  kServiceSetNavigationEdgeFollowNodeParamRequestIox[2]},
                                                 options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request->data.console_log_level.unsafe_assign(param_.console_log_level.c_str());
                         request->data.file_log_level.unsafe_assign(param_.file_log_level.c_str());
                         request.send().or_else([&](auto &error) { std::cout << "Could not send edge_follow ctrl node param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) { ret = response->success; }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    NavigationEdgeFollowNodeParam param_;
};

///////////////////////////////////////////////////////////////////////////

using nav_mower_alg_test_request = iox::popo::Client<fescue_msgs__srv__SetNavigationMowerAlgTest_Request,
                                                     fescue_msgs__srv__SetNavigationMowerAlgTest_Response>;
struct NavigationMowerAlgTestData
{
    int nav_alg_type{0};
};

class NavMowerAlgTestHandler : public IRequestHandler
{
public:
    NavMowerAlgTestHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "NavMowerAlgTestHandler: " << request << std::endl;
        try
        {
            nlohmann::json jsonObj = nlohmann::json::parse(request);
            param_.nav_alg_type = jsonObj["nav_alg_type"];
            if (!sendRequestToNavigationMowerNode())
            {
                std::cerr << "send set param request To edge_follow Node Error!" << std::endl;
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            std::cerr << "JSON Type Error: " << ex.what() << std::endl;
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNavigationMowerNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        nav_mower_alg_test_request client({kNavigationMowerAlgTestIox[0],
                                           kNavigationMowerAlgTestIox[1],
                                           kNavigationMowerAlgTestIox[2]},
                                          options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request->nav_alg_type = param_.nav_alg_type;
                         request.send().or_else([&](auto &error) { std::cout << "Could not send navigation mower alg test Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) { ret = response->success; }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    NavigationMowerAlgTestData param_;
};

struct CrossRegionAlgParam
{
    // 多区域通道参数
    float cross_region_linear{0.2};             /*param*/
    float cross_region_angular{0.5};            /*param*/
    float max_distance_threshold{0.95};         /*param*/
    float min_distance_threshold{0.65};         /*param*/
    float cross_region_special_linear{0.2};     // 特殊情况下的线速度（例如感知驱动）/*param*/
    float cross_region_special_angular{0.3};    // 特殊情况下的角速度（例如感知驱动）/*param*/
    float dis_tolerance{0.0};                   // 调整的距离阈值，防止出现冗余旋转 /*param*/
    float cross_region_angle_compensation{0.0}; // 跨区域角度补偿 /*param*/

    float channel_stop_pose_x{-0.5};               /*param*/
    int grass_count_threshold{7};                  /*param*/
    int edge_mode_direction{-1};                   // 默认逆时针 -1/*param*/
    float channel_width{1.5};                      // 通道宽度/*param*/
    float camera_2_center_dis{0.37};               // 小车摄像头到旋转中心的距离为切尔西(0.37) 格力博(0.45) /*param*/
    float adjust_mode_x_direction_threshold{-0.3}; // 过通道前，矫正模式下x方向阈值 默认-0.5/*param*/

    // 新增参数
    float mark_distance_threshold{0.5};               // 0.5 信标相对小车摄像头的距离阈值，判断是否在区域范围内 /*param*/
    int perception_drive_cooldown_time_threshold{20}; // 20s 感知驱动冷却时间  /*param*/

    float cross_region_adjust_displace{0.7}; // 0.7 跨区域后调整位移 /*param*/
};

class GetNavigationCrossRegionAlgParamHandler : public IRequestHandler
{
public:
    GetNavigationCrossRegionAlgParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequest())
        {
            my_json j;
            j["cross_region_linear"] = param_.cross_region_linear;
            j["cross_region_angular"] = param_.cross_region_angular;
            j["max_distance_threshold"] = param_.max_distance_threshold;
            j["min_distance_threshold"] = param_.min_distance_threshold;
            j["cross_region_special_linear"] = param_.cross_region_special_linear;
            j["cross_region_special_angular"] = param_.cross_region_special_angular;
            j["dis_tolerance"] = param_.dis_tolerance;
            j["cross_region_angle_compensation"] = param_.cross_region_angle_compensation;
            j["channel_stop_pose_x"] = param_.channel_stop_pose_x;
            j["grass_count_threshold"] = param_.grass_count_threshold;
            j["edge_mode_direction"] = param_.edge_mode_direction;
            j["channel_width"] = param_.channel_width;
            j["camera_2_center_dis"] = param_.camera_2_center_dis;
            j["adjust_mode_x_direction_threshold"] = param_.adjust_mode_x_direction_threshold;
            j["mark_distance_threshold"] = param_.mark_distance_threshold;
            j["perception_drive_cooldown_time_threshold"] = param_.perception_drive_cooldown_time_threshold;
            j["cross_region_adjust_displace"] = param_.cross_region_adjust_displace;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequest()
    {
        auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__GetNavigationCrossRegionAlgParam_Request,
                                                          fescue_msgs__srv__GetNavigationCrossRegionAlgParam_Response>>("get_navigation_cross_region_alg_param_request");
        auto response_handler = [](const fescue_msgs__srv__GetNavigationCrossRegionAlgParam_Response &response_receive,
                                   fescue_msgs__srv__GetNavigationCrossRegionAlgParam_Response &response_output) -> bool {
            response_output.success = response_receive.success;
            memcpy(&response_output.data, &response_receive.data, sizeof(response_output.data));
            return response_output.success;
        };

        fescue_msgs__srv__GetNavigationCrossRegionAlgParam_Request request_input;
        fescue_msgs__srv__GetNavigationCrossRegionAlgParam_Response response_output;
        if (!client->SendRequest(request_input, response_output, nullptr, response_handler))
        {
            return false;
        }

        param_.cross_region_linear = response_output.data.cross_region_linear;
        param_.cross_region_angular = response_output.data.cross_region_angular;
        param_.max_distance_threshold = response_output.data.max_distance_threshold;
        param_.min_distance_threshold = response_output.data.min_distance_threshold;
        param_.cross_region_special_linear = response_output.data.cross_region_special_linear;
        param_.cross_region_special_angular = response_output.data.cross_region_special_angular;
        param_.dis_tolerance = response_output.data.dis_tolerance;
        param_.cross_region_angle_compensation = response_output.data.cross_region_angle_compensation;
        param_.channel_stop_pose_x = response_output.data.channel_stop_pose_x;
        param_.grass_count_threshold = response_output.data.grass_count_threshold;
        param_.edge_mode_direction = response_output.data.edge_mode_direction;
        param_.channel_width = response_output.data.channel_width;
        param_.camera_2_center_dis = response_output.data.camera_2_center_dis;
        param_.adjust_mode_x_direction_threshold = response_output.data.adjust_mode_x_direction_threshold;
        param_.mark_distance_threshold = response_output.data.mark_distance_threshold;
        param_.perception_drive_cooldown_time_threshold = response_output.data.perception_drive_cooldown_time_threshold;
        param_.cross_region_adjust_displace = response_output.data.cross_region_adjust_displace;

        return true;
    }

private:
    CrossRegionAlgParam param_;
};

class SetNavigationCrossRegionAlgParamHandler : public IRequestHandler
{
public:
    SetNavigationCrossRegionAlgParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetNavigationCrossRegionAlgParamHandler: " << request << std::endl;
        try
        {
            nlohmann::json j = nlohmann::json::parse(request);
            param_.cross_region_linear = j["cross_region_linear"];
            param_.cross_region_angular = j["cross_region_angular"];
            param_.max_distance_threshold = j["max_distance_threshold"];
            param_.min_distance_threshold = j["min_distance_threshold"];
            param_.cross_region_special_linear = j["cross_region_special_linear"];
            param_.cross_region_special_angular = j["cross_region_special_angular"];
            param_.dis_tolerance = j["dis_tolerance"];
            param_.cross_region_angle_compensation = j["cross_region_angle_compensation"];
            param_.channel_stop_pose_x = j["channel_stop_pose_x"];
            param_.grass_count_threshold = j["grass_count_threshold"];
            param_.edge_mode_direction = j["edge_mode_direction"];
            param_.channel_width = j["channel_width"];
            param_.camera_2_center_dis = j["camera_2_center_dis"];
            param_.adjust_mode_x_direction_threshold = j["adjust_mode_x_direction_threshold"];
            param_.mark_distance_threshold = j["mark_distance_threshold"];
            param_.perception_drive_cooldown_time_threshold = j["perception_drive_cooldown_time_threshold"];
            param_.cross_region_adjust_displace = j["cross_region_adjust_displace"];
            if (!sendRequest())
            {
                LOG_ERROR("send set param request To cross_region Node Error!");
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            LOG_ERROR("JSON Parse Error: {}", ex.what());
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            LOG_ERROR("JSON Type Error: {}", ex.what());
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequest()
    {
        auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__SetNavigationCrossRegionAlgParam_Request,
                                                          fescue_msgs__srv__SetNavigationCrossRegionAlgParam_Response>>("set_navigation_cross_region_alg_param_request");

        auto request_handler = [](const fescue_msgs__srv__SetNavigationCrossRegionAlgParam_Request &request_input,
                                  fescue_msgs__srv__SetNavigationCrossRegionAlgParam_Request &request_send) {
            memcpy(&request_send.data, &request_input.data, sizeof(request_input.data));
        };

        auto response_handler = [](const fescue_msgs__srv__SetNavigationCrossRegionAlgParam_Response &response_receive,
                                   fescue_msgs__srv__SetNavigationCrossRegionAlgParam_Response &response_output) -> bool {
            response_output.success = response_receive.success;
            return response_output.success;
        };

        fescue_msgs__srv__SetNavigationCrossRegionAlgParam_Request request_input;
        fescue_msgs__srv__SetNavigationCrossRegionAlgParam_Response response_output;
        request_input.data.cross_region_linear = param_.cross_region_linear;
        request_input.data.cross_region_angular = param_.cross_region_angular;
        request_input.data.max_distance_threshold = param_.max_distance_threshold;
        request_input.data.min_distance_threshold = param_.min_distance_threshold;
        request_input.data.cross_region_special_linear = param_.cross_region_special_linear;
        request_input.data.cross_region_special_angular = param_.cross_region_special_angular;
        request_input.data.dis_tolerance = param_.dis_tolerance;
        request_input.data.cross_region_angle_compensation = param_.cross_region_angle_compensation;
        request_input.data.channel_stop_pose_x = param_.channel_stop_pose_x;
        request_input.data.grass_count_threshold = param_.grass_count_threshold;
        request_input.data.edge_mode_direction = param_.edge_mode_direction;
        request_input.data.channel_width = param_.channel_width;
        request_input.data.camera_2_center_dis = param_.camera_2_center_dis;
        request_input.data.adjust_mode_x_direction_threshold = param_.adjust_mode_x_direction_threshold;
        request_input.data.mark_distance_threshold = param_.mark_distance_threshold;
        request_input.data.perception_drive_cooldown_time_threshold = param_.perception_drive_cooldown_time_threshold;
        request_input.data.cross_region_adjust_displace = param_.cross_region_adjust_displace;
        return client->SendRequest(request_input, response_output, request_handler, response_handler);
    }

private:
    CrossRegionAlgParam param_;
};

struct NavigationMowerManualTwistParam
{
    uint64_t timestamp_ms;
    float twist_linear;
    float twist_angular;
};

class NavigationMowerManualTwistHandler : public IRequestHandler
{
    using iox_twist_publisher = iox::popo::Publisher<mower_msgs::msg::Twist>;

public:
    NavigationMowerManualTwistHandler()
    {
        iox::popo::PublisherOptions options_pub;
        options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;
        pub_twist_ = std::make_unique<iox_twist_publisher>(iox::capro::ServiceDescription{
                                                               kSWTwistIox[0],
                                                               kSWTwistIox[1],
                                                               kSWTwistIox[2],
                                                               {0U, 0U, 0U, 0U},
                                                               iox::capro::Interfaces::INTERNAL},
                                                           options_pub);
    }

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        NavigationMowerManualTwistParam param;
        LOG_INFO("NavigationMowerManualTwistHandler: {}", request.c_str());
        try
        {
            nlohmann::json j = nlohmann::json::parse(request);
            param.timestamp_ms = j["timestamp_ms"];
            param.twist_linear = j["twist_linear"];
            param.twist_angular = j["twist_angular"];
            if (!publishTwist(param))
            {
                LOG_ERROR("send tool control twist param Error!");
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            LOG_ERROR("JSON Parse Error: {}", ex.what());
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            LOG_ERROR("JSON Type Error: {}", ex.what());
            status = "400";
        }
        return std::make_pair(status, content);
    }

private:
    bool publishTwist(const NavigationMowerManualTwistParam &param)
    {
        mower_msgs::msg::Twist twist;
        twist.timestamp = param.timestamp_ms;
        twist.linear_velocity = param.twist_linear;
        twist.angular_velocity = param.twist_angular;
        LOG_DEBUG("NavigationMowerManualTwistHandler publish twist {} {}", twist.linear_velocity, twist.angular_velocity);
        pub_twist_->publishCopyOf(twist).or_else(
            [](auto &error) { std::cerr << "NavigationMowerManualTwistHandler publishTwist Unable to publishCopyOf, error: " << error << std::endl; });
        return true;
    }

private:
    std::unique_ptr<iox_twist_publisher> pub_twist_{nullptr};
};

struct SpiralMowerNodeParam
{
    std::string console_log_level;
    std::string file_log_level;
};

class SetNavigationSpiralMowerNodeParamHandler : public IRequestHandler
{
public:
    SetNavigationSpiralMowerNodeParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetNavigationSpiralMowerNodeParamHandler: " << request << std::endl;
        SpiralMowerNodeParam param;
        try
        {
            nlohmann::json j = nlohmann::json::parse(request);
            param.console_log_level = j["console_log_level"];
            param.file_log_level = j["file_log_level"];
            if (!sendRequest(param))
            {
                LOG_ERROR("send set param request To spiral Node Error!");
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            LOG_ERROR("JSON Parse Error: {}", ex.what());
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            LOG_ERROR("JSON Type Error: {}", ex.what());
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequest(SpiralMowerNodeParam &param)
    {
        auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__SetNavigationSpiralMowerNodeParam_Request,
                                                          fescue_msgs__srv__SetNavigationSpiralMowerNodeParam_Response>>("set_navigation_spiral_node_param_request");

        auto request_handler = [](const fescue_msgs__srv__SetNavigationSpiralMowerNodeParam_Request &request_input,
                                  fescue_msgs__srv__SetNavigationSpiralMowerNodeParam_Request &request_send) {
            request_send.data.console_log_level = request_input.data.console_log_level;
            request_send.data.file_log_level = request_input.data.file_log_level;
        };

        auto response_handler = [](const fescue_msgs__srv__SetNavigationSpiralMowerNodeParam_Response &response_receive,
                                   fescue_msgs__srv__SetNavigationSpiralMowerNodeParam_Response &response_output) -> bool {
            response_output.success = response_receive.success;
            return response_output.success;
        };

        fescue_msgs__srv__SetNavigationSpiralMowerNodeParam_Request request_input;
        fescue_msgs__srv__SetNavigationSpiralMowerNodeParam_Response response_output;
        request_input.data.console_log_level.unsafe_assign(param.console_log_level.c_str());
        request_input.data.file_log_level.unsafe_assign(param.file_log_level.c_str());
        return client->SendRequest(request_input, response_output, request_handler, response_handler);
    }
};

class GetNavigationSpiralMowerNodeParamHandler : public IRequestHandler
{
public:
    GetNavigationSpiralMowerNodeParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        SpiralMowerNodeParam param;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequest(param))
        {
            my_json j;
            j["console_log_level"] = param.console_log_level;
            j["file_log_level"] = param.file_log_level;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequest(SpiralMowerNodeParam &param)
    {
        auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__GetNavigationSpiralMowerNodeParam_Request,
                                                          fescue_msgs__srv__GetNavigationSpiralMowerNodeParam_Response>>("get_navigation_spiral_node_param_request");
        auto response_handler = [](const fescue_msgs__srv__GetNavigationSpiralMowerNodeParam_Response &response_receive,
                                   fescue_msgs__srv__GetNavigationSpiralMowerNodeParam_Response &response_output) -> bool {
            response_output.success = response_receive.success;
            response_output.data.console_log_level = response_receive.data.console_log_level;
            response_output.data.file_log_level = response_receive.data.file_log_level;
            return response_output.success;
        };

        fescue_msgs__srv__GetNavigationSpiralMowerNodeParam_Request request_input;
        fescue_msgs__srv__GetNavigationSpiralMowerNodeParam_Response response_output;
        if (!client->SendRequest(request_input, response_output, nullptr, response_handler))
        {
            return false;
        }

        param.console_log_level = std::string(response_output.data.console_log_level.c_str());
        param.file_log_level = std::string(response_output.data.file_log_level.c_str());
        return true;
    }
};

struct SpiralMowerAlgParam
{
    int spiral_rotate_angle;     // 螺旋割草旋转角度
    float forward_speed;         // 前进速度
    float trun_corner_speed_min; // 转弯最小速度,转弯速度 rad/s,转角最低速
    int angular_accuracy;        // 角度精度±1°
};

class SetNavigationSpiralMowerAlgParamHandler : public IRequestHandler
{
public:
    SetNavigationSpiralMowerAlgParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetNavigationSpiralMowerAlgParamHandler: " << request << std::endl;
        SpiralMowerAlgParam param;
        try
        {
            nlohmann::json j = nlohmann::json::parse(request);
            param.spiral_rotate_angle = j["spiral_rotate_angle"];
            param.forward_speed = j["forward_speed"];
            param.trun_corner_speed_min = j["trun_corner_speed_min"];
            param.angular_accuracy = j["angular_accuracy"];
            if (!sendRequest(param))
            {
                LOG_ERROR("send set param request To spiral Node Error!");
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            LOG_ERROR("JSON Parse Error: {}", ex.what());
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            LOG_ERROR("JSON Type Error: {}", ex.what());
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequest(SpiralMowerAlgParam &param)
    {
        auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__SetNavigationSpiralMowerAlgParam_Request,
                                                          fescue_msgs__srv__SetNavigationSpiralMowerAlgParam_Response>>("set_navigation_spiral_alg_param_request");

        auto request_handler = [](const fescue_msgs__srv__SetNavigationSpiralMowerAlgParam_Request &request_input,
                                  fescue_msgs__srv__SetNavigationSpiralMowerAlgParam_Request &request_send) {
            memcpy(&request_send.data, &request_input.data, sizeof(request_input.data));
        };

        auto response_handler = [](const fescue_msgs__srv__SetNavigationSpiralMowerAlgParam_Response &response_receive,
                                   fescue_msgs__srv__SetNavigationSpiralMowerAlgParam_Response &response_output) -> bool {
            response_output.success = response_receive.success;
            return response_output.success;
        };

        fescue_msgs__srv__SetNavigationSpiralMowerAlgParam_Request request_input;
        fescue_msgs__srv__SetNavigationSpiralMowerAlgParam_Response response_output;
        request_input.data.spiral_rotate_angle = param.spiral_rotate_angle;
        request_input.data.forward_speed = param.forward_speed;
        request_input.data.trun_corner_speed_min = param.trun_corner_speed_min;
        request_input.data.angular_accuracy = param.angular_accuracy;
        return client->SendRequest(request_input, response_output, request_handler, response_handler);
    }
};

class GetNavigationSpiralMowerAlgParamHandler : public IRequestHandler
{
public:
    GetNavigationSpiralMowerAlgParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        SpiralMowerAlgParam param;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequest(param))
        {
            my_json j;
            j["spiral_rotate_angle"] = param.spiral_rotate_angle;
            j["forward_speed"] = param.forward_speed;
            j["trun_corner_speed_min"] = param.trun_corner_speed_min;
            j["angular_accuracy"] = param.angular_accuracy;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequest(SpiralMowerAlgParam &param)
    {
        auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__GetNavigationSpiralMowerAlgParam_Request,
                                                          fescue_msgs__srv__GetNavigationSpiralMowerAlgParam_Response>>("get_navigation_spiral_alg_param_request");
        auto response_handler = [](const fescue_msgs__srv__GetNavigationSpiralMowerAlgParam_Response &response_receive,
                                   fescue_msgs__srv__GetNavigationSpiralMowerAlgParam_Response &response_output) -> bool {
            response_output.success = response_receive.success;
            memcpy(&response_output.data, &response_receive.data, sizeof(response_receive.data));
            return response_output.success;
        };

        fescue_msgs__srv__GetNavigationSpiralMowerAlgParam_Request request_input;
        fescue_msgs__srv__GetNavigationSpiralMowerAlgParam_Response response_output;
        if (!client->SendRequest(request_input, response_output, nullptr, response_handler))
        {
            return false;
        }

        param.spiral_rotate_angle = response_output.data.spiral_rotate_angle;
        param.forward_speed = response_output.data.forward_speed;
        param.trun_corner_speed_min = response_output.data.trun_corner_speed_min;
        param.angular_accuracy = response_output.data.angular_accuracy;
        return true;
    }
};

//-----------------------------------------------------------------------------------------------------------------

struct EdgeFollowAlgParam
{
    float acc;
    float slow_acc;
    float max_linear;
    float kp_r;
    float kp_l;
};

class SetNavigationEdgeFollowAlgParamHandler : public IRequestHandler
{
public:
    SetNavigationEdgeFollowAlgParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetNavigationEdgeFollowAlgParamHandler: " << request << std::endl;
        EdgeFollowAlgParam param;
        try
        {
            nlohmann::json j = nlohmann::json::parse(request);
            param.acc = j["acc"];
            param.slow_acc = j["slow_acc"];
            param.max_linear = j["max_linear"];
            param.kp_r = j["kp_r"];
            param.kp_l = j["kp_l"];
            if (!sendRequest(param))
            {
                LOG_ERROR("send set param request To edge follow alg Error!");
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            LOG_ERROR("JSON Parse Error: {}", ex.what());
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            LOG_ERROR("JSON Type Error: {}", ex.what());
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequest(EdgeFollowAlgParam &param)
    {
        auto client = std::make_unique<IceoryxClientMower<ob_mower_srvs::SetEdgeFollowAlgParamRequest,
                                                          ob_mower_srvs::SetEdgeFollowAlgParamResponse>>("set_navigation_edge_follow_alg_param_request");

        auto request_handler = [](const ob_mower_srvs::SetEdgeFollowAlgParamRequest &request_input,
                                  ob_mower_srvs::SetEdgeFollowAlgParamRequest &request_send) {
            request_send = request_input;
        };

        auto response_handler = [](const ob_mower_srvs::SetEdgeFollowAlgParamResponse &response_receive,
                                   ob_mower_srvs::SetEdgeFollowAlgParamResponse &response_output) -> bool {
            response_output.success = response_receive.success;
            return response_output.success;
        };

        ob_mower_srvs::SetEdgeFollowAlgParamRequest request_input;
        ob_mower_srvs::SetEdgeFollowAlgParamResponse response_output;
        request_input.data.acc = param.acc;
        request_input.data.slow_acc = param.slow_acc;
        request_input.data.max_linear = param.max_linear;
        request_input.data.kp_r = param.kp_r;
        request_input.data.kp_l = param.kp_l;
        return client->SendRequest(request_input, response_output, request_handler, response_handler);
    }
};

class GetNavigationEdgeFollowAlgParamHandler : public IRequestHandler
{
public:
    GetNavigationEdgeFollowAlgParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        EdgeFollowAlgParam param;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequest(param))
        {
            my_json j;
            j["acc"] = param.acc;
            j["slow_acc"] = param.slow_acc;
            j["max_linear"] = param.max_linear;
            j["kp_r"] = param.kp_r;
            j["kp_l"] = param.kp_l;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequest(EdgeFollowAlgParam &param)
    {
        auto client = std::make_unique<IceoryxClientMower<ob_mower_srvs::GetEdgeFollowAlgParamRequest,
                                                          ob_mower_srvs::GetEdgeFollowAlgParamResponse>>("get_navigation_edge_follow_alg_param_request");
        auto response_handler = [](const ob_mower_srvs::GetEdgeFollowAlgParamResponse &response_receive,
                                   ob_mower_srvs::GetEdgeFollowAlgParamResponse &response_output) -> bool {
            response_output = response_receive;
            return response_output.success;
        };

        ob_mower_srvs::GetEdgeFollowAlgParamRequest request_input;
        ob_mower_srvs::GetEdgeFollowAlgParamResponse response_output;
        if (!client->SendRequest(request_input, response_output, nullptr, response_handler))
        {
            return false;
        }

        param.acc = response_output.data.acc;
        param.slow_acc = response_output.data.slow_acc;
        param.max_linear = response_output.data.max_linear;
        param.kp_r = response_output.data.kp_r;
        param.kp_l = response_output.data.kp_l;
        return true;
    }
};

} // namespace fescue_iox
