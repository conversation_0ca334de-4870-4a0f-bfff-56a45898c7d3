#pragma once

#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "mower_msgs/msg/mcu_motor_speed.hpp"
#include "mower_msgs/msg/mcu_sensor.hpp"
#include "mower_msgs/msg/node_heartbeat.hpp"
#include "mower_msgs/msg/twist.hpp"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "ob_mower_srvs/device_self_check_service.h"
#include "opencv2/opencv.hpp"
#include "request_device_handler_implement.h"
#include "sensor_msgs/image__struct.h"
#include "utils/iceoryx_publisher_mower.hpp"
#include "utils/iceoryx_server_mower.hpp"
#include "utils/iceoryx_subscriber_mower.hpp"
#include "utils/thread_safe_queue.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{

struct NodeHeartbeatInfo
{
    std::string node_name;
    uint64_t pid;
    uint64_t timestamp_ms;
    NodeHeartbeatInfo() = default;
    NodeHeartbeatInfo(const std::string &node_name, uint64_t pid, uint64_t timestamp_ms)
        : node_name(node_name)
        , pid(pid)
        , timestamp_ms(timestamp_ms)
    {
    }
};

struct ImageData
{
    std::vector<uint8_t> data;
    std::string event;
    uint32_t height;
    uint32_t width;
};

class TestServerNode
{
    using iox_fusion_result_subscriber = iox::popo::Subscriber<fescue_msgs__msg__PerceptionFusionResult>;
    using iox_image_subscriber = iox::popo::Subscriber<sensor_msgs__msg__Image_iox>;

    using set_dev_selfcheck_request = fescue_iox::ob_mower_srvs::SetDeviceSelfCheckRequest;
    using set_dev_selfcheck_response = fescue_iox::ob_mower_srvs::SetDeviceSelfCheckResponse;

public:
    TestServerNode(const std::string &node_name);
    ~TestServerNode();

private:
    void InitWorkingDirectory();
    void InitParams();
    void InitSpdLog();
    void InitPublisher();
    void InitSubscriber();
    void InitService();
    void InitCameraVenc();
    void DeinitCameraVenc();
    void InitHeartbeat();
    void DeinitHeartbeat();

private:
    int getChannel(const std::string &event) const;
    void convertBGR8ToNV12(const cv::Mat &bgrImage, std::vector<uint8_t> &nv12Data);
    void convertMono8ToNV12(const uint8_t *mono8, std::vector<uint8_t> &nv12, int width, int height);
    void copyNV12ToCenter(const uint8_t *src_nv12, int src_w, int src_h, uint8_t *dst_nv12, int dst_w, int dst_h);
    void FrameVencJpeg(uint32_t chn, uint8_t *data, int width, int height, uint64_t timestamp_ms, const std::string &event);
    void processDataWithOpenCV(const sensor_msgs__msg__Image_iox &image, const std::string &event);
    void processCameraRawImage(const sensor_msgs__msg__Image_iox &image, const std::string &event);
    void processFusionBEVImage(const sensor_msgs__msg__Image_iox &image, const std::string &event);
    std::vector<uint8_t> getImageData(const sensor_msgs__msg__Image_iox &image, uint32_t &img_width, uint32_t &img_height);
    void processOtherImage(const sensor_msgs__msg__Image_iox &image, const std::string &event);
    void encodeJpeg(int channel, const ImageData &image);
    void imageEncodeWorker();
    void processNodeHeartbeat(const mower_msgs::msg::NodeHeartbeat &data, const std::string &event);
    void processFusionResult(const fescue_msgs__msg__PerceptionFusionResult &data, const std::string &event);
    void DealMcuSensor(const mower_msgs::msg::McuSensor &data, const std::string &event);
    void DealMcuMotorSpeed(const mower_msgs::msg::McuMotorSpeed &data, const std::string &event);
    static void McuSensorDataCallback(McuSensorData &data);
    static void McuMotorSpeedDataCallback(McuMotorSpeedData &data);
    bool DealDeviceSelfCheck(const fescue_iox::ob_mower_srvs::DeviceSelfCheckData &data);
    void DealDeviceSelfCheckThread();
    void PublishTwistToMCU(float linear, float angular);

private:
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>> sub_fusion_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>> sub_rgb_1280x720_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>> sub_fusion_bev_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>> sub_fusion_debug_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>> sub_segment_debug_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>> sub_object_detect_debug_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>> sub_charge_mark_detect_debug_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>> sub_qrcode_location_debug_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>> sub_mark_location_debug_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>> sub_motion_detection_debug_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>> sub_occlusion_detection_debug_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::NodeHeartbeat>> sub_node_heartbeat_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuSensor>> sub_mcu_sensor_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuMotorSpeed>> sub_mcu_motor_speed_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_dev_selfcheck_request, set_dev_selfcheck_response>> server_self_check_{nullptr};
    std::unique_ptr<IceoryxPublisherMower<mower_msgs::msg::Twist>> pub_twist_{nullptr};

    int server_port_{8080};
    std::string node_name_{""};
    // config params
    std::string log_dir_{""};
    std::string console_log_level_{""};
    std::string file_log_level_{""};
    int jpeg_quality_{50};
    int max_jpeg_channel_{7};

    std::unordered_map<int, std::string> channel_usage_;
    std::mutex mtx_;
    std::condition_variable cv_;
    std::queue<ImageData> img_queue_;  // 任务队列
    std::vector<std::thread> threads_; // 线程池

    int rms_port_{554};
    int rms_max_channel_{9};
    int rms_max_element_{1};

    std::atomic_bool thread_running_{true};
    std::mutex heartbeat_mtx_;
    std::map<std::string, NodeHeartbeatInfo> node_heartbeat_info_map_;
    std::thread heartbeat_thread_;
    std::atomic_bool heartbeat_thread_running_{true};
    static ThreadSafeQueue<McuSensorData> mcu_sensor_data_queue_;
    static ThreadSafeQueue<McuMotorSpeedData> mcu_motor_speed_data_queue_;

    fescue_iox::ob_mower_srvs::DeviceSelfCheckData dev_self_check_data_;
    std::atomic_bool self_check_thread_running_{false};
    std::unique_ptr<std::thread> deal_self_check_thread_{nullptr};
};

} // namespace fescue_iox
