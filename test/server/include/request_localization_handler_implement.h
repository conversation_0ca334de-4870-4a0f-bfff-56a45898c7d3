#pragma once

#include "fifo_map.hpp"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "iox/vector.hpp"
#include "json.hpp"
#include "ob_mower_srvs/localization_area_estimate_alg_param.h"
#include "ob_mower_srvs/localization_motion_detection_alg_param.h"
#include "ob_mower_srvs/mark_location_alg_param_service__struct.h"
#include "ob_mower_srvs/node_common_param_service.h"
#include "ob_mower_srvs/qrcode_location_alg_param_service__struct.h"
#include "request_handler_interface.h"
#include "utils/file.hpp"
#include "utils/iceoryx_client_mower.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"

namespace fescue_iox
{

using json = nlohmann::json;
using namespace nlohmann;

// A workaround to give to use fifo_map as map, we are just ignoring the 'less'
// compare
template <class K, class V, class dummy_compare, class A>
using my_workaround_fifo_map = fifo_map<K, V, fifo_map_compare<K>, A>;
using my_json = basic_json<my_workaround_fifo_map>;

using get_qrcode_location_param_client = iox::popo::Client<fescue_msgs__srv__GetQRCodeLocationAlgParam_Request, fescue_msgs__srv__GetQRCodeLocationAlgParam_Response>;
using set_qrcode_location_param_client = iox::popo::Client<fescue_msgs__srv__SetQRCodeLocationAlgParam_Request, fescue_msgs__srv__SetQRCodeLocationAlgParam_Response>;

using get_mark_location_param_client = iox::popo::Client<fescue_msgs__srv__GetMarkLocationAlgParam_Request, fescue_msgs__srv__GetMarkLocationAlgParam_Response>;
using set_mark_location_param_client = iox::popo::Client<fescue_msgs__srv__SetMarkLocationAlgParam_Request, fescue_msgs__srv__SetMarkLocationAlgParam_Response>;

struct LocMotionDetectAlgParam
{
    bool showImg;           // 是否显示图片，false
    int verbosity;          // 打印log等级，4
    int frameSkip;          // 每隔多少帧处理一次,3
    int maxCorners;         // 最大特征点数, 200
    int minCorners;         // 最大特征点数, 100
    double qualityLevel;    // 特征点质量水平, 0.01
    double minDistance;     // 特征点最小距离,10
    int blockSize;          // FAST/Harris块大小,3
    bool useHarrisDetector; // 是否使用Harris, false
    double Harris_k;        // Harris检测参数,0.04
    int maxLevel;           // 金字塔层数,3
    int winSize;            // 窗口大小, 15
    float down_sample;      // 下采样, 1
    double minDisplacement; // 判断静止时的最小平均像素偏移, 0.2
};

struct LocQRCodeAlgParam
{
    int verbosity;
    bool showImg;
    int only_use_perception;
    float percept_time_diff_thre;
    bool writeImg;
    float Perceptual_window_ratio;
    float plane_distance_threshold;

    int pnp_method;
    int aprilTagMinClusterPixels;
    float minMarkerPerimeterRate;
    float maxMarkerPerimeterRate;
    float outputRollAng;        // default: 0.1
    int cornerRefinementMethod; // default 0, range: 0~3
    bool use_bilateral_filter;  // default: false

    // remote distance QR detection setting for QR parameter
    bool use_nearby_speed_up;           // default: 0
    float minCornerDistanceRate;        // default: 0.1
    float edgeThresholdRate;            // default 0.1
    int detection_area_size;            // default 40000
    float nearbyMinMarkerPerimeterRate; // default 0.3
};

class GetQRCodeLocationParamHandler : public IRequestHandler
{
public:
    GetQRCodeLocationParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequestToNode())
        {
            my_json j;
            j["verbosity"] = param_.verbosity;
            j["showImg"] = param_.showImg;
            j["only_use_perception"] = param_.only_use_perception;
            j["percept_time_diff_thre"] = param_.percept_time_diff_thre;
            j["writeImg"] = param_.writeImg;
            j["Perceptual_window_ratio"] = param_.Perceptual_window_ratio;
            j["plane_distance_threshold"] = param_.plane_distance_threshold;
            j["pnp_method"] = param_.pnp_method;
            j["aprilTagMinClusterPixels"] = param_.aprilTagMinClusterPixels;
            j["minMarkerPerimeterRate"] = param_.minMarkerPerimeterRate;
            j["maxMarkerPerimeterRate"] = param_.maxMarkerPerimeterRate;
            j["outputRollAng"] = param_.outputRollAng;
            j["cornerRefinementMethod"] = param_.cornerRefinementMethod;
            j["use_bilateral_filter"] = param_.use_bilateral_filter;
            j["use_nearby_speed_up"] = param_.use_nearby_speed_up;
            j["minCornerDistanceRate"] = param_.minCornerDistanceRate;
            j["edgeThresholdRate"] = param_.edgeThresholdRate;
            j["detection_area_size"] = param_.detection_area_size;
            j["nearbyMinMarkerPerimeterRate"] = param_.nearbyMinMarkerPerimeterRate;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        get_qrcode_location_param_client client({kServiceGetQRCodeLocationParamRequestIox[0],
                                                 kServiceGetQRCodeLocationParamRequestIox[1],
                                                 kServiceGetQRCodeLocationParamRequestIox[2]},
                                                options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request.send().or_else([&](auto &error) { std::cout << "Could not send get detect qrcode param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) {
                    // 接收从 perception node 返回的应答
                    ret = response->success;
                    param_.verbosity = response->data.verbosity;
                    param_.showImg = response->data.showImg;
                    param_.only_use_perception = response->data.only_use_perception;
                    param_.percept_time_diff_thre = response->data.percept_time_diff_thre;
                    param_.writeImg = response->data.writeImg;
                    param_.Perceptual_window_ratio = response->data.Perceptual_window_ratio;
                    param_.plane_distance_threshold = response->data.plane_distance_threshold;
                    param_.pnp_method = response->data.pnp_method;
                    param_.aprilTagMinClusterPixels = response->data.aprilTagMinClusterPixels;
                    param_.minMarkerPerimeterRate = response->data.minMarkerPerimeterRate;
                    param_.maxMarkerPerimeterRate = response->data.maxMarkerPerimeterRate;
                    param_.outputRollAng = response->data.outputRollAng;
                    param_.cornerRefinementMethod = response->data.cornerRefinementMethod;
                    param_.use_bilateral_filter = response->data.use_bilateral_filter;
                    param_.use_nearby_speed_up = response->data.use_nearby_speed_up;
                    param_.minCornerDistanceRate = response->data.minCornerDistanceRate;
                    param_.edgeThresholdRate = response->data.edgeThresholdRate;
                    param_.detection_area_size = response->data.detection_area_size;
                    param_.nearbyMinMarkerPerimeterRate = response->data.nearbyMinMarkerPerimeterRate;
                }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    LocQRCodeAlgParam param_;
};

class SetQRCodeLocationParamHandler : public IRequestHandler
{
public:
    SetQRCodeLocationParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetQRCodeLocationParamHandler: " << request << std::endl;
        try
        {
            nlohmann::json jsonObj = nlohmann::json::parse(request);
            param_.verbosity = jsonObj["verbosity"];
            param_.showImg = jsonObj["showImg"];
            param_.only_use_perception = jsonObj["only_use_perception"];
            param_.percept_time_diff_thre = jsonObj["percept_time_diff_thre"];
            param_.writeImg = jsonObj["writeImg"];
            param_.Perceptual_window_ratio = jsonObj["Perceptual_window_ratio"];
            param_.plane_distance_threshold = jsonObj["plane_distance_threshold"];
            param_.pnp_method = jsonObj["pnp_method"];
            param_.aprilTagMinClusterPixels = jsonObj["aprilTagMinClusterPixels"];
            param_.minMarkerPerimeterRate = jsonObj["minMarkerPerimeterRate"];
            param_.maxMarkerPerimeterRate = jsonObj["maxMarkerPerimeterRate"];
            param_.outputRollAng = jsonObj["outputRollAng"];
            param_.cornerRefinementMethod = jsonObj["cornerRefinementMethod"];
            param_.use_bilateral_filter = jsonObj["use_bilateral_filter"];
            param_.use_nearby_speed_up = jsonObj["use_nearby_speed_up"];
            param_.minCornerDistanceRate = jsonObj["minCornerDistanceRate"];
            param_.edgeThresholdRate = jsonObj["edgeThresholdRate"];
            param_.detection_area_size = jsonObj["detection_area_size"];
            param_.nearbyMinMarkerPerimeterRate = jsonObj["nearbyMinMarkerPerimeterRate"];

            if (!sendRequestToNode())
            {
                std::cerr << "send set param request To DetectQRCode Error!" << std::endl;
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            std::cerr << "JSON Type Error: " << ex.what() << std::endl;
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        set_qrcode_location_param_client client({kServiceSetQRCodeLocationParamRequestIox[0],
                                                 kServiceSetQRCodeLocationParamRequestIox[1],
                                                 kServiceSetQRCodeLocationParamRequestIox[2]},
                                                options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // 发送到 perception node
                         request->data.verbosity = param_.verbosity;
                         request->data.showImg = param_.showImg;
                         request->data.only_use_perception = param_.only_use_perception;
                         request->data.percept_time_diff_thre = param_.percept_time_diff_thre;
                         request->data.writeImg = param_.writeImg;
                         request->data.Perceptual_window_ratio = param_.Perceptual_window_ratio;
                         request->data.plane_distance_threshold = param_.plane_distance_threshold;
                         request->data.pnp_method = param_.pnp_method;
                         request->data.aprilTagMinClusterPixels = param_.aprilTagMinClusterPixels;
                         request->data.minMarkerPerimeterRate = param_.minMarkerPerimeterRate;
                         request->data.maxMarkerPerimeterRate = param_.maxMarkerPerimeterRate;
                         request->data.outputRollAng = param_.outputRollAng;
                         request->data.cornerRefinementMethod = param_.cornerRefinementMethod;
                         request->data.use_bilateral_filter = param_.use_bilateral_filter;
                         request->data.use_nearby_speed_up = param_.use_nearby_speed_up;
                         request->data.minCornerDistanceRate = param_.minCornerDistanceRate;
                         request->data.edgeThresholdRate = param_.edgeThresholdRate;
                         request->data.detection_area_size = param_.detection_area_size;
                         request->data.nearbyMinMarkerPerimeterRate = param_.nearbyMinMarkerPerimeterRate;

                         request.send().or_else([&](auto &error) { std::cout << "Could not send set DetectQRCode param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) { ret = response->success; }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    LocQRCodeAlgParam param_;
};

struct MarkLocaitonAlgParam
{
    int verbosity;
    bool showImg;
    int only_use_perception;
    float percept_time_diff_thre;
    bool writeImg;
    float Perceptual_window_ratio;

    int pnp_method;
    int aprilTagMinClusterPixels;
    float minMarkerPerimeterRate;
    float maxMarkerPerimeterRate;
    float outputRollAng;        // default: 0.1
    int cornerRefinementMethod; // default 0, range: 0~3
    bool use_bilateral_filter;  // default: false

    // nearby QR parameter
    bool use_nearby_speed_up;           // default: 0
    float minCornerDistanceRate;        // default: 0.1
    float edgeThresholdRate;            // default 0.1
    int detection_area_size;            // default 40000
    float nearbyMinMarkerPerimeterRate; // default 0.3

    int markModel;
    int bucketID;
};

class GetMarkLocationParamHandler : public IRequestHandler
{
public:
    GetMarkLocationParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        if (sendRequestToNode())
        {
            my_json j;
            j["verbosity"] = param_.verbosity;
            j["showImg"] = param_.showImg;
            j["only_use_perception"] = param_.only_use_perception;
            j["percept_time_diff_thre"] = param_.percept_time_diff_thre;
            j["writeImg"] = param_.writeImg;
            j["Perceptual_window_ratio"] = param_.Perceptual_window_ratio;

            j["pnp_method"] = param_.pnp_method;
            j["aprilTagMinClusterPixels"] = param_.aprilTagMinClusterPixels;
            j["minMarkerPerimeterRate"] = param_.minMarkerPerimeterRate;
            j["maxMarkerPerimeterRate"] = param_.maxMarkerPerimeterRate;
            j["outputRollAng"] = param_.outputRollAng;
            j["cornerRefinementMethod"] = param_.cornerRefinementMethod;
            j["use_bilateral_filter"] = param_.use_bilateral_filter;

            j["use_nearby_speed_up"] = param_.use_nearby_speed_up;
            j["minCornerDistanceRate"] = param_.minCornerDistanceRate;
            j["edgeThresholdRate"] = param_.edgeThresholdRate;
            j["detection_area_size"] = param_.detection_area_size;
            j["nearbyMinMarkerPerimeterRate"] = param_.nearbyMinMarkerPerimeterRate;

            j["markModel"] = param_.markModel;
            j["bucketID"] = param_.bucketID;

            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        get_mark_location_param_client client({kServiceGetMarkLocationParamRequestIox[0],
                                               kServiceGetMarkLocationParamRequestIox[1],
                                               kServiceGetMarkLocationParamRequestIox[2]},
                                              options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // add request data
                         request.send().or_else([&](auto &error) { std::cout << "Could not send get mark location param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) {
                    // 接收从 perception node 返回的应答
                    ret = response->success;

                    param_.verbosity = response->data.verbosity;
                    param_.showImg = response->data.showImg;
                    param_.only_use_perception = response->data.only_use_perception;
                    param_.percept_time_diff_thre = response->data.percept_time_diff_thre;
                    param_.writeImg = response->data.writeImg;
                    param_.Perceptual_window_ratio = response->data.Perceptual_window_ratio;

                    param_.pnp_method = response->data.pnp_method;
                    param_.aprilTagMinClusterPixels = response->data.aprilTagMinClusterPixels;
                    param_.minMarkerPerimeterRate = response->data.minMarkerPerimeterRate;
                    param_.maxMarkerPerimeterRate = response->data.maxMarkerPerimeterRate;
                    param_.outputRollAng = response->data.outputRollAng;
                    param_.cornerRefinementMethod = response->data.cornerRefinementMethod;
                    param_.use_bilateral_filter = response->data.use_bilateral_filter;

                    param_.use_nearby_speed_up = response->data.use_nearby_speed_up;
                    param_.minCornerDistanceRate = response->data.minCornerDistanceRate;
                    param_.edgeThresholdRate = response->data.edgeThresholdRate;
                    param_.detection_area_size = response->data.detection_area_size;
                    param_.nearbyMinMarkerPerimeterRate = response->data.nearbyMinMarkerPerimeterRate;

                    param_.markModel = response->data.markModel;
                    param_.bucketID = response->data.bucketID;
                }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    MarkLocaitonAlgParam param_;
};

class SetMarkLocationParamHandler : public IRequestHandler
{
public:
    SetMarkLocationParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        std::cout << "SetMarkLocationParamHandler: " << request << std::endl;
        try
        {
            nlohmann::json j = nlohmann::json::parse(request);
            param_.verbosity = j["verbosity"];
            param_.showImg = j["showImg"];
            param_.only_use_perception = j["only_use_perception"];
            param_.percept_time_diff_thre = j["percept_time_diff_thre"];
            param_.writeImg = j["writeImg"];
            param_.Perceptual_window_ratio = j["Perceptual_window_ratio"];

            param_.pnp_method = j["pnp_method"];
            param_.aprilTagMinClusterPixels = j["aprilTagMinClusterPixels"];
            param_.minMarkerPerimeterRate = j["minMarkerPerimeterRate"];
            param_.maxMarkerPerimeterRate = j["maxMarkerPerimeterRate"];
            param_.outputRollAng = j["outputRollAng"];
            param_.cornerRefinementMethod = j["cornerRefinementMethod"];
            param_.use_bilateral_filter = j["use_bilateral_filter"];

            param_.use_nearby_speed_up = j["use_nearby_speed_up"];
            param_.minCornerDistanceRate = j["minCornerDistanceRate"];
            param_.edgeThresholdRate = j["edgeThresholdRate"];
            param_.detection_area_size = j["detection_area_size"];
            param_.nearbyMinMarkerPerimeterRate = j["nearbyMinMarkerPerimeterRate"];

            param_.markModel = j["markModel"];
            param_.bucketID = j["bucketID"];

            if (!sendRequestToNode())
            {
                std::cerr << "send set param request To MarkLocation Error!" << std::endl;
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            std::cerr << "JSON Parse Error: " << ex.what() << std::endl;
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            std::cerr << "JSON Type Error: " << ex.what() << std::endl;
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequestToNode()
    {
        bool ret = false;
        iox::popo::WaitSet<> waitset;
        iox::popo::ClientOptions options;
        options.responseQueueCapacity = 2U;
        set_mark_location_param_client client({kServiceSetMarkLocationParamRequestIox[0],
                                               kServiceSetMarkLocationParamRequestIox[1],
                                               kServiceSetMarkLocationParamRequestIox[2]},
                                              options);

        auto result = waitset.attachState(client, iox::popo::ClientState::HAS_RESPONSE);
        if (result.has_error())
        {
            auto error = result.get_error();
            std::cerr << "Failed to attach state to waitset. Error: " << static_cast<int>(error) << std::endl;
            return false;
        }

        client.loan().and_then([&](auto &request) {
                         // 发送到 perception node
                         request->data.verbosity = param_.verbosity;
                         request->data.showImg = param_.showImg;
                         request->data.only_use_perception = param_.only_use_perception;
                         request->data.percept_time_diff_thre = param_.percept_time_diff_thre;
                         request->data.writeImg = param_.writeImg;
                         request->data.Perceptual_window_ratio = param_.Perceptual_window_ratio;

                         request->data.pnp_method = param_.pnp_method;
                         request->data.aprilTagMinClusterPixels = param_.aprilTagMinClusterPixels;
                         request->data.minMarkerPerimeterRate = param_.minMarkerPerimeterRate;
                         request->data.maxMarkerPerimeterRate = param_.maxMarkerPerimeterRate;
                         request->data.outputRollAng = param_.outputRollAng;
                         request->data.cornerRefinementMethod = param_.cornerRefinementMethod;
                         request->data.use_bilateral_filter = param_.use_bilateral_filter;

                         request->data.use_nearby_speed_up = param_.use_nearby_speed_up;
                         request->data.minCornerDistanceRate = param_.minCornerDistanceRate;
                         request->data.edgeThresholdRate = param_.edgeThresholdRate;
                         request->data.detection_area_size = param_.detection_area_size;
                         request->data.nearbyMinMarkerPerimeterRate = param_.nearbyMinMarkerPerimeterRate;
                         request->data.markModel = param_.markModel;
                         request->data.bucketID = param_.bucketID;

                         request.send().or_else([&](auto &error) { std::cout << "Could not send set mark location param Request! Error: " << error << std::endl; });
                     })
            .or_else([](auto &error) { std::cout << "Could not allocate Request! Error: " << error << std::endl; });

        auto notificationVector = waitset.timedWait(iox::units::Duration::fromSeconds(2));
        for (auto &notification : notificationVector)
        {
            if (notification->doesOriginateFrom(&client))
            {
                while (client.take().and_then([&](const auto &response) { ret = response->success; }))
                {
                }
            }
            else
            {
                std::cout << "Received notification from unknown entity!" << std::endl;
                ret = false;
            }
        }

        return ret;
    }

private:
    MarkLocaitonAlgParam param_;
};

class GetLocMotionDetectAlgParamHandler : public IRequestHandler
{
public:
    GetLocMotionDetectAlgParamHandler() = default;
    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        LocMotionDetectAlgParam param;
        if (sendRequest(param))
        {
            my_json j;
            j["showImg"] = param.showImg;
            j["verbosity"] = param.verbosity;
            j["frameSkip"] = param.frameSkip;
            j["maxCorners"] = param.maxCorners;
            j["minCorners"] = param.minCorners;
            j["qualityLevel"] = param.qualityLevel;
            j["minDistance"] = param.minDistance;
            j["blockSize"] = param.blockSize;
            j["useHarrisDetector"] = param.useHarrisDetector;
            j["Harris_k"] = param.Harris_k;
            j["maxLevel"] = param.maxLevel;
            j["winSize"] = param.winSize;
            j["down_sample"] = param.down_sample;
            j["minDisplacement"] = param.minDisplacement;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequest(LocMotionDetectAlgParam &param)
    {
        auto client = std::make_unique<IceoryxClientMower<ob_mower_srvs::GetLocMotionDetectAlgParamRequest,
                                                          ob_mower_srvs::GetLocMotionDetectAlgParamResponse>>("get_localization_motion_detection_param");
        auto response_handler = [](const ob_mower_srvs::GetLocMotionDetectAlgParamResponse &response_receive,
                                   ob_mower_srvs::GetLocMotionDetectAlgParamResponse &response_output) -> bool {
            response_output = response_receive;
            return response_output.success;
        };

        ob_mower_srvs::GetLocMotionDetectAlgParamRequest request_input;
        ob_mower_srvs::GetLocMotionDetectAlgParamResponse response_output;
        if (!client->SendRequest(request_input, response_output, nullptr, response_handler))
        {
            return false;
        }
        param.verbosity = response_output.data.verbosity;
        param.showImg = response_output.data.showImg;
        param.frameSkip = response_output.data.frameSkip;
        param.maxCorners = response_output.data.maxCorners;
        param.minCorners = response_output.data.minCorners;
        param.qualityLevel = response_output.data.qualityLevel;
        param.minDistance = response_output.data.minDistance;
        param.blockSize = response_output.data.blockSize;
        param.useHarrisDetector = response_output.data.useHarrisDetector;
        param.Harris_k = response_output.data.Harris_k;
        param.maxLevel = response_output.data.maxLevel;
        param.winSize = response_output.data.winSize;
        param.down_sample = response_output.data.down_sample;
        param.minDisplacement = response_output.data.minDisplacement;
        return true;
    }
};

class SetLocMotionDetectAlgParamHandler : public IRequestHandler
{
public:
    SetLocMotionDetectAlgParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        LocMotionDetectAlgParam param;
        std::cout << "SetLocMotionDetectAlgParamHandler: " << request << std::endl;
        try
        {
            nlohmann::json j = nlohmann::json::parse(request);
            param.showImg = j["showImg"];
            param.verbosity = j["verbosity"];
            param.frameSkip = j["frameSkip"];
            param.maxCorners = j["maxCorners"];
            param.minCorners = j["minCorners"];
            param.qualityLevel = j["qualityLevel"];
            param.minDistance = j["minDistance"];
            param.blockSize = j["blockSize"];
            param.useHarrisDetector = j["useHarrisDetector"];
            param.Harris_k = j["Harris_k"];
            param.maxLevel = j["maxLevel"];
            param.winSize = j["winSize"];
            param.down_sample = j["down_sample"];
            param.minDisplacement = j["minDisplacement"];
            if (!sendRequest(param))
            {
                LOG_ERROR("send set param request To motion detection Node Error!");
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            LOG_ERROR("JSON Parse Error: {}", ex.what());
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            LOG_ERROR("JSON Type Error: {}", ex.what());
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequest(const LocMotionDetectAlgParam &param)
    {
        auto client = std::make_unique<IceoryxClientMower<ob_mower_srvs::SetLocMotionDetectAlgParamRequest,
                                                          ob_mower_srvs::SetLocMotionDetectAlgParamResponse>>("set_localization_motion_detection_param");
        auto request_handler = [](const ob_mower_srvs::SetLocMotionDetectAlgParamRequest &request_input,
                                  ob_mower_srvs::SetLocMotionDetectAlgParamRequest &request_send) {
            request_send = request_input;
        };
        auto response_handler = [](const ob_mower_srvs::SetLocMotionDetectAlgParamResponse &response_receive,
                                   ob_mower_srvs::SetLocMotionDetectAlgParamResponse &response_output) -> bool {
            response_output = response_receive;
            return response_output.success;
        };
        ob_mower_srvs::SetLocMotionDetectAlgParamRequest request_input;
        ob_mower_srvs::SetLocMotionDetectAlgParamResponse response_output;
        request_input.timestamp_ms = GetTimestampMs();
        request_input.data.verbosity = param.verbosity;
        request_input.data.showImg = param.showImg;
        request_input.data.frameSkip = param.frameSkip;
        request_input.data.maxCorners = param.maxCorners;
        request_input.data.minCorners = param.minCorners;
        request_input.data.qualityLevel = param.qualityLevel;
        request_input.data.minDistance = param.minDistance;
        request_input.data.blockSize = param.blockSize;
        request_input.data.useHarrisDetector = param.useHarrisDetector;
        request_input.data.Harris_k = param.Harris_k;
        request_input.data.maxLevel = param.maxLevel;
        request_input.data.winSize = param.winSize;
        request_input.data.down_sample = param.down_sample;
        request_input.data.minDisplacement = param.minDisplacement;
        return client->SendRequest(request_input, response_output, request_handler, response_handler);
    }
};

struct LocAreaEstimateAlgParam
{
    bool saveResult;         // default:0
    bool outputLog;          // default:0
    int useImuMode;          // default:3
    double bodyCompensation; // default:0.165
};

class GetLocAreaEstimateAlgParamHandler : public IRequestHandler
{
public:
    GetLocAreaEstimateAlgParamHandler() = default;
    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        (void)request;
        ResponseContent content{""};
        ResponseStatus status{"400"};
        LocAreaEstimateAlgParam param;
        if (sendRequest(param))
        {
            my_json j;
            j["saveResult"] = param.saveResult;
            j["outputLog"] = param.outputLog;
            j["useImuMode"] = param.useImuMode;
            j["bodyCompensation"] = param.bodyCompensation;
            status = "200";
            content = j.dump(2);
        }
        return std::make_pair(status, content);
    }
    bool sendRequest(LocAreaEstimateAlgParam &param)
    {
        auto client = std::make_unique<IceoryxClientMower<ob_mower_srvs::GetLocAreaEstimateAlgParamRequest,
                                                          ob_mower_srvs::GetLocAreaEstimateAlgParamResponse>>("get_localization_area_estimation_alg_param");
        auto response_handler = [](const ob_mower_srvs::GetLocAreaEstimateAlgParamResponse &response_receive,
                                   ob_mower_srvs::GetLocAreaEstimateAlgParamResponse &response_output) -> bool {
            response_output = response_receive;
            return response_output.success;
        };

        ob_mower_srvs::GetLocAreaEstimateAlgParamRequest request_input;
        ob_mower_srvs::GetLocAreaEstimateAlgParamResponse response_output;
        if (!client->SendRequest(request_input, response_output, nullptr, response_handler))
        {
            return false;
        }
        param.saveResult = response_output.data.saveResult;
        param.outputLog = response_output.data.outputLog;
        param.useImuMode = response_output.data.useImuMode;
        param.bodyCompensation = response_output.data.bodyCompensation;
        return true;
    }
};

class SetLocAreaEstimateAlgParamHandler : public IRequestHandler
{
public:
    SetLocAreaEstimateAlgParamHandler() = default;

    std::pair<ResponseStatus, ResponseContent> handle(const RequestContent &request) override
    {
        ResponseContent content{""};
        ResponseStatus status{"200"};
        LocAreaEstimateAlgParam param;
        std::cout << "SetLocAreaEstimateAlgParamHandler: " << request << std::endl;
        try
        {
            nlohmann::json j = nlohmann::json::parse(request);
            param.saveResult = j["saveResult"];
            param.outputLog = j["outputLog"];
            param.useImuMode = j["useImuMode"];
            param.bodyCompensation = j["bodyCompensation"];
            if (!sendRequest(param))
            {
                LOG_ERROR("send set param request To Area Estimate alg Error!");
                status = "400";
            }
        }
        catch (const nlohmann::json::parse_error &ex)
        {
            LOG_ERROR("JSON Parse Error: {}", ex.what());
            status = "400";
        }
        catch (const nlohmann::json::type_error &ex)
        {
            LOG_ERROR("JSON Type Error: {}", ex.what());
            status = "400";
        }
        return std::make_pair(status, content);
    }
    bool sendRequest(const LocAreaEstimateAlgParam &param)
    {
        auto client = std::make_unique<IceoryxClientMower<ob_mower_srvs::SetLocAreaEstimateAlgParamRequest,
                                                          ob_mower_srvs::SetLocAreaEstimateAlgParamResponse>>("set_localization_area_estimation_alg_param");
        auto request_handler = [](const ob_mower_srvs::SetLocAreaEstimateAlgParamRequest &request_input,
                                  ob_mower_srvs::SetLocAreaEstimateAlgParamRequest &request_send) {
            request_send = request_input;
        };
        auto response_handler = [](const ob_mower_srvs::SetLocAreaEstimateAlgParamResponse &response_receive,
                                   ob_mower_srvs::SetLocAreaEstimateAlgParamResponse &response_output) -> bool {
            response_output = response_receive;
            return response_output.success;
        };
        ob_mower_srvs::SetLocAreaEstimateAlgParamRequest request_input;
        ob_mower_srvs::SetLocAreaEstimateAlgParamResponse response_output;
        request_input.data.saveResult = param.saveResult;
        request_input.data.outputLog = param.outputLog;
        request_input.data.useImuMode = param.useImuMode;
        request_input.data.bodyCompensation = param.bodyCompensation;
        return client->SendRequest(request_input, response_output, request_handler, response_handler);
    }
};

} // namespace fescue_iox
