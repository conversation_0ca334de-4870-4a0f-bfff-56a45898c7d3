#include "test_server_node.h"

#include "Obvideo/ImuCamera.h"
#include "librms.h"
#include "mower_sdk_version.h"
#include "request_device_handler_implement.h"
#include "test_server.h"
#include "test_server_node_config.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"

#include <cmath>      // for std::labs()
#include <filesystem> //c++17

namespace fescue_iox
{

static std::unordered_map<std::string, int> EventChannelMap_ = {
    {"camera_color_1280x720_result", 0},
    {"fusion_mask_image", 1},
    {"fusion_debug_image", 2},
    {"segmenter_color_mask", 3},
    {"detect_object_color_image", 4},
    {"charge_mark_detect_image", 5},
    {"detect_qrcode_color_image", 6},
    {"mark_location_image", 7},
    {"localization_motion_detection_image", 8},
    {"occlusion_detect_image", 9},
};

ThreadSafeQueue<McuSensorData> TestServerNode::mcu_sensor_data_queue_{2};          // Initialize with capacity of 2
ThreadSafeQueue<McuMotorSpeedData> TestServerNode::mcu_motor_speed_data_queue_{2}; // Initialize with capacity of 2

TestServerNode::TestServerNode(const std::string &node_name)
    : node_name_(node_name)
{
    InitWorkingDirectory();
    InitParams();
    InitSpdLog();
    InitCameraVenc();
    InitSubscriber();
    InitPublisher();
    InitService();
    InitHeartbeat();
    RunMowerTestServer(server_port_, &TestServerNode::McuSensorDataCallback, &TestServerNode::McuMotorSpeedDataCallback);
    RMS_Start(rms_port_, rms_max_channel_, rms_max_element_);
}

TestServerNode::~TestServerNode()
{
    LOG_WARN("TestServerNode exit start!");
    self_check_thread_running_.store(false);
    if (deal_self_check_thread_ &&
        deal_self_check_thread_->joinable())
    {
        deal_self_check_thread_->join();
    }
    DeinitHeartbeat();
    RMS_Stop();
    StopMowerTestServer();
    DeinitCameraVenc();
    LOG_WARN("TestServerNode exit ok!");
}

void TestServerNode::InitCameraVenc()
{
    CamVencParam_t param;
    param.type = VENC_MJPEG_TYPE;
    param.fps = 15;
    param.mjpgQp = jpeg_quality_;

    for (int i = 0; i < max_jpeg_channel_; i++)
    {
        param.chn = i;
        if (i == 0)
        {
            param.width = 1280;
            param.height = 720;
        }
        else
        {
            param.width = 640;
            param.height = 360;
        }
        ImuCameraVencInit(param);
        // ImuCameraVencStartFrame(i);
    }

    // 启动线程池
    for (int i = 2; i < max_jpeg_channel_; ++i)
    {
        threads_.emplace_back(&TestServerNode::imageEncodeWorker, this);
    }
}

void TestServerNode::DeinitCameraVenc()
{
    for (int i = 0; i < max_jpeg_channel_; i++)
    {
        ImuCameraVencStopFrame(i);
        ImuCameraVencDeInit(i);
    }

    thread_running_.store(false);
    cv_.notify_all();
    for (auto &th : threads_)
    {
        if (th.joinable())
        {
            th.join();
        }
    }
}

void TestServerNode::InitHeartbeat()
{
    heartbeat_thread_ = std::thread([&] {
        while (heartbeat_thread_running_.load())
        {
            {
                std::lock_guard<std::mutex> lock(heartbeat_mtx_);
                for (const auto &it : node_heartbeat_info_map_)
                {
                    if (GetTimestampMs() - it.second.timestamp_ms > 5000)
                    {
                        LOG_WARN("node {} pid {} heartbeat timeout!",
                                 it.second.node_name.c_str(), it.second.pid);
                    }
                }
            }
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    });
}

void TestServerNode::DeinitHeartbeat()
{
    heartbeat_thread_running_.store(false);
    if (heartbeat_thread_.joinable())
    {
        heartbeat_thread_.join();
    }
}

void TestServerNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void TestServerNode::InitParams()
{
    const std::string conf_file{"conf/test_server_node/test_server_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("Test node create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("Test node create config path failed!!!");
        }
    }
    if (!Config<TestServerNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init Test node config parameters failed!");
    }
    TestServerNodeConfig config = Config<TestServerNodeConfig>::GetConfig();
    LOG_INFO("{}", config.toString().c_str());
    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    server_port_ = config.server_port;
    jpeg_quality_ = config.jpeg_quality;
    rms_max_channel_ = EventChannelMap_.size();
    LOG_INFO("************ rms_max_channel_: {} ********************", rms_max_channel_);
    rms_max_element_ = config.max_element;
    if (rms_max_element_ < 1)
    {
        rms_max_element_ = 1;
    }
    if (!Config<TestServerNodeConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set Test node config parameters failed!");
    }
    CreateDirectories(log_dir_);
    CreateDirectories(log_dir_ + "/" + node_name_);
}

void TestServerNode::InitSpdLog()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void TestServerNode::InitPublisher()
{
    pub_twist_ = std::make_unique<IceoryxPublisherMower<mower_msgs::msg::Twist>>("mcu_twist");
}

void TestServerNode::InitSubscriber()
{
    sub_fusion_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>>(
        "fusion_result", 10, [this](const fescue_msgs__msg__PerceptionFusionResult &data, const std::string &event) {
            processFusionResult(data, event);
        });

    sub_rgb_1280x720_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "camera_color_1280x720_result", 1, [this](const sensor_msgs__msg__Image_iox &data, const std::string &event) {
            processCameraRawImage(data, event);
        });

    sub_fusion_bev_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "fusion_mask_image", 1, [this](const sensor_msgs__msg__Image_iox &data, const std::string &event) {
            processFusionBEVImage(data, event);
        });

    sub_fusion_debug_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "fusion_debug_image", 1, [this](const sensor_msgs__msg__Image_iox &data, const std::string &event) {
            processOtherImage(data, event);
        });

    sub_segment_debug_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "segmenter_color_mask", 1, [this](const sensor_msgs__msg__Image_iox &data, const std::string &event) {
            processOtherImage(data, event);
        });

    sub_object_detect_debug_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "detect_object_color_image", 1, [this](const sensor_msgs__msg__Image_iox &data, const std::string &event) {
            processOtherImage(data, event);
        });

    sub_charge_mark_detect_debug_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "charge_mark_detect_image", 1, [this](const sensor_msgs__msg__Image_iox &data, const std::string &event) {
            processOtherImage(data, event);
        });

    sub_qrcode_location_debug_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "detect_qrcode_color_image", 1, [this](const sensor_msgs__msg__Image_iox &data, const std::string &event) {
            processOtherImage(data, event);
        });

    sub_mark_location_debug_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "mark_location_image", 1, [this](const sensor_msgs__msg__Image_iox &data, const std::string &event) {
            processOtherImage(data, event);
        });

    sub_motion_detection_debug_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "localization_motion_detection_image", 1, [this](const sensor_msgs__msg__Image_iox &data, const std::string &event) {
            processOtherImage(data, event);
        });

    sub_occlusion_detection_debug_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "occlusion_detect_image", 1, [this](const sensor_msgs__msg__Image_iox &data, const std::string &event) {
            processOtherImage(data, event);
        });

    sub_node_heartbeat_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::NodeHeartbeat>>(
        "node_heartbeat", 10, [this](const mower_msgs::msg::NodeHeartbeat &data, const std::string &event) {
            processNodeHeartbeat(data, event);
        });

    sub_mcu_sensor_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuSensor>>(
        "mcu_sensor", 1, [this](const mower_msgs::msg::McuSensor &data, const std::string &event) {
            DealMcuSensor(data, event);
        });

    sub_mcu_motor_speed_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuMotorSpeed>>(
        "mcu_motor_speed", 1, [this](const mower_msgs::msg::McuMotorSpeed &data, const std::string &event) {
            DealMcuMotorSpeed(data, event);
        });
}

void TestServerNode::InitService()
{
    server_self_check_ = std::make_unique<IceoryxServerMower<set_dev_selfcheck_request, set_dev_selfcheck_response>>(
        "set_device_selfcheck_request", 10U, [this](const set_dev_selfcheck_request &request, set_dev_selfcheck_response &response) {
            response.success = DealDeviceSelfCheck(request.data);
            LOG_INFO("Set device self check execute {}", response.success);
        });
}

bool TestServerNode::DealDeviceSelfCheck(const fescue_iox::ob_mower_srvs::DeviceSelfCheckData &data)
{
    LOG_INFO("Deal device self check linear {}, angular {}, duration_ms {}", data.linear, data.angular, data.duration_ms);
    if (deal_self_check_thread_)
    {
        self_check_thread_running_.store(false);
        if (deal_self_check_thread_->joinable())
        {
            deal_self_check_thread_->join();
            deal_self_check_thread_.reset();
        }
    }
    if (data.duration_ms > 0)
    {
        dev_self_check_data_ = data;
        self_check_thread_running_.store(true);
        deal_self_check_thread_ = std::make_unique<std::thread>(std::bind(&TestServerNode::DealDeviceSelfCheckThread, this));
    }
    return true;
}

void TestServerNode::PublishTwistToMCU(float linear, float angular)
{
    if (pub_twist_)
    {
        mower_msgs::msg::Twist twist;
        twist.timestamp = GetTimestampMs();
        twist.linear_velocity = linear;
        twist.angular_velocity = angular;
        LOG_DEBUG("Device self checl publish twist {} {}", twist.linear_velocity, twist.angular_velocity);
        pub_twist_->publishCopyOf(twist);
    }
}

void TestServerNode::DealDeviceSelfCheckThread()
{
    LOG_WARN("Device self check thread start......");

    // send Test Mode Entry
    TestModeData mode;
    auto handler = std::make_unique<SetTestModeHandler>();
    mode.mode = 0;
    mode.timestamp_ms = GetTimestampMs();
    LOG_INFO("send test mode Entry!");
    if (!handler->sendRequest(mode))
    {
        LOG_ERROR("Send Test mode Entry to MCU fail!");
        self_check_thread_running_.store(false);
        return;
    }

    auto start = GetTimestampMs();
    while (GetTimestampMs() - start < dev_self_check_data_.duration_ms)
    {
        if (!self_check_thread_running_.load())
        {
            break;
        }
        PublishTwistToMCU(dev_self_check_data_.linear, dev_self_check_data_.angular);
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    // send stop
    LOG_INFO("send stop!");
    PublishTwistToMCU(0, 0);

    // send Test Mode Exit
    mode.mode = 1;
    mode.timestamp_ms = GetTimestampMs();
    LOG_INFO("send test mode Exit!");
    if (!handler->sendRequest(mode))
    {
        LOG_ERROR("Send Test mode Exit to MCU fail!");
        self_check_thread_running_.store(false);
    }

    LOG_WARN("Device self check thread exit......");
}

void TestServerNode::DealMcuSensor(const mower_msgs::msg::McuSensor &data, const std::string &event)
{
    (void)event;
    McuSensorData mcu_sensor_data;
    mcu_sensor_data.lift_left = data.lift_left;
    mcu_sensor_data.lift_right = data.lift_right;
    mcu_sensor_data.collision_left = data.collision_left;
    mcu_sensor_data.collision_right = data.collision_right;
    mcu_sensor_data.emergency_stop = data.emergency_stop;
    mcu_sensor_data.charge_terminal_status = data.charge_terminal_status;
    mcu_sensor_data.blade_status = data.blade_status;
    mcu_sensor_data.battery_value = data.battery_value;
    mcu_sensor_data_queue_.push(std::move(mcu_sensor_data));
}

void TestServerNode::DealMcuMotorSpeed(const mower_msgs::msg::McuMotorSpeed &data, const std::string &event)
{
    (void)event;
    McuMotorSpeedData motor_speed_data;
    motor_speed_data.motor_speed_left = data.motor_speed_left;
    motor_speed_data.motor_speed_right = data.motor_speed_right;
    motor_speed_data.motor_current_left = data.motor_current_left;
    motor_speed_data.motor_current_right = data.motor_current_right;
    mcu_motor_speed_data_queue_.push(std::move(motor_speed_data));
}

void TestServerNode::McuSensorDataCallback(McuSensorData &data)
{
    mcu_sensor_data_queue_.pop_wait(data);
}

void TestServerNode::McuMotorSpeedDataCallback(McuMotorSpeedData &data)
{
    mcu_motor_speed_data_queue_.pop_wait(data);
}

void TestServerNode::processFusionResult(const fescue_msgs__msg__PerceptionFusionResult &data, const std::string &event)
{
    (void)event;
    uint64_t timestamp = GetTimestampMs();
    LOG_DEBUG("TestServerNode fusion_result: {} system_time: {} diff_ms: {}", data.timestamp, timestamp, timestamp - data.timestamp);
    uint64_t transfer_time = timestamp - data.output_timestamp;
    uint64_t algo_time = data.output_timestamp - data.timestamp;
    if (transfer_time >= 100)
    {
        LOG_ERROR("TestServerNode fusion_result transfer cost time {}ms", transfer_time);
    }
    if (algo_time >= 500)
    {
        LOG_ERROR("TestServerNode fusion_result algorithms cost time {}ms", algo_time);
    }
}

int TestServerNode::getChannel(const std::string &event) const
{
    auto it = EventChannelMap_.find(event);
    if (it != EventChannelMap_.end())
    {
        return it->second;
    }
    return -1;
}

void TestServerNode::convertBGR8ToNV12(const cv::Mat &bgrImage, std::vector<uint8_t> &nv12Data)
{
    // Step 1: Convert BGR to YUV420
    cv::Mat yuvImage;
    cv::cvtColor(bgrImage, yuvImage, cv::COLOR_BGR2YUV_I420);

    // Get image dimensions
    int width = bgrImage.cols;
    int height = bgrImage.rows;

    // Step 2: Reshape YUV420 data to NV12 format
    nv12Data.clear();
    nv12Data.resize(width * height * 3 / 2); // NV12 size: width * height * 1.5

    // Get pointers to Y, U, and V planes in YUV420
    const uint8_t *yuvPtr = yuvImage.ptr<uint8_t>();
    const uint8_t *yPlane = yuvPtr;                                         // Y plane
    const uint8_t *uPlane = yuvPtr + width * height;                        // U plane
    const uint8_t *vPlane = yuvPtr + width * height + (width * height / 4); // V plane

    // Copy Y plane to NV12
    std::memcpy(nv12Data.data(), yPlane, width * height);

    // Interleave U and V planes for NV12
    uint8_t *uvPlane = nv12Data.data() + width * height; // UV plane starts after Y
    for (int i = 0; i < width * height / 4; ++i)
    {
        uvPlane[2 * i] = uPlane[i];     // U
        uvPlane[2 * i + 1] = vPlane[i]; // V
    }
}

void TestServerNode::convertMono8ToNV12(const uint8_t *mono8, std::vector<uint8_t> &nv12, int width, int height)
{
    // Y 平面大小
    int y_size = width * height;
    // UV 平面大小
    int uv_size = y_size / 2;

    // 调整 NV12 缓冲区大小
    nv12.resize(y_size + uv_size);

    // 复制 Y 平面
    for (int i = 0; i < y_size; ++i)
    {
        nv12[i] = mono8[i];
    }

    // 填充 UV 平面为 128
    for (int i = y_size; i < y_size + uv_size; ++i)
    {
        nv12[i] = 128;
    }
}

void TestServerNode::copyNV12ToCenter(const uint8_t *src_nv12, int src_w, int src_h, uint8_t *dst_nv12, int dst_w, int dst_h)
{
    int offsetX = (dst_w - src_w) / 2;
    int offsetY = (dst_h - src_h) / 2;

    // 计算 NV12 平面的起始位置
    uint8_t *dst_y_plane = dst_nv12;
    uint8_t *dst_uv_plane = dst_nv12 + dst_w * dst_h;

    const uint8_t *src_y_plane = src_nv12;
    const uint8_t *src_uv_plane = src_nv12 + src_w * src_h;

    // 复制 Y 平面
    for (int j = 0; j < src_h; j++)
    {
        memcpy(dst_y_plane + (offsetY + j) * dst_w + offsetX,
               src_y_plane + j * src_w,
               src_w);
    }

    // 复制 UV 平面（NV12 是 YUV420，UV 采样率是 2x2）
    int uv_offsetY = offsetY / 2; // UV 行偏移
    int uv_offsetX = offsetX;     // UV 列偏移

    for (int j = 0; j < src_h / 2; j++)
    {
        memcpy(dst_uv_plane + (uv_offsetY + j) * dst_w + uv_offsetX,
               src_uv_plane + j * src_w,
               src_w);
    }
}

void TestServerNode::FrameVencJpeg(uint32_t chn, uint8_t *data, int width, int height, uint64_t timestamp_ms, const std::string &event)
{
    ImuCamFrameInfo_t frame_info;
    frame_info.yAddr = (char *)data;
    frame_info.ySize = width * height;
    frame_info.uvAddr = (char *)(data + width * height);
    frame_info.uvSize = width * height / 2;

    ImuCameraVencStartFrame(chn);
    if (ImuCameraVencSendFrame(chn, frame_info) == 0)
    {
        ImuCamFrameInfo_t out_frame;
        if (ImuCameraVencGetFrame(chn, &out_frame) == 0)
        {
            LOG_DEBUG("ImuCameraVencGetFrame {} ok size {}!", event.c_str(), (int)(out_frame.ySize));
            int channel = getChannel(event);
            if (channel != -1)
            {
                RMS_SendFrame(channel, out_frame.yAddr, out_frame.ySize, width, height, timestamp_ms);
            }
            else
            {
                LOG_ERROR("Find event {} channel id fail!!", event.c_str());
            }
            ImuCameraVencReleaseFrame(chn);
        }
        else
        {
            LOG_INFO("ImuCameraVencGetFrame fail");
        }
    }
    else
    {
        LOG_INFO("ImuCameraVencSendFrame fail");
    }
    ImuCameraVencStopFrame(chn);
}

void TestServerNode::processDataWithOpenCV(const sensor_msgs__msg__Image_iox &image, const std::string &event)
{
    std::string encoding = std::string(image.encoding.c_str());
    cv::Mat bgr_img;
    LOG_DEBUG("event is {} encoding {} with {} height {}", event.c_str(), encoding.c_str(), image.width, image.height);
    if (encoding == "nv12")
    {
        NV12ToMat(bgr_img, image.data.data(), image.width, image.height);
    }
    else if (encoding == "bgr8")
    {
        bgr_img = cv::Mat(image.height, image.width, CV_8UC3, const_cast<uint8_t *>(image.data.data()));
    }
    else if (encoding == "mono8")
    {
        bgr_img = cv::Mat(image.height, image.width, CV_8UC1, const_cast<uint8_t *>(image.data.data()));
    }

    if (bgr_img.empty())
    {
        return;
    }

    std::vector<uchar> encoded_data;
    std::vector<int> compression_params = {cv::IMWRITE_JPEG_QUALITY, 90}; // 设置JPEG压缩质量（0-100）
    if (!cv::imencode(".jpg", bgr_img, encoded_data, compression_params))
    {
        LOG_ERROR("Failed to encode image!");
        return;
    }

    int channel = getChannel(event);
    if (channel != -1)
    {
        uint64_t timestamp_ms = image.header.stamp.sec * 1000;
        RMS_SendFrame(channel, reinterpret_cast<char *>(encoded_data.data()), encoded_data.size(), image.width, image.height, timestamp_ms);
    }
    else
    {
        LOG_ERROR("Find event {} channel id fail!!", event.c_str());
    }
}

void TestServerNode::processCameraRawImage(const sensor_msgs__msg__Image_iox &image, const std::string &event)
{
    int encode_channel = 0;
    FrameVencJpeg(encode_channel, (uint8_t *)image.data.data(), image.width, image.height, time(0), event);
}

void TestServerNode::processFusionBEVImage(const sensor_msgs__msg__Image_iox &image, const std::string &event)
{
    // 1. convert image to nv12
    std::vector<uint8_t> nv12Data;
    convertMono8ToNV12(image.data.data(), nv12Data, image.width, image.height);
    // 2. resize image
    int dst_w = 640, dst_h = 360;
    std::vector<uint8_t> dst_nv12(dst_w * dst_h * 3 / 2, 128); // 目标图像初始化为灰色
    copyNV12ToCenter(nv12Data.data(), image.width, image.height, dst_nv12.data(), dst_w, dst_h);
    // 3. encode image
    int encode_channel = 1;
    FrameVencJpeg(encode_channel, dst_nv12.data(), dst_w, dst_h, time(0), event);
}

std::vector<uint8_t> TestServerNode::getImageData(const sensor_msgs__msg__Image_iox &image, uint32_t &img_width, uint32_t &img_height)
{
    std::vector<uint8_t> nv12Data;

    std::string encoding = std::string(image.encoding.c_str());
    if (encoding == "bgr8")
    {
        cv::Mat bgr_img;
        bgr_img = cv::Mat(image.height, image.width, CV_8UC3, const_cast<uint8_t *>(image.data.data()));
        convertBGR8ToNV12(bgr_img, nv12Data);
    }
    else if (encoding == "mono8")
    {
        convertMono8ToNV12(image.data.data(), nv12Data, image.width, image.height);
    }

    if (image.width % 16 == 0 && image.height % 16 == 0)
    {
        img_width = image.width;
        img_height = image.height;
        return nv12Data;
    }
    else
    {
        int dst_w = 640, dst_h = 360;
        std::vector<uint8_t> dst_nv12(dst_w * dst_h * 3 / 2, 128); // 目标图像初始化为灰色
        copyNV12ToCenter(nv12Data.data(), image.width, image.height, dst_nv12.data(), dst_w, dst_h);
        img_width = dst_w;
        img_height = dst_h;
        return dst_nv12;
    }
}

void TestServerNode::processOtherImage(const sensor_msgs__msg__Image_iox &image, const std::string &event)
{
    ImageData img_data;
    img_data.data = getImageData(image, img_data.width, img_data.height);
    if (!img_data.data.empty())
    {
        img_data.event = event;
        {
            std::lock_guard<std::mutex> lock(mtx_);
            if (static_cast<int>(img_queue_.size()) >= max_jpeg_channel_ - 2)
            {
                img_queue_.pop();
                LOG_WARN("img_queue_ FULL! Dropping oldest frame");
            }
            img_queue_.push(std::move(img_data));
        }
        cv_.notify_one(); // 唤醒一个线程
    }
    else
    {
        LOG_ERROR("getImageData ERROR, event: {}", event);
    }
}

void TestServerNode::encodeJpeg(int channel, const ImageData &image)
{
    FrameVencJpeg(channel, const_cast<uint8_t *>(image.data.data()), image.width, image.height, time(0), image.event);
    std::lock_guard<std::mutex> lock(mtx_);
    channel_usage_[channel] = "";
}

void TestServerNode::imageEncodeWorker()
{
    srand(time(nullptr));

    while (thread_running_.load())
    {
        ImageData img;
        int selected_channel = -1;

        {
            // 1. wait for image
            std::unique_lock<std::mutex> lock(mtx_);
            cv_.wait(lock, [this] { return !img_queue_.empty() || !thread_running_; });

            if (!thread_running_)
                return;

            // 2. get free img encode channel
            std::vector<int> free_channels;
            for (int i = 2; i < max_jpeg_channel_; ++i)
            {
                if (channel_usage_[i].empty())
                {
                    free_channels.push_back(i);
                }
            }

            // 3. get img
            if (!img_queue_.empty())
            {
                img = std::move(img_queue_.front());
                img_queue_.pop();
                if (!free_channels.empty())
                {
                    selected_channel = free_channels[rand() % free_channels.size()];
                }
                else
                {
                    selected_channel = 2 + rand() % 5; // 选择 2~6 之间的通道
                }
                channel_usage_[selected_channel] = img.event;
            }
        }

        // 4. encode image
        if (selected_channel != -1)
        {
            encodeJpeg(selected_channel, img);
        }
    }
}

void TestServerNode::processNodeHeartbeat(const mower_msgs::msg::NodeHeartbeat &data, const std::string &event)
{
    (void)event;
    std::lock_guard<std::mutex> lock(heartbeat_mtx_);
    std::string node_name = std::string(data.node_name.c_str());
    auto it = node_heartbeat_info_map_.find(node_name);
    if (it == node_heartbeat_info_map_.end())
    {
        node_heartbeat_info_map_[node_name] = NodeHeartbeatInfo(node_name, data.pid, data.timestamp);
    }
    else
    {
        it->second.timestamp_ms = data.timestamp;
    }
}

} // namespace fescue_iox
