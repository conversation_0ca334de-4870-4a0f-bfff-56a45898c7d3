cmake_minimum_required(VERSION 3.16)
project(mono_mower_sdk_iceoryx C CXX)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_C_STANDARD 11)

# set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC -O3")
# set(CMAKE_C_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG}  -fPIC -g -O3")
# set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC -O3")
# #除CMAKE_CXX_FLAGS外，debug版本的额外编译器参数
# set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -fPIC -g -O3")
# #除CMAKE_CXX_FLAGS外，release版本的额外编译器参数
# set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE}")
# set(CMAKE_BUILD_TYPE "Release")
# set(CMAKE_BUILD_TYPE "Debug")

#输出详细的编译和链接信息
#set(CMAKE_VERBOSE_MAKEFILE ON)
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU" OR CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    add_compile_options(-Wall -Wextra -Wpedantic -Werror)
endif()

add_compile_options(-rdynamic)

set(THIRDPARTY_INSTALL_DIR "mower_common_lib" CACHE STRING "mower 3rdparty libs install dir")
set(SOFTWARE_INSTALL_DIR "mower_algorithm" CACHE STRING "mower algorithm execute install dir")

include(cmake/prebuild.cmake)
message(STATUS " <<<<<<<<<<<<< ImuCameraDriver configuration: >>>>>>>>>>>>>")
message(STATUS "CAMERA_NAME: " ${CAMERA_NAME})
message(STATUS "HOST_PLATFORM: " ${HOST_PLATFORM})
message(STATUS "SOC_NAME: " ${SOC_NAME})
message(STATUS "CMAKE_TOOLCHAIN_FILE: " ${CMAKE_TOOLCHAIN_FILE})
message(STATUS " <<<<<<<<<<<<<< ImuCameraDriver configuration >>>>>>>>>>>>>>\n\n")

execute_process(
    COMMAND git rev-list --tags --max-count=1
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE GIT_COMMIT_ID
    OUTPUT_STRIP_TRAILING_WHITESPACE
)
execute_process(
    COMMAND git describe --tags ${GIT_COMMIT_ID}
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE GIT_TAG
    OUTPUT_STRIP_TRAILING_WHITESPACE
)
execute_process(
    COMMAND git log -1 --format=%H
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE GIT_VERSION
    OUTPUT_STRIP_TRAILING_WHITESPACE
)
execute_process(
    COMMAND date +"%Y-%m-%d %H:%M:%S"
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE COMPILE_TIME
    OUTPUT_STRIP_TRAILING_WHITESPACE
)
SET(PROJECT_PATH  "${CMAKE_CURRENT_SOURCE_DIR}" CACHE PATH "root path")
SET(PROJECT_NAME  "${PROJECT_NAME}" CACHE PATH "project path")
configure_file (
  "${CMAKE_CURRENT_SOURCE_DIR}/mower_sdk_version.h.in"
  "${CMAKE_CURRENT_BINARY_DIR}/mower_sdk_version.h"
)

#公共库和头文件查找路径配置
include_directories(${CMAKE_BINARY_DIR}/)
include_directories(${PROJECT_SOURCE_DIR}/)
include_directories(${PROJECT_SOURCE_DIR}/fescue_msgs/)
include_directories(${PROJECT_SOURCE_DIR}/fescue_msgs/header/)
include_directories(${PROJECT_SOURCE_DIR}/common/utils/include)
include_directories(${PROJECT_SOURCE_DIR}/common/utils/include/utils)
include_directories(${PROJECT_SOURCE_DIR}/common/config/include)
include_directories(${PROJECT_SOURCE_DIR}/thirdparty/iceoryx/include/iceoryx/v2.95.3/)
include_directories(${PROJECT_SOURCE_DIR}/thirdparty/yaml-cpp/include/)
include_directories(${PROJECT_SOURCE_DIR}/thirdparty/opencv/include/opencv4/)
include_directories(${PROJECT_SOURCE_DIR}/thirdparty/math/eigen3)
include_directories(${PROJECT_SOURCE_DIR}/thirdparty/boost/include)
include_directories(${PROJECT_SOURCE_DIR}/thirdparty/spdlog/include)
include_directories(${PROJECT_SOURCE_DIR}/thirdparty/json/include)
include_directories(${PROJECT_SOURCE_DIR}/thirdparty/osqp/include)
include_directories(${PROJECT_SOURCE_DIR}/thirdparty/backtrace/include)
# software 定义消息
include_directories(${PROJECT_SOURCE_DIR}/mower_msgs/)

# include_directories(${PROJECT_SOURCE_DIR}/thirdparty/cryptopp/include)

link_directories(${PROJECT_SOURCE_DIR}/thirdparty/attr/lib/${HOST_PLATFORM}/${SOC_NAME}/)
link_directories(${PROJECT_SOURCE_DIR}/thirdparty/acl/lib/${HOST_PLATFORM}/${SOC_NAME}/)
link_directories(${PROJECT_SOURCE_DIR}/thirdparty/iceoryx/lib/${HOST_PLATFORM}/${SOC_NAME}/)
link_directories(${PROJECT_SOURCE_DIR}/thirdparty/yaml-cpp/lib/${HOST_PLATFORM}/${SOC_NAME}/)
link_directories(${PROJECT_SOURCE_DIR}/thirdparty/opencv/lib/${HOST_PLATFORM}/${SOC_NAME}/)
link_directories(${PROJECT_SOURCE_DIR}/thirdparty/boost/lib/${HOST_PLATFORM}/${SOC_NAME}/)
link_directories(${PROJECT_SOURCE_DIR}/thirdparty/spdlog/lib/${HOST_PLATFORM}/${SOC_NAME}/)
link_directories(${PROJECT_SOURCE_DIR}/thirdparty/osqp/lib/${HOST_PLATFORM}/${SOC_NAME}/)
link_directories(${PROJECT_SOURCE_DIR}/thirdparty/backtrace/lib/${HOST_PLATFORM}/${SOC_NAME}/)
# link_directories(${PROJECT_SOURCE_DIR}/thirdparty/cryptopp/lib/${HOST_PLATFORM}/${SOC_NAME}/)

# 添加各个模块的编译
option(BUILD_BRINGUP "" ON)
option(BUILD_UTILS "" ON)
option(BUILD_PERCEPTION "" ON)
option(BUILD_NAVIGATION "" ON)
option(BUILD_LOCALIZATION "" ON)
option(BUILD_CALIBRATION "" ON)
option(BUILD_DOC "" OFF)
option(BUILD_CAMERA "" OFF)
option(BUILD_ACTUATOR "" OFF)
option(BUILD_TEST "" ON)

if(BUILD_BRINGUP)
    add_subdirectory(bringup)
endif()

if(BUILD_UTILS)
    add_subdirectory(common/utils)
endif()

if(BUILD_CAMERA)
    add_subdirectory(interaction/camera)
endif()

if(BUILD_ACTUATOR)
    add_subdirectory(interaction/actuator)
endif()

if(BUILD_TEST)
    add_subdirectory(test)
endif()

if(BUILD_PERCEPTION)
    add_subdirectory(perception)
endif()

if(BUILD_NAVIGATION)
    add_subdirectory(navigation)
endif()

if(BUILD_LOCALIZATION)
    add_subdirectory(localization)
endif()

if(BUILD_CALIBRATION)
    add_subdirectory(calibration)
endif()

if(BUILD_DOC)
    add_subdirectory(doc)
endif()

#公共资源安装, 如:iceoryx库、roudi、yaml-cpp库、OpenCV库
install(DIRECTORY
    ${PROJECT_SOURCE_DIR}/thirdparty/iceoryx/lib/${HOST_PLATFORM}/${SOC_NAME}/
    DESTINATION ${PROJECT_SOURCE_DIR}/install/${THIRDPARTY_INSTALL_DIR}/
)
install(DIRECTORY
    ${PROJECT_SOURCE_DIR}/thirdparty/iceoryx/bin/${HOST_PLATFORM}/${SOC_NAME}/
    DESTINATION ${PROJECT_SOURCE_DIR}/install/${SOFTWARE_INSTALL_DIR}/bin/
)
install(DIRECTORY
    ${PROJECT_SOURCE_DIR}/thirdparty/opencv/lib/${HOST_PLATFORM}/${SOC_NAME}/
    DESTINATION ${PROJECT_SOURCE_DIR}/install/${THIRDPARTY_INSTALL_DIR}/
)
install(DIRECTORY
    ${PROJECT_SOURCE_DIR}/thirdparty/boost/lib/${HOST_PLATFORM}/${SOC_NAME}/
    DESTINATION ${PROJECT_SOURCE_DIR}/install/${THIRDPARTY_INSTALL_DIR}/
)
install(DIRECTORY
    ${PROJECT_SOURCE_DIR}/thirdparty/attr/lib/${HOST_PLATFORM}/${SOC_NAME}/
    DESTINATION ${PROJECT_SOURCE_DIR}/install/${THIRDPARTY_INSTALL_DIR}/
)
install(DIRECTORY
    ${PROJECT_SOURCE_DIR}/thirdparty/acl/lib/${HOST_PLATFORM}/${SOC_NAME}/
    DESTINATION ${PROJECT_SOURCE_DIR}/install/${THIRDPARTY_INSTALL_DIR}/
)
install(DIRECTORY
    ${PROJECT_SOURCE_DIR}/thirdparty/glog/lib/${HOST_PLATFORM}/${SOC_NAME}/
    DESTINATION ${PROJECT_SOURCE_DIR}/install/${THIRDPARTY_INSTALL_DIR}/
)
install(DIRECTORY
    ${PROJECT_SOURCE_DIR}/thirdparty/spdlog/lib/${HOST_PLATFORM}/${SOC_NAME}/
    DESTINATION ${PROJECT_SOURCE_DIR}/install/${THIRDPARTY_INSTALL_DIR}/
)
install(DIRECTORY
    ${PROJECT_SOURCE_DIR}/thirdparty/osqp/lib/${HOST_PLATFORM}/${SOC_NAME}/
    DESTINATION ${PROJECT_SOURCE_DIR}/install/${THIRDPARTY_INSTALL_DIR}/
)
install(DIRECTORY
    ${PROJECT_SOURCE_DIR}/thirdparty/backtrace/lib/${HOST_PLATFORM}/${SOC_NAME}/
    DESTINATION ${PROJECT_SOURCE_DIR}/install/${THIRDPARTY_INSTALL_DIR}/
)
