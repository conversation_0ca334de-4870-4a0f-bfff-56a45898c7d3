#include "multi_trajectory_tracker.hpp"

#include "utils/logger.hpp"
#include "utils/math_type.hpp"

namespace fescue_iox
{

MultiTrajectoryTracker::MultiTrajectoryTracker(const std::vector<std::vector<TrajectoryPose>> &trajs)
    : trajs_(trajs)
{
    for (size_t i = 0; i < trajs_.size(); ++i)
    {
        LOG_INFO("traj {} size: {} length: {}", i, trajs_[i].size(), CalculateTrajectoryLength(trajs_[i]));
        if (trajs_[i].empty())
        {
            continue;
        }
        LOG_INFO("traj {}, end pose x: {}, y: {}, theta: {}, linear_velocity: {}",
                 i, trajs_[i].back().x, trajs_[i].back().y, trajs_[i].back().theta,
                 trajs_[i].back().linear_velocity);
    }
}

double MultiTrajectoryTracker::CalculateTrajectoryLength(const std::vector<TrajectoryPose> &traj) const
{
    double length = 0;
    for (size_t i = 1; i < traj.size(); ++i)
    {
        length += std::hypot(traj[i].x - traj[i - 1].x, traj[i].y - traj[i - 1].y);
    }
    return length;
}

bool MultiTrajectoryTracker::IsSpinTraj(const std::vector<TrajectoryPose> &traj) const
{
    if (traj.size() != 1)
    {
        return false;
    }
    const auto &pose = traj[0];
    if (std::abs(pose.linear_velocity) < 1e-6 && std::abs(pose.angular_velocity) > 1e-6)
    {
        return true;
    }
    return false;
}

void MultiTrajectoryTracker::Update(double time, const TrajectoryPose &pose, const OdomResult &odom_result)
{
    (void)time;
    if (result_ == TrackerResult::kArrived || result_ == TrackerResult::kTimeout)
    {
        return;
    }
    result_ = TrackerResult::kRunning;
    if (trajs_.empty())
    {
        LOG_WARN("trajs is empty");
        return;
    }
    double final_pose_arrival_dist = 0.1;
    double final_pose_arrival_angle = 0.1;
    double middle_pose_arrival_dist = 0.1;
    double middle_pose_arrival_angle = 0.2;
    double arrival_dist;
    double arrival_angle;
    if (current_traj_index_ == trajs_.size() - 1)
    {
        arrival_dist = final_pose_arrival_dist;
        arrival_angle = final_pose_arrival_angle;
    }
    else
    {
        arrival_dist = middle_pose_arrival_dist;
        arrival_angle = middle_pose_arrival_angle;
    }
    const auto &cur_traj = trajs_[current_traj_index_];
    bool is_cur_traj_spin = IsSpinTraj(cur_traj);
    // 调用轨迹跟随
    if (pure_pursuit_tracker_ == nullptr)
    {
        PurePursuitConfig pure_pursuit_config;
        if (current_traj_index_ == trajs_.size() - 1)
        {
            pure_pursuit_config.try_reach_goal_angle = true;
        }
        else
        {
            pure_pursuit_config.try_reach_goal_angle = false;
        }
        pure_pursuit_tracker_ = std::make_shared<PurePursuitTracker>(cur_traj, pure_pursuit_config);
    }
    pure_pursuit_tracker_->Update(pose, odom_result);
    linear_velocity_ = pure_pursuit_tracker_->GetLinearVelocity();
    angular_velocity_ = pure_pursuit_tracker_->GetAngularVelocity();
    // 到达每条轨迹的终点附近时，到达最近的位置，达到角度和位置要求，即可判达
    const auto &cur_final_pose = cur_traj.back();
    double dist_to_final_pose = sqrt(pow(pose.x - cur_final_pose.x, 2) + pow(pose.y - cur_final_pose.y, 2));
    double angle_to_final_pose = NormalizeAngle(pose.theta - cur_final_pose.theta);
    const auto &cur_result = pure_pursuit_tracker_->GetResult();
    bool is_cur_traj_arrived = (cur_result == TrackerResult::kArrived);
    // LOG_INFO("dist_to_final_pose: {:.4f}, angle_to_final_pose: {:.4f}, last_dist: {:.4f}, is_cur_traj_arrived: {}, is_cur_traj_spin: {}",
    //          dist_to_final_pose, angle_to_final_pose, last_dist_, is_cur_traj_arrived, is_cur_traj_spin);
    if (is_cur_traj_arrived || (!is_cur_traj_spin && dist_to_final_pose < arrival_dist && abs(angle_to_final_pose) < arrival_angle &&
                                last_dist_ > 1e-6 && dist_to_final_pose > last_dist_))
    {
        if (current_traj_index_ == trajs_.size() - 1)
        {
            LOG_INFO("Arrived at the final pose, cur x: {}, y: {}, theta: {}, final x: {}, y: {}, theta: {}, index: {}",
                     pose.x, pose.y, pose.theta, cur_final_pose.x, cur_final_pose.y, cur_final_pose.theta, current_traj_index_);
            result_ = TrackerResult::kArrived;
        }
        else
        {
            current_traj_index_++;
            pure_pursuit_tracker_ = nullptr;
            LOG_INFO("Switch to next traj, cur x: {}, y: {}, theta: {}, final x: {}, y: {}, theta: {}, index: {}",
                     pose.x, pose.y, pose.theta, cur_final_pose.x, cur_final_pose.y, cur_final_pose.theta, current_traj_index_);
        }
    }
    else if (cur_result == TrackerResult::kTimeout)
    {
        result_ = TrackerResult::kTimeout;
    }
    last_dist_ = dist_to_final_pose;
}

} // namespace fescue_iox