#include "moving_average_filter.hpp"

#include "utils/current_time.hpp"

#include <cmath>

namespace fescue_iox
{

MovingAverageFilter::MovingAverageFilter(int filter_time)
    : filter_time_(filter_time)
{
}

void MovingAverageFilter::setFilterTime(int filter_time)
{
    filter_time_ = filter_time;
}

geometry_msgs__msg__PoseStamped_iox MovingAverageFilter::filter(const geometry_msgs__msg__PoseStamped_iox &new_pose)
{
    // 获取当前时间
    auto current_time = CurrentTimeSeconds();

    // 将新数据添加到队列
    poses_.push_back(new_pose);

    // 移除时间超出滤波窗口的数据
    while (!poses_.empty() && (current_time - poses_.front().header.stamp.sec) > (uint64_t)filter_time_)
    {
        poses_.pop_front();
    }

    // 计算均值
    return computeAveragePose();
}

geometry_msgs__msg__PoseStamped_iox MovingAverageFilter::computeAveragePose()
{
    geometry_msgs__msg__PoseStamped_iox average_pose;
    average_pose.header.stamp = poses_.back().header.stamp; // 使用最新时间戳

    // 初始化位置和方向的累加和
    double sum_x = 0.0, sum_y = 0.0, sum_z = 0.0;
    double sum_qx = 0.0, sum_qy = 0.0, sum_qz = 0.0, sum_qw = 0.0;
    int n = poses_.size();

    // 累加所有姿态的位置信息和方向信息
    for (const auto &pose : poses_)
    {
        sum_x += pose.pose.position.x;
        sum_y += pose.pose.position.y;
        sum_z += pose.pose.position.z;

        sum_qx += pose.pose.orientation.x;
        sum_qy += pose.pose.orientation.y;
        sum_qz += pose.pose.orientation.z;
        sum_qw += pose.pose.orientation.w;
    }

    // 计算平均值
    average_pose.pose.position.x = sum_x / n;
    average_pose.pose.position.y = sum_y / n;
    average_pose.pose.position.z = sum_z / n;

    average_pose.pose.orientation.x = sum_qx / n;
    average_pose.pose.orientation.y = sum_qy / n;
    average_pose.pose.orientation.z = sum_qz / n;
    average_pose.pose.orientation.w = sum_qw / n;

    // 归一化四元数
    double norm = std::sqrt(
        average_pose.pose.orientation.x * average_pose.pose.orientation.x +
        average_pose.pose.orientation.y * average_pose.pose.orientation.y +
        average_pose.pose.orientation.z * average_pose.pose.orientation.z +
        average_pose.pose.orientation.w * average_pose.pose.orientation.w);

    average_pose.pose.orientation.x /= norm;
    average_pose.pose.orientation.y /= norm;
    average_pose.pose.orientation.z /= norm;
    average_pose.pose.orientation.w /= norm;

    return average_pose;
}

} // namespace fescue_iox
