#include "connect_pose_tracker.hpp"

#include "utils/logger.hpp"
#include "connect_trajectory_generator.hpp"

namespace fescue_iox
{

ConnectPoseTracker::ConnectPoseTracker(const TrajectoryPose& start_pose, const TrajectoryPose& end_pose, 
                                       const OccupancyResult& start_pose_result) {
    ConnectTrajectoryGenerator generator;
    trajs_ = generator.ConnectPose(start_pose, end_pose, start_pose_result);
    LOG_INFO("trajs size: {} start pose: x={} y={} theta={} end pose: x={} y={} theta={}", 
             trajs_.size(), start_pose.x, start_pose.y, start_pose.theta, end_pose.x, end_pose.y, end_pose.theta);
    multi_trajectory_tracker_ = std::make_shared<MultiTrajectoryTracker>(trajs_);
}

void ConnectPoseTracker::Update(double time, const TrajectoryPose& pose, const OdomResult& odom_result) {
    multi_trajectory_tracker_->Update(time, pose, odom_result);
    linear_velocity_ = multi_trajectory_tracker_->GetLinearVelocity();
    angular_velocity_ = multi_trajectory_tracker_->GetAngularVelocity();
    result_ = multi_trajectory_tracker_->GetResult();
}

}