#include "path_track.hpp"

#include "mower_sdk_version.h"
#include "path_track_config.hpp"
#include "utils/current_time.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

namespace fescue_iox
{

#define GOAL_POSE_FILTER 1

NavigationPathTrackAlg::NavigationPathTrackAlg(const PathTrackAlgParam &param)
{
    SetPathTrackParam(param);
    filter_ = std::make_unique<MovingAverageFilter>(filter_time_);
}

NavigationPathTrackAlg::~NavigationPathTrackAlg()
{
}

void NavigationPathTrackAlg::SetPathTrackParam(const PathTrackAlgParam &param)
{
    path_point_reached_threshold_ = param.path_point_reached_threshold;
    max_linear_v_ = param.max_linear_v;
    max_angular_w_ = param.max_angular_w;
    desired_linear_vel_ = param.desired_linear_vel;
    regulated_linear_scaling_min_radius_ = param.regulated_linear_scaling_min_radius;
    regulated_linear_scaling_min_speed_ = param.regulated_linear_scaling_min_speed;
    safety_distance_along_edge_ = param.safety_distance_along_edge; // 沿边安全距离

    lookahead_dist_ = param.lookahead_dist;
    use_velocity_scaled_lookahead_dist_ = param.use_velocity_scaled_lookahead_dist;
    lookahead_time_ = param.lookahead_time;
    min_lookahead_dist_ = param.min_lookahead_dist;
    max_lookahead_dist_ = param.max_lookahead_dist;
    filter_time_ = param.filter_time;

    robot_in_map_x_ = param.robot_in_map_x;
    robot_in_map_y_ = param.robot_in_map_y;
    robot_in_map_yaw_ = param.robot_in_map_yaw;

    if (filter_)
    {
        filter_->setFilterTime(filter_time_);
    }
}

int NavigationPathTrackAlg::DoPathTrack(const fescue_msgs__msg__NavigationEdgeFollowData &data, PathTrackAlgResult &result)
{
    bool ret = GetEdgeFollowTrackPath(data);
    if (!ret)
    {
        LOG_ERROR("NavigationPathTrackAlg get edge follow track path failed!");
        return -1;
    }

    std::vector<geometry_msgs__msg__PoseStamped_iox> track_path_local = GetEdgeFollowTrackPathLocal(track_path_);
    geometry_msgs__msg__PoseStamped_iox goal_pose = GetEdgeFollowGoalPose(track_path_local);
    result = GetEdgeFollowTwist(goal_pose);

    return 0;
}

bool NavigationPathTrackAlg::GetEdgeFollowTrackPath(const fescue_msgs__msg__NavigationEdgeFollowData &data)
{
    LOG_DEBUG("Sub walk along edge data [resolution, car_position]: [{}, {}, {}]",
              data.pixels_to_meters, robot_in_map_x_, robot_in_map_y_);
    for (size_t i = 0; i < data.path.poses.data.size(); i++)
    {
        LOG_DEBUG("Sub perception walk along edge path: [{}, {}]",
                  data.path.poses.data[i].pose.position.x, data.path.poses.data[i].pose.position.y);
    }

    if (data.path.poses.data.size() == 0)
    {
        LOG_ERROR("There are no waypoints along the edge!!!");
        return false;
    }

    resolution_ = data.pixels_to_meters; // m/pix

    robot_pose_.x = robot_in_map_x_ * resolution_;
    robot_pose_.y = robot_in_map_y_ * resolution_;
    robot_pose_.w = robot_in_map_yaw_; // 1.57 机器人相对于图像原点的朝向，固定方向【车道线】

    track_path_ = data.path;
    if (track_path_.poses.data.size() > 1)
    {
        for (size_t i = 0; i < track_path_.poses.data.size() - 1; ++i)
        {
            // 计算方位角
            float dx = track_path_.poses.data[i + 1].pose.position.x - track_path_.poses.data[i].pose.position.x;
            float dy = (-track_path_.poses.data[i + 1].pose.position.y) - (-track_path_.poses.data[i].pose.position.y);
            float dw = atan2f(dy, dx);

            // 加入分辨率
            track_path_.poses.data[i].pose.position.x = track_path_.poses.data[i].pose.position.x * resolution_;
            track_path_.poses.data[i].pose.position.y = -track_path_.poses.data[i].pose.position.y * resolution_;
            track_path_.poses.data[i].pose.position.z = dw;

            if (i == track_path_.poses.data.size() - 2)
            {
                // 加入分辨率
                track_path_.poses.data[i + 1].pose.position.x = track_path_.poses.data[i + 1].pose.position.x * resolution_;
                track_path_.poses.data[i + 1].pose.position.y = -track_path_.poses.data[i + 1].pose.position.y * resolution_;
                track_path_.poses.data[i + 1].pose.position.z = track_path_.poses.data[i].pose.position.z;
            }
        }
    }
    else if (track_path_.poses.data.size() == 1)
    {
        // 2. 只有一个路径点的情况
        // std::cout << "只有一个路径点的情况" << std::endl;
        track_path_.poses.data[0].pose.position.x = robot_in_map_x_ * resolution_;
        track_path_.poses.data[0].pose.position.y = (robot_in_map_y_ / 2) * resolution_;
        track_path_.poses.data[0].pose.position.z = robot_in_map_yaw_;
    }
    else
    {
        // 2. 只有0个路径点的情况
        // std::cout << "只有0个路径点的情况" << std::endl;
        geometry_msgs__msg__PoseStamped_iox pose;
        pose.pose.position.x = robot_in_map_x_ * resolution_;
        pose.pose.position.y = (robot_in_map_y_ / 2) * resolution_;
        pose.pose.position.z = robot_in_map_yaw_;
        track_path_.poses.data.push_back(pose);
    }

    LOG_DEBUG("Robot position: [{}, {}, {}, {}[deg(yaw)]]", robot_pose_.x, robot_pose_.y, robot_pose_.w, robot_pose_.w * 180 / 3.14);
    LOG_DEBUG("Path position path[0]: [{}, {}, {}, {}[deg(yaw)]]",
              track_path_.poses.data[0].pose.position.x, track_path_.poses.data[0].pose.position.y,
              track_path_.poses.data[0].pose.position.z, track_path_.poses.data[0].pose.position.z * 180 / 3.14);

    return true;
}

std::vector<geometry_msgs__msg__PoseStamped_iox> NavigationPathTrackAlg::GetEdgeFollowTrackPathLocal(const nav_msgs__msg__Path_iox &track_path)
{
    int len = track_path.poses.data.size();
    LOG_DEBUG("track_path len: {}", len);
    LOG_DEBUG("track_path on map ({}, {})", track_path.poses.data[0].pose.position.x, track_path.poses.data[0].pose.position.x);

    // 1. 将路径点转为小车坐标系下
    VecXYW odom_pose;
    odom_pose.x = robot_in_map_x_ * resolution_;
    odom_pose.y = robot_in_map_y_ * resolution_;
    odom_pose.w = robot_in_map_yaw_; // 1.57 机器人相对于图像原点的朝向，固定方向【车道线】

    LOG_DEBUG("resolution_ 在小车坐标系下 {}", resolution_);

    std::vector<geometry_msgs__msg__PoseStamped_iox> track_path_local;
    VecXYW goal;
    for (size_t i = 0; i < track_path.poses.data.size(); i++)
    {
        geometry_msgs__msg__PoseStamped_iox pose = track_path.poses.data[i];
        goal.x = pose.pose.position.x;
        goal.y = pose.pose.position.y;
        goal.w = pose.pose.position.z;

        LOG_DEBUG("odom_pose在map坐标系下 ({}, {})", odom_pose.x, odom_pose.x);
        LOG_DEBUG("goal在map坐标系下 ({}, {})", goal.x, goal.x);
        VecXYW local_point = G2L(odom_pose, goal);
        LOG_DEBUG("local_point在小车坐标系下 ({}, {})", local_point.x, local_point.x);

        geometry_msgs__msg__PoseStamped_iox pose_local;
        pose_local.pose.position.x = local_point.x;
        pose_local.pose.position.y = local_point.y;
        pose_local.pose.position.z = local_point.w;

        // 增加沿边安全距离
        pose_local.pose.position.y += safety_distance_along_edge_;
        track_path_local.push_back(pose_local);
        LOG_DEBUG("pose_local在小车坐标系下 ({}, {})", pose_local.pose.position.x, pose_local.pose.position.y);
    }

    return track_path_local;
}

geometry_msgs__msg__PoseStamped_iox NavigationPathTrackAlg::GetEdgeFollowGoalPose(std::vector<geometry_msgs__msg__PoseStamped_iox> &track_path_local)
{
    geometry_msgs__msg__PoseStamped_iox goal_pose; // 前视点的位置

    lookahead_dist_ = GetLookAheadDistance(0);

    auto goal_pose_it = std::find_if(track_path_local.begin(), track_path_local.end(),
                                     [&](const auto &ps) { return hypot(ps.pose.position.x, ps.pose.position.y) >= lookahead_dist_; });
    if (goal_pose_it == track_path_local.end())
    {
        goal_pose_it = std::prev(track_path_local.end());
        goal_pose = *goal_pose_it;
        LOG_DEBUG("goal_pose 为最后一个位置({}, {}, {}, {}(deg))", goal_pose.pose.position.x, goal_pose.pose.position.y,
                  goal_pose.pose.position.z, goal_pose.pose.position.z * 180 / 3.14);
    }
    else if (goal_pose_it != track_path_local.begin())
    {
        // 如果开启了插值，并且找到的点不是路径的第一个点，则进行线段与圆的交点计算
        // 计算前一个点和当前目标点之间，距离原点恰好为前视距离的点
        // 这里利用的是两点式直线方程和圆的交点计算公式
        auto prev_pose_it = std::prev(goal_pose_it);
        // 计算前一个点和当前目标点之间，距离原点恰好为前视距离的点
        auto point = CircleSegmentIntersection(prev_pose_it->pose.position, goal_pose_it->pose.position, lookahead_dist_);
        goal_pose.pose.position.x = point.x;
        goal_pose.pose.position.y = point.y;
        goal_pose.pose.position.z = prev_pose_it->pose.position.z;
        LOG_DEBUG("goal_pose 为路径与前视距离的交点：({}, {}, {}, {}(deg))", goal_pose.pose.position.x, goal_pose.pose.position.y,
                  goal_pose.pose.position.z, goal_pose.pose.position.z * 180 / 3.14);
    }
    else
    {
        goal_pose = *goal_pose_it;
        LOG_DEBUG("goal_pose 为路径的第一个点：({}, {}, {}, {}(deg))", goal_pose.pose.position.x, goal_pose.pose.position.y,
                  goal_pose.pose.position.z, goal_pose.pose.position.z * 180 / 3.14);
    }

    // 加上时间戳，滤波函数需要用到
    goal_pose.header.stamp.sec = CurrentTimeSeconds();

#if GOAL_POSE_FILTER
    // 对收到的goal_pose进行滤波
    goal_pose = filter_->filter(goal_pose);
    LOG_DEBUG("Filtered goal_pose: [x: {}, y: {}, z: {}]", goal_pose.pose.position.x, goal_pose.pose.position.y, goal_pose.pose.position.z);
#endif

    return goal_pose;
}

PathTrackAlgResult NavigationPathTrackAlg::GetEdgeFollowTwist(const geometry_msgs__msg__PoseStamped_iox &goal_pose)
{
    double linear_vel = 0;
    double angular_vel = 0;
    PathTrackAlgResult result;

    // 4. RPP 跟踪获取的前视点
    // 计算机器人基座坐标系中到前视点的距离平方
    double carrot_dist2 = (goal_pose.pose.position.x * goal_pose.pose.position.x) +
                          (goal_pose.pose.position.y * goal_pose.pose.position.y);

    // Find curvature of circle (k = 1 / R)
    double curvature = 0.0;
    if (carrot_dist2 > 0.001)
    {
        curvature = 2.0 * goal_pose.pose.position.y / carrot_dist2;
    }

    LOG_DEBUG("curvature: ({}, {}, {}(curvature))", goal_pose.pose.position.y, carrot_dist2, curvature);

    // 4.1 应用约束条件调整线速度和角速度
    linear_vel = desired_linear_vel_;
    ApplyConstraints(curvature, linear_vel);

    // Apply curvature to angular velocity after constraining linear velocity
    angular_vel = linear_vel * curvature;

    LOG_DEBUG("angular_vel: ({})", angular_vel);

    // populate and return message
    result.linear = linear_vel;
    result.angular = angular_vel;

    return result;
}

double NavigationPathTrackAlg::GetLookAheadDistance(float linear)
{
    // If using velocity-scaled look ahead distances, find and clamp the dist
    // Else, use the static look ahead distance
    double lookahead_dist = lookahead_dist_;
    if (use_velocity_scaled_lookahead_dist_)
    {
        lookahead_dist = fabs(linear) * lookahead_time_;
        lookahead_dist = std::clamp(lookahead_dist, min_lookahead_dist_, max_lookahead_dist_);
    }

    return lookahead_dist;
}

void NavigationPathTrackAlg::ApplyConstraints(const double &curvature, double &linear_vel)
{
    double curvature_vel = linear_vel; // 由曲率限制的速度

    // 1. limit the linear velocity by curvature。 根据曲率限制线性速度
    const double radius = fabs(1.0 / curvature); // 计算曲率半径
    const double &min_rad = regulated_linear_scaling_min_radius_;

    LOG_DEBUG("radius: ({})", radius);

    if (radius < min_rad)
    {
        curvature_vel *= 1.0 - (fabs(radius - min_rad) / min_rad); // 当曲率半径小于最小半径时，减小速度
    }

    linear_vel = std::max(curvature_vel, regulated_linear_scaling_min_speed_);

    // Limit linear velocities to be valid
    linear_vel = std::clamp(fabs(linear_vel), 0.0, desired_linear_vel_);
}

geometry_msgs__msg__Point_iox NavigationPathTrackAlg::CircleSegmentIntersection(const geometry_msgs__msg__Point_iox &p1,
                                                                                const geometry_msgs__msg__Point_iox &p2,
                                                                                double r)
{
    double x1 = p1.x;
    double x2 = p2.x;
    double y1 = p1.y;
    double y2 = p2.y;

    double dx = x2 - x1;
    double dy = y2 - y1;
    double dr2 = dx * dx + dy * dy;
    double D = x1 * y2 - x2 * y1;

    // Augmentation to only return point within segment
    double d1 = x1 * x1 + y1 * y1;
    double d2 = x2 * x2 + y2 * y2;
    double dd = d2 - d1;

    geometry_msgs__msg__Point_iox p;
    double sqrt_term = std::sqrt(r * r * dr2 - D * D);               // 计算根号内的部分，二次方程解的一部分
    p.x = (D * dy + std::copysign(1.0, dd) * dx * sqrt_term) / dr2;  // 计算交点的x坐标
    p.y = (-D * dx + std::copysign(1.0, dd) * dy * sqrt_term) / dr2; // 计算交点的y坐标
    return p;
}

} // namespace fescue_iox
