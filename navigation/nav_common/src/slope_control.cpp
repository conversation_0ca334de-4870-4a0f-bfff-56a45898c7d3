#include "slope_control.hpp"

#include "mower_sdk_version.h"
#include "slope_control_config.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

namespace fescue_iox
{

NavigationSlopeControlAlg::NavigationSlopeControlAlg(const SlopeControlAlgParam &param)
{
    SetSlopeControlParam(param);
}

NavigationSlopeControlAlg::~NavigationSlopeControlAlg()
{
}

SlopeControlVelocityData NavigationSlopeControlAlg::DoSlopeControl(const SlopeControlVelocityData &input_vel, const SlopeControlVelocityData &current_vel,
                                                                   const SlopeDetectionResult &input_slope_result)
{
    (void)current_vel;
    LOG_INFO_THROTTLE(500, "mower_control->linear_velocity: {}, mower_control->angular_velocity: {}",
                      input_vel.linear, input_vel.angular);
    LOG_INFO_THROTTLE(500, "slope_detection_result->pitch: {}, slope_detection_result->roll: {}, slope_detection_result->slope_status: {}",
                      input_slope_result.pitch, input_slope_result.roll, input_slope_result.slope_status);
    SlopeControlVelocityData output_vel;

    // abnormal situation
    if (input_slope_result.slope_status == -1)
    {
        output_vel.linear = input_vel.linear;
        output_vel.angular = input_vel.angular;
    }
    else
    {
        output_vel.linear = input_vel.linear + pitch_flag_ * k_linear_ * sin(input_slope_result.pitch);
        output_vel.angular = input_vel.angular + k_angular_ * sin(input_slope_result.roll);
    }

    LOG_INFO_THROTTLE(500, "slope_control->linear_velocity: {}, slope_control->angular_velocity: {}",
                      output_vel.linear, output_vel.angular);

    return output_vel;
}

int NavigationSlopeControlAlg::SetSlopeControlParam(const SlopeControlAlgParam &config)
{
    k_linear_ = config.k_linear;
    k_angular_ = config.k_angular;

    return 0;
}

SlopeControlAlgParam NavigationSlopeControlAlg::GetSlopeControlParam()
{
    return SlopeControlAlgParam(k_linear_, k_angular_);
}

} // namespace fescue_iox
