#include "obstacle_detector.hpp"

#include "utils/logger.hpp"

namespace fescue_iox
{

ObstacleDetector::ObstacleDetector(float dead_zone, float danger_dis, float slow_down_dis)
    : dead_zone_(dead_zone)
    , danger_dis_(danger_dis)
    , start_slow_down_dis_(slow_down_dis)
{
}

ObstacleDetectionResult ObstacleDetector::DetectObstacles(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float resolution)
{
    (void)width;
    ObstacleDetectionResult result;

    if (dead_zone_ > danger_dis_ || dead_zone_ > start_slow_down_dis_)
    {
        LOG_ERROR("dead zone is bigger than danger_dis and start_slow_down_dis, values: {} {} {}",
                  dead_zone_, danger_dis_, start_slow_down_dis_);
        return result;
    }

    int y_slow = floor((start_slow_down_dis_ - dead_zone_) / resolution);
    int y_danger = floor((danger_dis_ - dead_zone_) / resolution);

    for (int y = height - 1; y >= 0; y--)
    {
        if (result.is_danger || result.is_slow)
        {
            break;
        }
        for (int x = 13; x < 28; x++)
        {
            if (grid[y][x] == 1)
            {
                result.y_in_forward = y;
                if (height - y_danger <= y)
                {
                    result.is_danger = true;
                    result.is_surrounded = true;
                    break;
                }
                else if (height - y_slow <= y)
                {
                    result.is_slow = true;
                    break;
                }
            }
        }
    }

    result.y_slow = y_slow;
    result.result_valid = true;

    return result;
}

} // namespace fescue_iox