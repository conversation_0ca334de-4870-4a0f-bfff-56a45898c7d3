
#include "nav_utils.hpp"

#include "opencv2/opencv.hpp"

#include <chrono>
#include <filesystem>
#include <iostream>
#include <limits>
#include <string>

namespace fescue_iox
{

/**
 * @brief 提取轮廓
 *
 */
std::vector<std::vector<cv::Point>> GetEdgeContours(const uint8_t *img, int width, int height)
{
    cv::Mat img_mat = cv::Mat(height, width, CV_8UC1, (void *)img);
    std::vector<std::vector<cv::Point>> contours;
    std::vector<cv::Vec4i> hierarchy;
    cv::findContours(img_mat, contours, hierarchy, cv::RETR_TREE, cv::CHAIN_APPROX_SIMPLE);
    return contours;
}

/**
 * @brief 检查两条线段是否相交，并返回交点
 *
 * @param p0
 * @param p1
 * @param p2
 * @param p3
 * @param i
 * @return true
 * @return false
 */
bool getLineIntersection(cv::Point2f p0, cv::Point2f p1, cv::Point2f p2, cv::Point2f p3, cv::Point2f &i)
{
    cv::Point2f s1, s2;
    s1.x = p1.x - p0.x;
    s1.y = p1.y - p0.y;
    s2.x = p3.x - p2.x;
    s2.y = p3.y - p2.y;

    float s, t;
    float det = (-s2.x * s1.y + s1.x * s2.y);

    if (det == 0)
    {
        return false; // 平行线，无交点
    }

    s = (-s1.y * (p0.x - p2.x) + s1.x * (p0.y - p2.y)) / det;
    t = (s2.x * (p0.y - p2.y) - s2.y * (p0.x - p2.x)) / det;

    if (s >= 0 && s <= 1 && t >= 0 && t <= 1)
    {
        // 线段相交
        i.x = p0.x + (t * s1.x);
        i.y = p0.y + (t * s1.y);
        return true;
    }

    return false; // 线段不相交
}

/**
 * @brief 在图像坐标系下的角度转换
 *
 * @param pt1 起始点
 * @param pt2 目标点
 * @return float
 */
float calculateAngle(const cv::Point &pt1, const cv::Point &pt2)
{
    float deltaY = (-pt2.y) - (-pt1.y);
    float deltaX = pt2.x - pt1.x;

    return atan2f(deltaY, deltaX); // 计算方向角度
}

// 检查路径点是否在轮廓范围内的函数
std::vector<cv::Point> filterPointsWithinContour(const std::vector<cv::Point> &path, const std::vector<cv::Point> &contour)
{
    std::vector<cv::Point> path_within;
    for (const cv::Point &point : path)
    {
        double result = cv::pointPolygonTest(contour, point, false);
        if (result >= 0)
        {
            // 点在轮廓内，保留
            path_within.push_back(point);
        }
    }
    return path_within;
}

std::vector<cv::Point> handlePathWithIntersections(const std::vector<cv::Point> &path, const cv::Point &p1, const cv::Point &p2)
{
    std::vector<cv::Point> path_temp2;

    if (path.size() == 0)
    {
        // 如果路径点为0，无法进行路径插值，直接返回空路径
        std::cerr << "路径点数为0, 无法处理交叉点。" << std::endl;
        return std::vector<cv::Point>();
    }
    else
    {
        for (size_t i = 0; i < path.size() - 1; ++i)
        {
            cv::Point2f intersection;
            if (getLineIntersection(path[i], path[i + 1], p1, p2, intersection))
            {
                std::cout << "机器人前方20cm相交点: (" << intersection.x << ", " << intersection.y << ")" << std::endl;
                path_temp2.emplace_back(cv::Point(static_cast<int>(intersection.x), static_cast<int>(intersection.y)));
            }
            else
            {
                path_temp2.emplace_back(path[i]);
            }
        }
        path_temp2.emplace_back(path[path.size() - 1]);
    }

    return path_temp2;
}

/**
 * @brief
 *
 * @param path_temp1
 * @param contour
 * @param p1 相交直线的两个端点
 * @param p2
 * @return std::vector<cv::Point>
 */
std::vector<cv::Point> processPath(const std::vector<cv::Point> &path_temp1, const std::vector<cv::Point> &contour, const cv::Point &p1, const cv::Point &p2)
{
    // 2.2.1 检查每个路径点是否在轮廓范围内,点在轮廓内，保留。用于后续剔除
    std::vector<cv::Point> path_within = filterPointsWithinContour(path_temp1, contour);

    // 2.2.2 检查路径中的每个线段与直线p1p2是否相交
    std::vector<cv::Point> path_temp2 = handlePathWithIntersections(path_temp1, p1, p2);

    // 2.2.3 path_temp2 中剔除在 path_within 中存在的点，同时保持 path_temp2 点的顺序不变，
    path_temp2.erase(
        std::remove_if(path_temp2.begin(), path_temp2.end(),
                       [&path_within](const cv::Point &p) {
                           return std::find(path_within.begin(), path_within.end(), p) != path_within.end();
                       }),
        path_temp2.end());

    return path_temp2;
}

/**
 * @brief 返回true，表示小车的左边存在障碍物，则原地向左旋转。返回false，发路径给控制算法
 *
 * @param img
 * @return true
 * @return false
 */
bool isNonGrassOnLeft(const cv::Mat &img, const int &dividing_line_left, const int &dividing_line_right)
{
    (void)dividing_line_left;
    cv::Mat img_mat = img.clone();

    // 转换为灰度图像
    if (img_mat.channels() == 3)
    {
        cv::cvtColor(img_mat, img_mat, cv::COLOR_BGR2GRAY);
    }

    // 二值化处理，阈值设为128，最大值为255
    cv::Mat binaryImg;
    cv::threshold(img_mat, binaryImg, 128, 255, cv::THRESH_BINARY);

    // 遍历图像左边 (100 < x < 200) 区域，检查是否存在白色像素
    for (int row = binaryImg.rows - 140; row < binaryImg.rows; ++row)
    {
        for (int col = 0; col < dividing_line_right; ++col) // col = dividing_line_left
        {
            // 如果找到一个白色像素 (值为255)
            if (binaryImg.at<uchar>(row, col) == 255)
            {
                return true;
            }
        }
    }

    return false;
}

/**
 * @brief 返回false，表示bev不存在障碍物，则向右原地旋转。返回true，发路径给控制算法
 *
 * @param img
 * @return true
 * @return false
 */
bool isHaveNonGrass(const cv::Mat &img)
{
    cv::Mat img_mat = img.clone();

    // 转换为灰度图像
    if (img_mat.channels() == 3)
    {
        cv::cvtColor(img_mat, img_mat, cv::COLOR_BGR2GRAY);
    }

    // 二值化处理，阈值设为128，最大值为255
    cv::Mat binaryImg;
    cv::threshold(img_mat, binaryImg, 128, 255, cv::THRESH_BINARY);

    // 遍历图像左边 (x < 200) 区域，检查是否存在白色像素
    for (int row = binaryImg.rows - 140; row < binaryImg.rows; ++row)
    {
        for (int col = 0; col < binaryImg.cols; ++col)
        {
            // 如果找到一个白色像素 (值为255)
            if (binaryImg.at<uchar>(row, col) == 255)
            {
                return true;
            }
        }
    }

    return false;
}

// 计算直线与图像边界的交点
void getLineEndPoints(const cv::Point &p, double theta, const cv::Size &imageSize, cv::Point &p1, cv::Point &p2)
{
    double a = tan(theta);    // 直线的斜率
    double b = p.y - a * p.x; // 直线的截距
    int w = imageSize.width;
    int h = imageSize.height;

    if (theta == 0 || theta == M_PI) // 如果直线与 x 轴平行
    {
        p1 = cv::Point(0, p.y);     // 第一个交点位于图像左边界
        p2 = cv::Point(w - 1, p.y); // 第二个交点位于图像右边界
    }
    else if (theta == M_PI / 2) // 如果直线与 y 轴平行
    {
        p1 = cv::Point(p.x, h - 1); // 第一个交点位于图像下边界
        p2 = cv::Point(p.x, 0);     // 第二个交点位于图像上边界
    }
    else
    {
        p1 = cv::Point(0, b);                   // 假设交点在图像左边界上
        p2 = cv::Point((h - 1 - b) / a, h - 1); // 根据直线方程计算可能的第二个交点

        // 如果第二个交点超出了图像右边界，调整为图像右边界上的交点
        if (p2.x < 0)
        {
            p2 = cv::Point(0, b); // 调整为图像左边界上的交点
        }
        else if (p2.x >= w)
        {
            p2 = cv::Point(w - 1, a * (w - 1) + b); // 调整为图像右边界上的交点
        }

        // 如果第一个交点超出了图像下边界，调整为图像下边界上的交点
        if (p1.y < 0)
        {
            p1 = cv::Point((0 - b) / a, 0); // 调整为图像上边界上的交点
        }
        else if (p1.y >= h)
        {
            p1 = cv::Point(w - 1, a * (w - 1) + b); // 调整为图像下边界上的交点
        }
    }

    // 确保 p1 到 p2 的方向在 0 到 pi 之间
    if ((p1.x > p2.x && p1.y == p2.y) || (p1.x == p2.x && p1.y < p2.y) ||
        (p1.x > p2.x && p1.y < p2.y) || (p1.x < p2.x && p1.y < p2.y))
    {
        std::swap(p1, p2);
    }
}

bool HaveIntersection(const cv::Point &min_x_point, double theta,
                      const cv::Mat &image, const std::vector<std::vector<cv::Point>> &contours_approx,
                      cv::Point &p1, cv::Point &p2)
{
    getLineEndPoints(min_x_point, theta, image.size(), p1, p2);

    // 遍历轮廓中的每一条线段，判断是否与 (p1, p2) 相交
    for (const auto &contour : contours_approx)
    {
        for (size_t i = 0; i < contour.size(); ++i)
        {
            cv::Point q1 = contour[i];
            cv::Point q2 = contour[(i + 1) % contour.size()]; // 循环获取轮廓的下一个点

            // 判断是否相交
            cv::Point2f intersection;
            if (getLineIntersection(q1, q2, p1, p2, intersection))
            {
                return true;
            }
        }
    }

    return false;
}

std::vector<cv::Point> GetFollowPath(const cv::Mat &input_img,
                                     const std::vector<cv::Point> &left_dead_line,
                                     const std::vector<cv::Point> &right_dead_line,
                                     const float &resolution)
{
    cv::Mat img_mat = input_img.clone();

    // 转换为灰度图像
    if (img_mat.channels() == 3)
    {
        cv::cvtColor(img_mat, img_mat, cv::COLOR_BGR2GRAY);
        cv::threshold(img_mat, img_mat, 127, 255, cv::THRESH_BINARY);
    }

    // 确保二值图像不是空的
    if (img_mat.empty())
    {
        std::cerr << "二值化处理后图像为空" << std::endl;
        return std::vector<cv::Point>{};
    }

#if 0
    // 对二值化后的画布进行形态学膨胀、腐蚀以及闭处理
    float robot_radius = 0.4;
    int size = static_cast<int>(ceil(robot_radius / resolution)); // kernel容易受小车半径和图像分辨率的影响
    cv::Mat kernel = cv::getStructuringElement(cv::MORPH_ELLIPSE, cv::Size(size, size), cv::Point(-1, -1));
    // cv::morphologyEx(img_mat, img_mat, cv::MORPH_OPEN, kernel, cv::Point(-1, -1), 1);   // 1
    cv::morphologyEx(img_mat, img_mat, cv::MORPH_DILATE, kernel, cv::Point(-1, -1), 1); // 1

#endif

    std::vector<std::vector<cv::Point>> contours;
    cv::findContours(img_mat, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_NONE); // 只检测外轮廓,存储所有的轮廓点

    // 遍历所有轮廓并进行多边形逼近
    std::vector<std::vector<cv::Point>> contours_approx;
    for (size_t i = 0; i < contours.size(); i++)
    {
        std::vector<cv::Point> approx;
        cv::approxPolyDP(contours[i], approx, 30.0, true); // 进行多边形逼近
        contours_approx.emplace_back(approx);
    }

    // 1. 对多边形逼近后的所有轮廓，按照逆时针进行排列
    std::vector<cv::Point> path;
    if (contours_approx.size() == 0)
    {
        return std::vector<cv::Point>{};
    }
    else if (contours_approx.size() == 1)
    {
        std::vector<cv::Point> contour = contours_approx.at(0);
        std::reverse(contour.begin(), contour.end());
        path = contour;
    }
    else
    {
        int x_min = std::numeric_limits<int>::max();
        int y_min = std::numeric_limits<int>::max();
        int x_max = std::numeric_limits<int>::min();
        int y_max = std::numeric_limits<int>::min();

        for (const auto &contour : contours_approx)
        {
            for (const auto &point : contour)
            {
                if (point.x < x_min)
                    x_min = point.x;
                if (point.y < y_min)
                    y_min = point.y;
                if (point.x > x_max)
                    x_max = point.x;
                if (point.y > y_max)
                    y_max = point.y;
            }
        }

        cv::Point p1(x_min, y_max);
        cv::Point p2(x_min, y_min);

        path.emplace_back(p1);
        path.emplace_back(p2);
    }

    // 2. 对生成的路径，剔除不合适的点
    std::vector<cv::Point> final_path;

    // 2.1 保留指定方向向量的点
    std::vector<cv::Point> path_temp1;
    for (size_t i = 1; i < path.size(); ++i)
    {
        if (i == path.size() - 1)
        {
            float angle = calculateAngle(path[i - 1], path[i]);

            // 判断角度是否在0到pi的范围内 (0,pi]
            if (angle > 0 && angle <= M_PI)
            {
                path_temp1.push_back(path[i - 1]);
                path_temp1.push_back(path[i]);
            }
        }
        else
        {
            float angle = calculateAngle(path[i - 1], path[i]);

            // 判断角度是否在0到pi的范围内 (0,pi]
            if (angle > 0 && angle <= M_PI)
            {
                path_temp1.push_back(path[i - 1]);
            }
        }
    }

    // 2.2 剔除机器人前方20cm以内的点
    std::vector<cv::Point> path_temp2;
    int distance_front = static_cast<int>(0.05 / resolution); // 0.2m
    int width = input_img.cols;
    int height = input_img.rows;

    cv::Point p1(0, height - distance_front);
    cv::Point p2(width, height - distance_front);
    cv::Point p3(0, height);
    cv::Point p4(width, height);
    std::vector<cv::Point> contour = {p1, p2, p4, p3};

    path_temp2 = processPath(path_temp1, contour, p1, p2);

    // 2.3 剔除盲区的路径点
    std::vector<cv::Point> path_temp3;

    std::vector<cv::Point> contour_left = left_dead_line;
    contour_left.emplace_back(p3);
    std::vector<cv::Point> contour_right = right_dead_line;
    contour_right.emplace_back(p4);

    path_temp3 = processPath(path_temp2, contour_left, left_dead_line[0], left_dead_line[1]);

    final_path = processPath(path_temp3, contour_right, right_dead_line[0], right_dead_line[1]);

    return final_path;
}

std::vector<cv::Point> GetFollowPath(const cv::Mat &input_img)
{
    cv::Mat image = input_img.clone();

    // 转换为灰度图像
    if (image.channels() == 3)
    {
        cv::cvtColor(image, image, cv::COLOR_BGR2GRAY);
        cv::threshold(image, image, 127, 255, cv::THRESH_BINARY);
    }

    // 确保二值图像不是空的
    if (image.empty())
    {
        std::cerr << "二值化处理后图像为空" << std::endl;
        return std::vector<cv::Point>{};
    }

    std::vector<std::vector<cv::Point>> contours;
    cv::findContours(image, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_NONE); // 只检测外轮廓,存储所有的轮廓点

    // 遍历所有轮廓并进行多边形逼近
    std::vector<std::vector<cv::Point>> contours_approx;
    for (size_t i = 0; i < contours.size(); i++)
    {
        std::vector<cv::Point> approx;
        cv::approxPolyDP(contours[i], approx, 5.0, true); // 进行多边形逼近
        contours_approx.emplace_back(approx);
    }

    cv::Point min_x_point;                       // 用于存储x坐标最小的点
    int min_x = std::numeric_limits<int>::max(); // 初始化为最大整数值

    // 遍历所有轮廓
    for (size_t i = 0; i < contours_approx.size(); i++)
    {
        // 遍历轮廓中的每个点
        for (size_t j = 0; j < contours_approx[i].size(); j++)
        {
            // 如果当前点的x坐标小于已记录的最小x坐标，则更新最小x坐标和点
            if (contours_approx[i][j].x < min_x)
            {
                min_x = contours_approx[i][j].x;
                min_x_point = contours_approx[i][j];
            }
        }
    }

    // 将x方向最小点，向左偏移5个像素
    min_x_point.x -= 5;
    if (min_x_point.x <= 0)
    {
        min_x_point.x += 5;
    }

    cv::Point bestP1, bestP2;
    // 遍历每个角度
    cv::Point p1, p2;

#if 1
    if (HaveIntersection(min_x_point, M_PI / 2, image, contours_approx, p1, p2))
    {
        bool is_last_intersect = true; // 上一个是否相交
        cv::Point p1_temp, p2_temp;
        for (double theta = M_PI / 2; theta > 0; theta -= M_PI / 180)
        {
            if (!HaveIntersection(min_x_point, theta, image, contours_approx, p1_temp, p2_temp) && is_last_intersect)
            {
                p1 = p1_temp;
                p2 = p2_temp;
                is_last_intersect = false;
            }
        }
    }
    else
    {
        bool is_last_intersect = false; // 上一个是否相交
        cv::Point p1_temp, p2_temp;
        for (double theta = M_PI / 2; theta < M_PI; theta += M_PI / 180)
        {
            if (HaveIntersection(min_x_point, theta, image, contours_approx, p1_temp, p2_temp) && !is_last_intersect)
            {
                p1 = p1_temp;
                p2 = p2_temp;
                is_last_intersect = true;
            }
        }
    }

#endif

#if 0
    // 直接用pi/2的方式生成路径
    getLineEndPoints(min_x_point, M_PI / 2, image.size(), p1, p2);

    // return std::vector<cv::Point>{p1, cv::Point(p2.x, p2.y + 2000)};

#endif

    // p1和p2肯定存在
    // return std::vector<cv::Point>{p1, p2};
    return std::vector<cv::Point>{p1, cv::Point(p2.x, p2.y + 2000)};
}

std::vector<std::vector<cv::Point>> getContours(const cv::Mat &input_img)
{
    std::vector<std::vector<cv::Point>> contours;
    cv::Mat img_mat = input_img.clone();

    // 转换为灰度图像
    if (img_mat.channels() == 3)
    {
        cv::cvtColor(img_mat, img_mat, cv::COLOR_BGR2GRAY);
        cv::threshold(img_mat, img_mat, 127, 255, cv::THRESH_BINARY);
    }

    // 确保二值图像不是空的
    if (img_mat.empty())
    {
        std::cerr << "二值化处理后图像为空" << std::endl;
        return contours;
    }

#if 0
    // 对二值化后的画布进行形态学膨胀、腐蚀以及闭处理
    float robot_radius = 0.4;
    int size = static_cast<int>(ceil(robot_radius / resolution)); // kernel容易受小车半径和图像分辨率的影响
    cv::Mat kernel = cv::getStructuringElement(cv::MORPH_ELLIPSE, cv::Size(size, size), cv::Point(-1, -1));
    // cv::morphologyEx(img_mat, img_mat, cv::MORPH_OPEN, kernel, cv::Point(-1, -1), 1);   // 1
    cv::morphologyEx(img_mat, img_mat, cv::MORPH_DILATE, kernel, cv::Point(-1, -1), 1); // 1

#endif

    cv::findContours(img_mat, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_NONE); // 只检测外轮廓,存储所有的轮廓点

    return contours;
}

// 将位姿转换为齐次变换矩阵
void poseToMatrix(const Pose_Mark &pose, double matrix[4][4])
{
    // 初始化为单位矩阵
    for (int i = 0; i < 4; ++i)
    {
        for (int j = 0; j < 4; ++j)
        {
            matrix[i][j] = (i == j) ? 1.0 : 0.0;
        }
    }

    // 平移部分
    matrix[0][3] = pose.x;
    matrix[1][3] = pose.y;
    matrix[2][3] = pose.z;

    // 旋转部分（使用欧拉角转换为旋转矩阵）
    double cr = cos(pose.roll), sr = sin(pose.roll);
    double cp = cos(pose.pitch), sp = sin(pose.pitch);
    double cy = cos(pose.yaw), sy = sin(pose.yaw);

    matrix[0][0] = cy * cp;
    matrix[0][1] = cy * sp * sr - sy * cr;
    matrix[0][2] = cy * sp * cr + sy * sr;

    matrix[1][0] = sy * cp;
    matrix[1][1] = sy * sp * sr + cy * cr;
    matrix[1][2] = sy * sp * cr - cy * sr;

    matrix[2][0] = -sp;
    matrix[2][1] = cp * sr;
    matrix[2][2] = cp * cr;
}

// 从齐次变换矩阵提取位姿
void matrixToPose(const double matrix[4][4], Pose_Mark &pose)
{
    // 提取平移部分
    pose.x = matrix[0][3];
    pose.y = matrix[1][3];
    pose.z = matrix[2][3];

    // 提取旋转部分（从旋转矩阵获取欧拉角）
    pose.roll = atan2(matrix[2][1], matrix[2][2]);
    pose.pitch = atan2(-matrix[2][0], sqrt(matrix[2][1] * matrix[2][1] + matrix[2][2] * matrix[2][2]));
    pose.yaw = atan2(matrix[1][0], matrix[0][0]);
}

// 矩阵求逆（仅适用于旋转和平移矩阵）
void invertMatrix(const double input[4][4], double output[4][4])
{
    // 旋转部分转置
    for (int i = 0; i < 3; ++i)
    {
        for (int j = 0; j < 3; ++j)
        {
            output[i][j] = input[j][i];
        }
    }

    // 平移部分
    for (int i = 0; i < 3; ++i)
    {
        output[i][3] = 0.0;
        for (int j = 0; j < 3; ++j)
        {
            output[i][3] -= output[i][j] * input[j][3];
        }
    }

    // 填充最后一行
    for (int i = 0; i < 4; ++i)
    {
        output[3][i] = (i == 3) ? 1.0 : 0.0;
    }
}

// 矩阵相乘
void multiplyMatrices(const double a[4][4], const double b[4][4], double result[4][4])
{
    for (int i = 0; i < 4; ++i)
    {
        for (int j = 0; j < 4; ++j)
        {
            result[i][j] = 0.0;
            for (int k = 0; k < 4; ++k)
            {
                result[i][j] += a[i][k] * b[k][j];
            }
        }
    }
}

// 主函数：计算base_link相对于mark的坐标
Pose_Mark calculateBaseLinkRelativeToMark(const Pose_Mark &camera_to_mark, const Pose_Mark &camera_to_base_link)
{
    double T_camera_to_mark[4][4], T_camera_to_base_link[4][4];
    double T_base_link_to_mark[4][4];

    // 将位姿转换为齐次变换矩阵
    poseToMatrix(camera_to_mark, T_camera_to_mark);
    poseToMatrix(camera_to_base_link, T_camera_to_base_link);

    // 计算base_link相对于mark的变换矩阵
    double T_camera_to_base_link_inv[4][4];
    invertMatrix(T_camera_to_base_link, T_camera_to_base_link_inv);
    multiplyMatrices(T_camera_to_mark, T_camera_to_base_link_inv, T_base_link_to_mark);

    // 将结果转换为位姿返回
    Pose_Mark base_link_to_mark;
    matrixToPose(T_base_link_to_mark, base_link_to_mark);
    return base_link_to_mark;
}

/**
 * @brief Get the Velocity From Motor R P M object
 *
 * @param left_rpm 左轮电机转速
 * @param right_rpm 右轮电机转速
 * @param wheel_radius 车轮半径
 * @param wheel_base 车轮轴距
 * @param linear_velocity 转换后的线速度
 * @param angular_velocity 转换后的角速度
 */
void GetVelocityFromMotorRPM(float left_rpm, float right_rpm, float wheel_radius, float wheel_base, float &linear_velocity, float &angular_velocity)
{
    // 将电机转速从RPM转换为rad/s
    float w_left = left_rpm * 2 * M_PI / 60.0f;
    float w_right = right_rpm * 2 * M_PI / 60.0f;

    float v_left = w_left * wheel_radius;
    float v_right = w_right * wheel_radius;

    linear_velocity = (v_right + v_left) / 2.0f;
    angular_velocity = (v_right - v_left) / wheel_base;
}

} // namespace fescue_iox
