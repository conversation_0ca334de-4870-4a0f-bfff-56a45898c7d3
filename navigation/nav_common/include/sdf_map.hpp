#pragma once

#include "opencv2/opencv.hpp"
#include "grid_map.hpp"

namespace fescue_iox
{

class SDFMap : public GridMapBase
{
   public:
    SDFMap(double resolution, int width, int height, const Point2f& origin)
        : GridMapBase(resolution, width, height, origin)
    {
        binary_map_ = cv::Mat(height, width, CV_8UC1, cv::<PERSON><PERSON><PERSON>(255));
    }

    inline void AddPoint(const Point2i& p) { AddPoint(p.x, p.y); }

    inline void AddPoint(int x, int y)
    {
        if (!IsInside(x, y)) {
            return;
        }
        binary_map_.at<uchar>(y, x) = 0;
    }

    inline float GetPreciseDist(int x, int y) const
    {
        if (!IsInside(x, y)) {
            return 0;
        }
        return distance_map_.at<float>(y, x) * GetResolution();
    }

    /**
     * @brief 计算距离变换，必须先调用Commit后才可以调用GetPreciseDist
     */
    void Commit() 
    { 
        cv::distanceTransform(binary_map_, distance_map_, cv::DIST_L2, 5);
    }

   private:
    cv::Mat binary_map_;
    cv::Mat distance_map_;
};

} // namespace fescue_iox