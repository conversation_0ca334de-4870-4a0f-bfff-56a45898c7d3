#pragma once

#include "data_type.hpp"
#include "pure_pursuit_tracker.hpp"

namespace fescue_iox
{

class MultiTrajectoryTracker
{
public:
    MultiTrajectoryTracker(const std::vector<std::vector<TrajectoryPose>>& trajs);

    void Update(double time, const TrajectoryPose& pose, const OdomResult& odom_result);

    double GetLinearVelocity() const { return linear_velocity_; }
    double GetAngularVelocity() const { return angular_velocity_; }
    size_t GetCurrentTrajIndex() const { return current_traj_index_; }
    TrackerResult GetResult() const { return result_; }

private:
    double CalculateTrajectoryLength(const std::vector<TrajectoryPose>& traj) const;
    bool IsSpinTraj(const std::vector<TrajectoryPose>& traj) const;

private:
    std::vector<std::vector<TrajectoryPose>> trajs_;
    double linear_velocity_ = 0;
    double angular_velocity_ = 0;
    size_t current_traj_index_ = 0;
    TrackerResult result_ = TrackerResult::kInvalid;
    double last_dist_ = -1;
    std::shared_ptr<PurePursuitTracker> pure_pursuit_tracker_ = nullptr;
};

}