#ifndef NAVIGATION_SLOPE_CONTROL_ALG_HPP
#define NAVIGATION_SLOPE_CONTROL_ALG_HPP

#include <string>
#include <vector>

namespace fescue_iox
{

struct SlopeControlAlgParam
{
    double k_linear{0.0};
    double k_angular{0.0};
    SlopeControlAlgParam() = default;
    SlopeControlAlgParam(double linear, double angular)
        : k_linear(linear)
        , k_angular(angular)
    {
    }
};

struct SlopeControlVelocityData
{
    double linear{0.0};
    double angular{0.0};
    SlopeControlVelocityData() = default;
    SlopeControlVelocityData(double vel_linear, double vel_angular)
        : linear(vel_linear)
        , angular(vel_angular)
    {
    }
};

struct SlopeDetectionResult
{
    float roll;
    float pitch;
    int slope_status;
    float yaw;
};

class NavigationSlopeControlAlg
{
public:
    NavigationSlopeControlAlg(const SlopeControlAlgParam &param);
    ~NavigationSlopeControlAlg();
    SlopeControlVelocityData DoSlopeControl(const SlopeControlVelocityData &input_vel, const SlopeControlVelocityData &current_vel,
                                            const SlopeDetectionResult &input_slope_result);
    int SetSlopeControlParam(const SlopeControlAlgParam &param);
    SlopeControlAlgParam GetSlopeControlParam();

private:
    // Parameters
    double k_linear_{0.0};
    double k_angular_{0.0};
    double pitch_flag_{-1.0};
};

} // namespace fescue_iox

#endif
