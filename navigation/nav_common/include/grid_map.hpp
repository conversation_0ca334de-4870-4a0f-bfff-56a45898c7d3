#pragma once

#include "data_type.hpp"

namespace fescue_iox
{

class GridMapBase
{
   public:
    GridMapBase(double resolution, int width, int height, const Point2f& origin) : 
        resolution_(resolution)
        , width_(width)
        , height_(height)
        , origin_(origin)
    {}

    inline bool IsInside(int x, int y) const { return x >= 0 && x < width_ && y >= 0 && y < height_; }
    inline bool IsInside(const Point2i& pos) const { return IsInside(pos.x, pos.y); }

    inline double GetResolution() const { return resolution_; }
    inline int GetWidth() const { return width_; }
    inline int GetHeight() const { return height_; }

    inline Point2f ConvertToPoint(const Point2i& grid) const
    {
        return Point2f(grid.x * resolution_ + origin_.x, grid.y * resolution_ + origin_.y);
    }

    inline Point2i ConvertToGrid(const Point2f& point) const
    {
        return Point2i((point.x - origin_.x) / resolution_, (point.y - origin_.y) / resolution_);
    }

   private:
    double resolution_;
    int width_;
    int height_;
    Point2f origin_;
};

} // namespace fescue_iox