#ifndef NAVIGATION_VELOCITY_SMOOTH_ALG_HPP
#define NAVIGATION_VELOCITY_SMOOTH_ALG_HPP

#include "geometry_msgs/twist__struct.h"

#include <string>
#include <vector>

namespace fescue_iox
{

struct VelocitySmoothVector3
{
    double x{0.0};
    double y{0.0};
    double z{0.0};
};

struct VelocitySmoothAlgInput
{
    uint64_t timestamp_ms{0};
    VelocitySmoothVector3 linear;
    VelocitySmoothVector3 angular;
    VelocitySmoothAlgInput(){};
    VelocitySmoothAlgInput(float x, float z, uint64_t t)
        : timestamp_ms{t}
        , linear{x, 0.0, 0.0}
        , angular{0.0, 0.0, z}
    {
    }
    VelocitySmoothAlgInput &operator=(const VelocitySmoothAlgInput &other)
    {
        if (this != &other)
        {
            timestamp_ms = other.timestamp_ms;
            linear = other.linear;
            angular = other.angular;
        }
        return *this;
    }
};

struct VelocitySmoothAlgResult
{
    VelocitySmoothVector3 linear;
    VelocitySmoothVector3 angular;
};

struct VelocitySmoothAlgParam
{
    int velocity_timeout{1};
    int smoothing_frequency{20};
    bool open_loop{true};
    bool scale_velocities{false};
    std::vector<double> max_velocities{0.3, 0.0, 0.6};
    std::vector<double> min_velocities{-0.3, 0.0, -0.6};
    std::vector<double> max_accels{5.0, 0.0, 2.5};
    std::vector<double> max_decels{-5.0, 0.0, -2.5};
    std::vector<double> deadband_velocities{0.01, 0.01, 0.01};
};

class NavigationVelocitySmoothAlg
{
public:
    NavigationVelocitySmoothAlg(const VelocitySmoothAlgParam &param);
    ~NavigationVelocitySmoothAlg();
    VelocitySmoothAlgResult DoVelocitySmooth(const VelocitySmoothAlgInput &input);
    int SetVelocitySmoothParam(const VelocitySmoothAlgParam &param);

private:
    bool ValidateTwist(const VelocitySmoothAlgInput &msg);
    double FindEtaConstraint(const double v_curr, const double v_cmd, const double accel, const double decel);
    double ApplyConstraints(const double v_curr, const double v_cmd, const double accel, const double decel, const double eta);

private:
    uint64_t last_command_time_{0};
    VelocitySmoothAlgInput command_;
    VelocitySmoothAlgInput last_cmd_;

    // Parameters
    uint64_t velocity_timeout_{1};
    int smoothing_frequency_{20};
    bool open_loop_{true};
    bool stopped_{false};
    bool scale_velocities_{false};
    std::vector<double> max_velocities_{0.3, 0.0, 0.6};
    std::vector<double> min_velocities_{-0.3, 0.0, -0.6};
    std::vector<double> max_accels_{5.0, 0.0, 2.5};
    std::vector<double> max_decels_{-5.0, 0.0, -2.5};
    std::vector<double> deadband_velocities_{0.01, 0.01, 0.01};
};

} // namespace fescue_iox

#endif
