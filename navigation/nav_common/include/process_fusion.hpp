#ifndef NAVIGATION_PROCESS_FUSION_HPP
#define NAVIGATION_PROCESS_FUSION_HPP

#include "data_type.hpp"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "sdf_map.hpp"

namespace fescue_iox
{

void GetFusionGrassDetectStatus(const fescue_msgs__msg__PerceptionFusionResult &data, GrassDetectStatus &status);

void GetFusionObstacleResult(const fescue_msgs__msg__PerceptionFusionResult &data, ObstacleResult &result);

void GetFusionBoundaryResult(const fescue_msgs__msg__PerceptionFusionResult &data, BoundaryResult &result);

void GetFusionOccupancyResult(const fescue_msgs__msg__PerceptionFusionResult &data, OccupancyResult &result);

void GetOptFusionOccupancyResult(const fescue_msgs__msg__PerceptionFusionResult &data, OccupancyResult &result);

std::shared_ptr<SDFMap> GetSDFMap(const OccupancyResult& occupancy_result, const Pose2f& cur_pose);

} // namespace fescue_iox

#endif
