#pragma once

#include <memory>

#include "data_type.hpp"
#include "multi_trajectory_tracker.hpp"
#include "ob_mower_msgs/perception_fusion_result_struct.h"

namespace fescue_iox
{

class ConnectPoseTracker {
public:
    ConnectPoseTracker(const TrajectoryPose& start_pose, const TrajectoryPose& end_pose, const OccupancyResult& start_pose_result);

    void Update(double time, const TrajectoryPose& pose, const OdomResult& odom_result);

    double GetLinearVelocity() const { return linear_velocity_; }
    double GetAngularVelocity() const { return angular_velocity_; }
    TrackerResult GetResult() const { return result_; }
    const std::vector<std::vector<TrajectoryPose>>& GetTrajs() const { return trajs_; }

private:
    double linear_velocity_ = 0;
    double angular_velocity_ = 0;
    std::vector<std::vector<TrajectoryPose>> trajs_;
    std::shared_ptr<MultiTrajectoryTracker> multi_trajectory_tracker_;
    TrackerResult result_ = TrackerResult::kInvalid;
};

}