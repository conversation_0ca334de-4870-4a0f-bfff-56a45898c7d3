#include "cross_region_node.hpp"

#include "cross_region_node_config.hpp"
#include "mower_sdk_version.h"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <fstream>
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

namespace fescue_iox
{
NavigationCrossRegionNode::NavigationCrossRegionNode(const std::string &node_name)
    : node_name_(node_name)
{
    InitWorkingDirectory();
    InitParam();
    InitLogger();
    InitSubscriber();
    InitPublisher();
    InitService();
    InitAlgorithm();
    InitHeartbeat();

#if PRINTF_IMU_DATA
    OpenImuDataFile();

#endif
}

NavigationCrossRegionNode::~NavigationCrossRegionNode()
{
    DeinitAlgorithm();

#if PRINTF_IMU_DATA
    if (imu_data_file_.is_open())
    {
        imu_data_file_.close();
        LOG_INFO("IMU data file closed");
    }

#endif
    LOG_WARN("NavigationCrossRegionNode exit!");
}

void NavigationCrossRegionNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void NavigationCrossRegionNode::InitParam()
{
    const std::string conf_file{"conf/navigation_cross_region_node/navigation_cross_region_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("NavigationCrossRegionNode create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("NavigationCrossRegionNode create config path failed!!!");
        }
    }
    if (!Config<NavigationCrossRegionNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init NavigationCrossRegionNode config parameters failed!");
    }
    NavigationCrossRegionNodeConfig config = Config<NavigationCrossRegionNodeConfig>::GetConfig();

    LOG_INFO("[navigation_cross_region_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[navigation_cross_region_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[navigation_cross_region_node] compile time: {}", _COMPILE_TIME_);

    LOG_INFO("{}", config.toString().c_str());

    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    cross_region_alg_conf_file_ = config.cross_region_alg_conf_file;

    if (!Config<NavigationCrossRegionNodeConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set NavigationCrossRegionNode config parameters failed!");
    }

    CreateDirectories(log_dir_);
}

void NavigationCrossRegionNode::InitAlgorithmParam()
{
    std::string conf_path = GetDirectoryPath(cross_region_alg_conf_file_);
    if (!conf_path.empty())
    {
        LOG_INFO("Navigation cross region algo create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("Navigation cross region algo create config path failed!!!");
        }
    }
    if (!Config<NavigationCrossRegionAlgConfig>::Init(cross_region_alg_conf_file_))
    {
        LOG_WARN("Init Navigation cross region algo config parameters failed!");
    }
    NavigationCrossRegionAlgConfig config = Config<NavigationCrossRegionAlgConfig>::GetConfig();
    LOG_INFO("{}", config.toString().c_str());
    ConfigParamToAlgParam(config, cross_region_param_);
    if (!Config<NavigationCrossRegionAlgConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set Navigation cross region algo config parameters failed!");
    }
}

void NavigationCrossRegionNode::InitAlgorithm()
{
    InitAlgorithmParam();

    cross_region_alg_ = std::make_unique<NavigationCrossRegionAlg>(cross_region_param_);

    cross_region_alg_->SetFeatureSelectCallback([this](const std::vector<FeatureSelectData> &data) {
        this->DealFeatureSelectCallback(data);
    });
    cross_region_alg_->SetCrossRegionRunningStateCallback([this](CrossRegionRunningState state) {
        this->DealCrossRegionRunningStateCallback(state);
    });
    cross_region_alg_->SetMarkLocationMarkIdCallback([this](int mark_id) -> bool {
        return this->DealMarkLocationMarkIdCallback(mark_id);
    });

    thread_running_.store(true);
    cross_region_thread_ = std::thread(&NavigationCrossRegionNode::CrossRegionThread, this);
}

void NavigationCrossRegionNode::DeinitAlgorithm()
{
    if (cross_region_alg_)
    {
        cross_region_alg_->ProhibitVelPublisher();
    }
    thread_running_.store(false);
    if (cross_region_thread_.joinable())
    {
        cross_region_thread_.join();
    }
}

void NavigationCrossRegionNode::InitLogger()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void NavigationCrossRegionNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

void NavigationCrossRegionNode::DealFeatureSelectCallback(const std::vector<FeatureSelectData> &data)
{
    if (!data.empty())
    {
        size_t data_size = data.size() > MAX_NAVIGATION_ALGO_NUM ? MAX_NAVIGATION_ALGO_NUM : data.size();
        fescue_msgs__msg__NavigationAlgoCtrlData nav_alg_ctrl_data;
        nav_alg_ctrl_data.sender.unsafe_assign("NavigationCrossRegionNode");
        for (size_t i = 0; i < data_size; i++)
        {
            fescue_msgs__msg__NavigationAlgoCtrlInfo info;
            info.type = static_cast<fescue_msgs__enum__NavigationAlgoType>(data.at(i).alg_id);
            info.state = static_cast<fescue_msgs__enum__NavigationAlgoState>(data.at(i).alg_status);
            nav_alg_ctrl_data.data.push_back(info);
        }
        pub_nav_alg_ctrl_->publishCopyOf(nav_alg_ctrl_data)
            .or_else([](auto &error) { std::cerr << "NavigationMowerNode nav_alg_ctrl_data Unable to publishCopyOf, error: " << error << std::endl; });
    }
}

void NavigationCrossRegionNode::DealCrossRegionRunningStateCallback(CrossRegionRunningState state)
{
    fescue_msgs__msg__CrossRegionStateData data;
    data.state = static_cast<fescue_msgs_enum__CrossRegionState>(state);
    pub_cross_region_state_->publishCopyOf(data)
        .or_else([](auto &error) { std::cerr << "NavigationCrossRegionNode pub_cross_region_state_ Unable to publishCopyOf, error: "
                                             << error << std::endl; });
}

void NavigationCrossRegionNode::InitSubscriber()
{
    sub_fusion_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>>(
        "fusion_result", 1, [this](const fescue_msgs__msg__PerceptionFusionResult &data, const std::string &event) {
            (void)event;
            DealPerceptionFusionResult(data);
        });

    sub_mark_loc_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__MarkLocationResult>>(
        "mark_location_result", 1, [this](const fescue_msgs__msg__MarkLocationResult &data, const std::string &event) {
            (void)event;
            DealMarkLocationResult(data);
        });

    sub_nav_alg_ctrl_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>>(
        "navigation_nav_alg_ctrl", 10, [this](const fescue_msgs__msg__NavigationAlgoCtrlData &data, const std::string &event) {
            (void)event;
            DealNavAlgCtrlResult(data);
        });

    sub_cross_region_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__CrossRegionStateData>>(
        "navigation_cross_region_state", 10, [this](const fescue_msgs__msg__CrossRegionStateData &data, const std::string &event) {
            (void)event;
            DealCrossRegionState(data);
        });

    sub_nav_running_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavigationRunningStateData>>(
        "navigation_running_state", 10, [this](const fescue_msgs__msg__NavigationRunningStateData &data, const std::string &event) {
            (void)event;
            DealNavRunningState(data);
        });

#if 1
    sub_mcu_imu_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuImu>>(
        "mcu_imu", 1, [this](const mower_msgs::msg::McuImu &data, const std::string &event) {
            (void)event;
            DealMcuImu(data);
        });
#else
    sub_soc_imu_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::SocImu>>(
        "soc_imu", 1, [this](const mower_msgs::msg::SocImu &data, const std::string &event) {
            (void)event;
            DealSocImu(data);
        });
#endif

    sub_mcu_motor_speed_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuMotorSpeed>>(
        "mcu_motor_speed", 1, [this](const mower_msgs::msg::McuMotorSpeed &data, const std::string &event) {
            (void)event;
            DealMcuMotorSpeed(data);
        });
}

void NavigationCrossRegionNode::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_nav_alg_ctrl_ = std::make_unique<iox_nav_alg_ctrl_publisher>(
        iox::capro::ServiceDescription{kNavigationNavAlgCtrlIox[0],
                                       kNavigationNavAlgCtrlIox[1],
                                       kNavigationNavAlgCtrlIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);

    pub_cross_region_state_ = std::make_unique<iox_cross_region_state_publisher>(
        iox::capro::ServiceDescription{kNavigationCrossRegionStateIox[0],
                                       kNavigationCrossRegionStateIox[1],
                                       kNavigationCrossRegionStateIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);

    pub_cross_region_final_result_ = std::make_unique<iox_cross_region_final_result_publisher>(
        iox::capro::ServiceDescription{kNavigationCrossRegionFinalResultIox[0],
                                       kNavigationCrossRegionFinalResultIox[1],
                                       kNavigationCrossRegionFinalResultIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
}

void NavigationCrossRegionNode::InitService()
{
    service_get_node_param_ = std::make_unique<IceoryxServerMower<get_node_param_request, get_node_param_response>>(
        "get_navigation_cross_region_node_param_request", 10U,
        [this](const get_node_param_request &request, get_node_param_response &response) {
            (void)request;
            response.success = GetCrossRegionNodeParam(response.data);
            LOG_INFO("Get navigation cross region node param execute {}", response.success);
        });

    service_set_node_param_ = std::make_unique<IceoryxServerMower<set_node_param_request, set_node_param_response>>(
        "set_navigation_cross_region_node_param_request", 10U,
        [this](const set_node_param_request &request, set_node_param_response &response) {
            response.success = SetCrossRegionNodeParam(request.data);
            LOG_INFO("Set navigation cross region node param execute {}", response.success);
        });

    service_get_alg_param_ = std::make_unique<IceoryxServerMower<get_alg_param_request, get_alg_param_response>>(
        "get_navigation_cross_region_alg_param_request", 10U,
        [this](const get_alg_param_request &request, get_alg_param_response &response) {
            (void)request;
            response.success = GetCrossRegionAlgParam(response.data);
            LOG_INFO("Get navigation cross region alg param execute {}", response.success);
        });

    service_set_alg_param_ = std::make_unique<IceoryxServerMower<set_alg_param_request, set_alg_param_response>>(
        "set_navigation_cross_region_alg_param_request", 10U,
        [this](const set_alg_param_request &request, set_alg_param_response &response) {
            response.success = SetCrossRegionAlgParam(request.data);
            LOG_INFO("Set navigation cross region alg param execute {}", response.success);
        });
}

void NavigationCrossRegionNode::CrossRegionThread()
{
    PerceptionFusionResult fusion_result;
    MarkLocationResult mark_loc_result;
    ImuData imu_data;

    while (thread_running_.load())
    {
        if (cross_region_enable_.load())
        {
            {
                std::scoped_lock lock(fusion_mutex_, mark_loc_mutex_, imu_mtx_);
                fusion_result = fusion_result_;
                mark_loc_result = mark_loc_result_;
                imu_data = imu_data_;
            }
            if (cross_region_alg_)
            {
                cross_region_alg_->DataConversion(mark_loc_result);
                auto result = cross_region_alg_->DoCrossRegion(fusion_result, mark_loc_result, imu_data);
                if (result.cross_region_completed)
                {
                    LOG_INFO("CrossRegionThread completed is {}!", result.cross_region_completed);
                    PublishCrossRegionFinalResult(result);
                    cross_region_enable_.store(false);

                    cross_region_alg_->ResetCrossRegionFlags();
                }
            }
        }
        else
        {
            LOG_DEBUG_THROTTLE(2000, "NavigationCrossRegionAlg disable!");
            if (cross_region_alg_)
            {
                cross_region_alg_->ResetCrossRegionFlags();
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }
}

void NavigationCrossRegionNode::DealPerceptionFusionResult(const fescue_msgs__msg__PerceptionFusionResult &msg)
{
    // switch (msg.boundary_state)
    // {
    // case 0:
    //     LOG_DEBUG("[CrossRegionNode] [fusion_result] No grass");
    //     break;
    // case 1:
    //     LOG_DEBUG("[CrossRegionNode] [fusion_result] All grass");
    //     break;
    // case 2:
    //     LOG_DEBUG("[CrossRegionNode] [fusion_result] Partial grass and obstacles");
    //     break;
    // default:
    //     LOG_DEBUG("Invalid perception grass detect status: {}", int(msg.boundary_state));
    //     break;
    // }

    std::lock_guard<std::mutex> lock(fusion_mutex_);
    GetFusionGrassDetectStatus(msg, fusion_result_.grass_detecte_status);
    GetFusionObstacleResult(msg, fusion_result_.obstacle_result);
    GetFusionBoundaryResult(msg, fusion_result_.boundary_result);
    fusion_result_.mower_point.x = msg.mower_point.x;
    fusion_result_.mower_point.y = msg.mower_point.y;
    fusion_result_.min_dist_point.x = msg.min_dist_point.x;
    fusion_result_.min_dist_point.y = msg.min_dist_point.y;
    fusion_result_.input_timestamp = msg.timestamp;
    fusion_result_.output_timestamp = msg.output_timestamp;
}

void NavigationCrossRegionNode::DealMarkLocationResult(const fescue_msgs__msg__MarkLocationResult &msg)
{
    std::lock_guard<std::mutex> lck(mark_loc_mutex_);
    mark_loc_result_.timestamp = msg.timestamp_ms;
    mark_loc_result_.mark_perception_status = msg.mark_perception_status;
    mark_loc_result_.mark_perception_direction = msg.mark_perception_direction;
    mark_loc_result_.detect_status = static_cast<int>(msg.detect_status);
    mark_loc_result_.roi_confidence = msg.roi_confidence;
    mark_loc_result_.target_direction = msg.target_direction;
    mark_loc_result_.mark_id = msg.mark_id;
    mark_loc_result_.xyzrpw.x = msg.pose.position.x;
    mark_loc_result_.xyzrpw.y = msg.pose.position.y;
    mark_loc_result_.xyzrpw.z = msg.pose.position.z;
    mark_loc_result_.xyzrpw.r = msg.roll;
    mark_loc_result_.xyzrpw.p = msg.pitch;
    mark_loc_result_.xyzrpw.w = msg.yaw;
    mark_loc_result_.mark_id_distance.clear();
    for (size_t i = 0; i < msg.mark_id_dis.size(); i++)
    {
        MarkIdDistance mark_id_dis;
        mark_id_dis.mark_id = msg.mark_id_dis[i].id;
        mark_id_dis.distance = msg.mark_id_dis[i].distance;
        mark_loc_result_.mark_id_distance.push_back(mark_id_dis);
    }
    if (cross_region_alg_)
    {
        cross_region_alg_->SetMarkLocationResult(mark_loc_result_);
    }
}

void NavigationCrossRegionNode::DealNavAlgCtrlResult(const fescue_msgs__msg__NavigationAlgoCtrlData &msg)
{
    for (size_t i = 0; i < msg.data.size(); i++)
    {
        if (msg.data[i].type == FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_CROSS_REGION)
        {
            switch (msg.data[i].state)
            {
            case FESCUE_MSGS_ENUM_NAV_ALGO_STATE_DISABLE:
                cross_region_enable_.store(false);
                SetCrossRegionVelPublisherProhibit(true); // Enable velocity publishing prohibition
                break;
            case FESCUE_MSGS_ENUM_NAV_ALGO_STATE_ENABLE:
                cross_region_enable_.store(true);
                SetCrossRegionVelPublisherProhibit(false); // Disable velocity publishing prohibition
                break;
            default:
                break;
            }
            break;
        }
    }
}

void NavigationCrossRegionNode::DealCrossRegionState(const fescue_msgs__msg__CrossRegionStateData &msg)
{
    std::lock_guard<std::mutex> lck(cross_region_mtx_);
    cross_region_state_ = static_cast<CrossRegionRunningState>(msg.state);
}

void NavigationCrossRegionNode::DealNavRunningState(const fescue_msgs__msg__NavigationRunningStateData &data)
{
    if (cross_region_alg_)
    {
        cross_region_alg_->SetAlgoRunningState(static_cast<MowerRunningState>(data.state));
    }
}

void NavigationCrossRegionNode::DealMcuImu(const mower_msgs::msg::McuImu &data)
{
    std::lock_guard<std::mutex> lock(imu_mtx_);

    float angular_velocity_z = -data.angular_velocity_z;

    if (enable_imu_filter_)
    {
        filter_state_.accel_x = LowPassFilter(data.linear_acceleration_x, filter_state_.accel_x, alpha_imu_);
        filter_state_.accel_y = LowPassFilter(data.linear_acceleration_y, filter_state_.accel_y, alpha_imu_);
        filter_state_.accel_z = LowPassFilter(data.linear_acceleration_z, filter_state_.accel_z, alpha_imu_);
        filter_state_.gyro_x = LowPassFilter(data.angular_velocity_x, filter_state_.gyro_x, alpha_imu_);
        filter_state_.gyro_y = LowPassFilter(data.angular_velocity_y, filter_state_.gyro_y, alpha_imu_);
        filter_state_.gyro_z = LowPassFilter(angular_velocity_z, filter_state_.gyro_z, alpha_imu_);

        imu_data_.linear_acceleration_x = filter_state_.accel_x;
        imu_data_.linear_acceleration_y = filter_state_.accel_y;
        imu_data_.linear_acceleration_z = filter_state_.accel_z;
        imu_data_.angular_velocity_x = filter_state_.gyro_x;
        imu_data_.angular_velocity_y = filter_state_.gyro_y;
        imu_data_.angular_velocity_z = filter_state_.gyro_z;
        imu_data_.frame_timestamp = data.frame_timestamp;
        imu_data_.system_timestamp = data.system_timestamp;

#if PRINTF_IMU_DATA
        if (imu_data_file_.is_open())
        {
            imu_data_file_ << data.system_timestamp << ","
                           << data.linear_acceleration_x << ","
                           << data.linear_acceleration_y << ","
                           << data.linear_acceleration_z << ","
                           << data.angular_velocity_x << ","
                           << data.angular_velocity_y << ","
                           << angular_velocity_z << ","
                           << filter_state_.accel_x << ","
                           << filter_state_.accel_y << ","
                           << filter_state_.accel_z << ","
                           << filter_state_.gyro_x << ","
                           << filter_state_.gyro_y << ","
                           << filter_state_.gyro_z << std::endl;
        }

#endif

        SetImuData(imu_data_);
    }
    else
    {
        imu_data_.linear_acceleration_x = data.linear_acceleration_x;
        imu_data_.linear_acceleration_y = data.linear_acceleration_y;
        imu_data_.linear_acceleration_z = data.linear_acceleration_z;
        imu_data_.angular_velocity_x = data.angular_velocity_x;
        imu_data_.angular_velocity_y = data.angular_velocity_y;
        imu_data_.angular_velocity_z = angular_velocity_z;
        imu_data_.frame_timestamp = data.frame_timestamp;
        imu_data_.system_timestamp = data.system_timestamp;

#if PRINTF_IMU_DATA
        if (imu_data_file_.is_open())
        {
            imu_data_file_ << data.system_timestamp << ","
                           << data.linear_acceleration_x << ","
                           << data.linear_acceleration_y << ","
                           << data.linear_acceleration_z << ","
                           << data.angular_velocity_x << ","
                           << data.angular_velocity_y << ","
                           << angular_velocity_z << ","
                           << data.linear_acceleration_x << ","
                           << data.linear_acceleration_y << ","
                           << data.linear_acceleration_z << ","
                           << data.angular_velocity_x << ","
                           << data.angular_velocity_y << ","
                           << angular_velocity_z << std::endl;
        }

#endif

        SetImuData(imu_data_);
    }
}

void NavigationCrossRegionNode::DealSocImu(const mower_msgs::msg::SocImu &data)
{
    std::lock_guard<std::mutex> lock(imu_mtx_);

    if (enable_imu_filter_)
    {
        filter_state_.accel_x = LowPassFilter(data.linear_acceleration_x, filter_state_.accel_x, alpha_imu_);
        filter_state_.accel_y = LowPassFilter(data.linear_acceleration_y, filter_state_.accel_y, alpha_imu_);
        filter_state_.accel_z = LowPassFilter(data.linear_acceleration_z, filter_state_.accel_z, alpha_imu_);
        filter_state_.gyro_x = LowPassFilter(data.angular_velocity_x, filter_state_.gyro_x, alpha_imu_);
        filter_state_.gyro_y = LowPassFilter(data.angular_velocity_y, filter_state_.gyro_y, alpha_imu_);
        filter_state_.gyro_z = LowPassFilter(data.angular_velocity_z, filter_state_.gyro_z, alpha_imu_);

        imu_data_.linear_acceleration_x = filter_state_.accel_x;
        imu_data_.linear_acceleration_y = filter_state_.accel_y;
        imu_data_.linear_acceleration_z = filter_state_.accel_z;
        imu_data_.angular_velocity_x = filter_state_.gyro_x;
        imu_data_.angular_velocity_y = filter_state_.gyro_y;
        imu_data_.angular_velocity_z = filter_state_.gyro_z;
        imu_data_.frame_timestamp = data.frame_timestamp;
        imu_data_.system_timestamp = data.system_timestamp;

#if PRINTF_IMU_DATA
        if (imu_data_file_.is_open())
        {
            imu_data_file_ << data.system_timestamp << ","
                           << data.linear_acceleration_x << ","
                           << data.linear_acceleration_y << ","
                           << data.linear_acceleration_z << ","
                           << data.angular_velocity_x << ","
                           << data.angular_velocity_y << ","
                           << data.angular_velocity_z << ","
                           << filter_state_.accel_x << ","
                           << filter_state_.accel_y << ","
                           << filter_state_.accel_z << ","
                           << filter_state_.gyro_x << ","
                           << filter_state_.gyro_y << ","
                           << filter_state_.gyro_z << std::endl;
        }

#endif
    }
    else
    {
        imu_data_.linear_acceleration_x = data.linear_acceleration_x;
        imu_data_.linear_acceleration_y = data.linear_acceleration_y;
        imu_data_.linear_acceleration_z = data.linear_acceleration_z;
        imu_data_.angular_velocity_x = data.angular_velocity_x;
        imu_data_.angular_velocity_y = data.angular_velocity_y;
        imu_data_.angular_velocity_z = data.angular_velocity_z;
        imu_data_.frame_timestamp = data.frame_timestamp;
        imu_data_.system_timestamp = data.system_timestamp;

#if PRINTF_IMU_DATA
        if (imu_data_file_.is_open())
        {
            imu_data_file_ << data.system_timestamp << ","
                           << data.linear_acceleration_x << ","
                           << data.linear_acceleration_y << ","
                           << data.linear_acceleration_z << ","
                           << data.angular_velocity_x << ","
                           << data.angular_velocity_y << ","
                           << data.angular_velocity_z << ","
                           << data.linear_acceleration_x << ","
                           << data.linear_acceleration_y << ","
                           << data.linear_acceleration_z << ","
                           << data.angular_velocity_x << ","
                           << data.angular_velocity_y << ","
                           << data.angular_velocity_z << std::endl;
        }

#endif
    }
}

void NavigationCrossRegionNode::SetImuData(const ImuData &imu_data)
{
    if (cross_region_alg_)
    {
        cross_region_alg_->SetImuData(imu_data);
    }
}

void NavigationCrossRegionNode::DealMcuMotorSpeed(const mower_msgs::msg::McuMotorSpeed &data)
{
    std::lock_guard<std::mutex> lock(motor_speed_mtx_);

    if (enable_motor_speed_filter_)
    {
        filter_state_.motor_speed_left = LowPassFilter(data.motor_speed_left, filter_state_.motor_speed_left, alpha_speed_);
        filter_state_.motor_speed_right = LowPassFilter(data.motor_speed_right, filter_state_.motor_speed_right, alpha_speed_);
        motor_speed_data_.motor_speed_left = filter_state_.motor_speed_left;
        motor_speed_data_.motor_speed_right = filter_state_.motor_speed_right;
        motor_speed_data_.frame_timestamp = data.frame_timestamp;
        motor_speed_data_.system_timestamp = data.system_timestamp;
        // motor_speed_data_.current_left = data.current_left; // 电流
        // motor_speed_data_.current_right = data.current_right;

        SetMotorSpeedData(motor_speed_data_);
    }
    else
    {
        motor_speed_data_.motor_speed_left = data.motor_speed_left;
        motor_speed_data_.motor_speed_right = data.motor_speed_right;
        motor_speed_data_.frame_timestamp = data.frame_timestamp;
        motor_speed_data_.system_timestamp = data.system_timestamp;
        // update encoder datas

        SetMotorSpeedData(motor_speed_data_);
    }
}

void NavigationCrossRegionNode::SetMotorSpeedData(const MotorSpeedData &motor_speed_data)
{
    if (cross_region_alg_)
    {
        cross_region_alg_->SetMotorSpeedData(motor_speed_data);
    }
}

float NavigationCrossRegionNode::LowPassFilter(float new_value, float prev_value, float alpha)
{
    return alpha * new_value + (1.0f - alpha) * prev_value;
}

bool NavigationCrossRegionNode::GetCrossRegionAlgParam(fescue_msgs__msg__NavigationCrossRegionAlgParam &data)
{
    if (!cross_region_alg_)
    {
        return false;
    }
    CrossRegionAlgParam param;
    cross_region_alg_->GetCrossRegionAlgParam(param);
    data.cross_region_linear = param.cross_region_linear;
    data.cross_region_angular = param.cross_region_angular;
    data.max_distance_threshold = param.max_distance_threshold;
    data.min_distance_threshold = param.min_distance_threshold;
    data.cross_region_special_linear = param.cross_region_special_linear;
    data.cross_region_special_angular = param.cross_region_special_angular;
    data.dis_tolerance = param.dis_tolerance;
    data.cross_region_angle_compensation = param.cross_region_angle_compensation;
    data.channel_stop_pose_x = param.channel_stop_pose_x;
    data.grass_count_threshold = param.grass_count_threshold;
    data.edge_mode_direction = param.edge_mode_direction;
    data.channel_width = param.channel_width;
    data.camera_2_center_dis = param.camera_2_center_dis;
    data.adjust_mode_x_direction_threshold = param.adjust_mode_x_direction_threshold;
    data.mark_distance_threshold = param.mark_distance_threshold;
    data.perception_drive_cooldown_time_threshold = param.perception_drive_cooldown_time_threshold;
    data.cross_region_adjust_displace = param.cross_region_adjust_displace;
    return true;
}

bool NavigationCrossRegionNode::SetCrossRegionAlgParam(const fescue_msgs__msg__NavigationCrossRegionAlgParam &data)
{
    if (!cross_region_alg_)
    {
        return false;
    }

    NavigationCrossRegionAlgConfig config = Config<NavigationCrossRegionAlgConfig>::GetConfig();
    config.cross_region_linear = data.cross_region_linear;
    config.cross_region_angular = data.cross_region_angular;
    config.max_distance_threshold = data.max_distance_threshold;
    config.min_distance_threshold = data.min_distance_threshold;
    config.cross_region_special_linear = data.cross_region_special_linear;
    config.cross_region_special_angular = data.cross_region_special_angular;
    config.dis_tolerance = data.dis_tolerance;
    config.cross_region_angle_compensation = data.cross_region_angle_compensation;
    config.channel_stop_pose_x = data.channel_stop_pose_x;
    config.grass_count_threshold = data.grass_count_threshold;
    config.edge_mode_direction = data.edge_mode_direction;
    config.channel_width = data.channel_width;
    config.camera_2_center_dis = data.camera_2_center_dis;
    config.adjust_mode_x_direction_threshold = data.adjust_mode_x_direction_threshold;
    config.mark_distance_threshold = data.mark_distance_threshold;
    config.perception_drive_cooldown_time_threshold = data.perception_drive_cooldown_time_threshold;
    config.cross_region_adjust_displace = data.cross_region_adjust_displace;
    if (!Config<NavigationCrossRegionAlgConfig>::SetConfig(config))
    {
        LOG_WARN("Set NavigationCrossRegionAlg config parameters failed!");
        return false;
    }
    LOG_INFO("New NavigationCrossRegionAlg param: {}", config.toString().c_str());
    CrossRegionAlgParam param;
    ConfigParamToAlgParam(config, param);
    cross_region_alg_->SetCrossRegionAlgParam(param);
    return true;
}

bool NavigationCrossRegionNode::GetCrossRegionNodeParam(fescue_msgs__msg__NavigationCrossRegionNodeParam &data)
{
    data.console_log_level.unsafe_assign(console_log_level_.c_str());
    data.file_log_level.unsafe_assign(file_log_level_.c_str());
    return true;
}

bool NavigationCrossRegionNode::SetCrossRegionNodeParam(const fescue_msgs__msg__NavigationCrossRegionNodeParam &data)
{
    console_log_level_ = std::string(data.console_log_level.c_str());
    file_log_level_ = std::string(data.file_log_level.c_str());
    InitLogger();
    NavigationCrossRegionNodeConfig config = Config<NavigationCrossRegionNodeConfig>::GetConfig();
    config.common_conf.console_log_level = console_log_level_;
    config.common_conf.file_log_level = file_log_level_;
    if (!Config<NavigationCrossRegionNodeConfig>::SetConfig(config))
    {
        LOG_WARN("Set NavigationCrossRegionNode config parameters failed!");
        return false;
    }
    LOG_INFO("New NavigationCrossRegionNode params: {}", config.toString().c_str());
    return true;
}

void NavigationCrossRegionNode::ConfigParamToAlgParam(const NavigationCrossRegionAlgConfig &config, CrossRegionAlgParam &param)
{
    param.cross_region_linear = config.cross_region_linear;
    param.cross_region_angular = config.cross_region_angular;
    param.max_distance_threshold = config.max_distance_threshold;
    param.min_distance_threshold = config.min_distance_threshold;
    param.cross_region_special_linear = config.cross_region_special_linear;
    param.cross_region_special_angular = config.cross_region_special_angular;
    param.dis_tolerance = config.dis_tolerance;
    param.cross_region_angle_compensation = config.cross_region_angle_compensation;

    param.channel_stop_pose_x = config.channel_stop_pose_x;
    param.grass_count_threshold = config.grass_count_threshold;
    param.edge_mode_direction = config.edge_mode_direction;
    param.channel_width = config.channel_width;
    param.camera_2_center_dis = config.camera_2_center_dis;
    param.adjust_mode_x_direction_threshold = config.adjust_mode_x_direction_threshold;

    param.mark_distance_threshold = config.mark_distance_threshold;
    param.perception_drive_cooldown_time_threshold = config.perception_drive_cooldown_time_threshold;

    param.cross_region_adjust_displace = config.cross_region_adjust_displace;
}

void NavigationCrossRegionNode::AlgParamToConfigParam(NavigationCrossRegionAlgConfig &config, const CrossRegionAlgParam &param)
{
    config.cross_region_linear = param.cross_region_linear;
    config.cross_region_angular = param.cross_region_angular;
    config.max_distance_threshold = param.max_distance_threshold;
    config.min_distance_threshold = param.min_distance_threshold;
    config.cross_region_special_linear = param.cross_region_special_linear;
    config.cross_region_special_angular = param.cross_region_special_angular;
    config.dis_tolerance = param.dis_tolerance;
    config.cross_region_angle_compensation = param.cross_region_angle_compensation;

    config.channel_stop_pose_x = param.channel_stop_pose_x;
    config.grass_count_threshold = param.grass_count_threshold;
    config.edge_mode_direction = param.edge_mode_direction;
    config.channel_width = param.channel_width;
    config.camera_2_center_dis = param.camera_2_center_dis;
    config.adjust_mode_x_direction_threshold = param.adjust_mode_x_direction_threshold;

    config.mark_distance_threshold = param.mark_distance_threshold;
    config.perception_drive_cooldown_time_threshold = param.perception_drive_cooldown_time_threshold;

    config.cross_region_adjust_displace = param.cross_region_adjust_displace;
}

void NavigationCrossRegionNode::PublishCrossRegionFinalResult(const CrossRegionAlgResult &result)
{
    fescue_msgs__msg__NavCrossRegionFinalResult final_result;
    final_result.completed = result.cross_region_completed;
    final_result.timestamp = GetTimestampMs();

    if (result.cross_region_status == CrossRegionStatus::Successed)
    {
        final_result.result = true;
    }
    else if (result.cross_region_status == CrossRegionStatus::Failed)
    {
        final_result.result = false;
    }

    pub_cross_region_final_result_->publishCopyOf(final_result).or_else([](auto &error) {
        (void)error;
        LOG_ERROR("NavigationCrossRegionNode Unable to publishCopyOf recharge final result");
    });
}

bool NavigationCrossRegionNode::DealMarkLocationMarkIdCallback(int mark_id)
{
    auto client = std::make_unique<IceoryxClientMower<fescue_msgs__srv__SetDetectMarkId_Request,
                                                      fescue_msgs__srv__SetDetectMarkId_Response>>("mark_location_set_detect_mark_id");

    auto request_handler = [](const fescue_msgs__srv__SetDetectMarkId_Request &request_input,
                              fescue_msgs__srv__SetDetectMarkId_Request &request_send) -> void {
        request_send.data.mark_id = request_input.data.mark_id;
    };

    auto response_handler = [](const fescue_msgs__srv__SetDetectMarkId_Response &response_receive,
                               fescue_msgs__srv__SetDetectMarkId_Response &response_output) -> bool {
        response_output.success = response_receive.success;
        return response_output.success;
    };

    fescue_msgs__srv__SetDetectMarkId_Request request_input;
    fescue_msgs__srv__SetDetectMarkId_Response response_output;
    request_input.data.mark_id = mark_id;
    return client->SendRequest(request_input, response_output, request_handler, response_handler);
}

#if PRINTF_IMU_DATA

void NavigationCrossRegionNode::OpenImuDataFile()
{
    std::string file_path = log_dir_ + "/imu_data.txt";
    imu_data_file_.open(file_path, std::ios::out);
    if (imu_data_file_.is_open())
    {
        LOG_INFO("IMU数据文件已打开: {}", file_path.c_str());
        // 写入标题行
        imu_data_file_ << "timestamp,accel_x,accel_y,accel_z,gyro_x,gyro_y,gyro_z,filtered_accel_x,filtered_accel_y,filtered_accel_z,filtered_gyro_x,filtered_gyro_y,filtered_gyro_z" << std::endl;
    }
    else
    {
        LOG_ERROR("无法打开IMU数据文件: {}", file_path.c_str());
    }
}
#endif

} // namespace fescue_iox
