#include "behavior.hpp"

#include "mower_sdk_version.h"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

namespace fescue_iox
{

NavigationBehaviorAlg::NavigationBehaviorAlg(const BehaviorAlgParam &param)
    : vel_publisher_(std::make_unique<VelocityPublisher>("Behavior"))
{
    SetBehaviorAlgParam(param);

    InitSlipDetection();
}

NavigationBehaviorAlg::~NavigationBehaviorAlg()
{
    PublishVelocity(0.0, 0.0, 1000); // Send continuously for 1s

    DeinitSlipDetection();
    LOG_WARN("NavigationBehaviorAlg exit!");
}

void NavigationBehaviorAlg::InitSlipDetection()
{
    slip_detection_running_.store(true);
    slip_detection_thread_ = std::thread(&NavigationBehaviorAlg::SlipDetectionThread, this);
}

void NavigationBehaviorAlg::DeinitSlipDetection()
{
    slip_detection_running_.store(false);
    if (slip_detection_thread_.joinable())
    {
        slip_detection_thread_.join();
    }
}

void NavigationBehaviorAlg::SlipDetectionThread()
{
    while (slip_detection_running_.load())
    {
        // Get the latest motor speed and motion detection data
        MotorSpeedData motor_speed_data;
        MotionDetectionResult motion_detection_result;

        {
            std::lock_guard<std::mutex> lock(motor_speed_mtx_);
            motor_speed_data = motor_speed_data_;
        }

        {
            std::lock_guard<std::mutex> lock(motion_detection_result_mtx_);
            motion_detection_result = motion_detection_result_;
        }

        // Perform slip detection
        bool current_slip = IsWheelSlipping(motor_speed_data, motion_detection_result, wheel_radius_, wheel_base_);
        // LOG_ERROR("[MowerAlg] [SlipDetectionThread] current_slip({})", current_slip);

        // Update slip status
        is_slipping_detected_.store(current_slip);

        // Control detection frequency
        // std::this_thread::sleep_for(std::chrono::milliseconds(1000 / slip_detection_frequency_));
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }
}

void NavigationBehaviorAlg::SetMotorSpeedData(const MotorSpeedData &motor_speed_data)
{
    std::lock_guard<std::mutex> lock(motor_speed_mtx_);
    motor_speed_data_ = motor_speed_data;
}

void NavigationBehaviorAlg::SetMotionDetectionResult(const MotionDetectionResult &motion_detection_result)
{
    std::lock_guard<std::mutex> lock(motion_detection_result_mtx_);
    motion_detection_result_ = motion_detection_result;
}

void NavigationBehaviorAlg::SetBehaviorAlgParam(const BehaviorAlgParam &param)
{
    /****************************************Collision Recovery****************************************************/
    collision_backup_speed_ = param.collision_backup_speed;       // Backward speed (m/s)/*param*/
    collision_backup_duration_ = param.collision_backup_duration; // Backward duration (ms)/*param*/
    collision_turn_angle_ = param.collision_turn_angle;           // Turning angle (45 degrees)/*param*/
    collision_turn_speed_ = param.collision_turn_speed;           // Turning speed (rad/s)/*param*/

    /****************************************Lift Recovery****************************************************/
    lift_backup_speed_ = param.lift_backup_speed;       // Backward speed during recovery phase m/s/*param*/
    lift_backup_duration_ = param.lift_backup_duration; // Backward duration during recovery phase ms/*param*/

    /****************************************Unstuck Recovery****************************************************/
    backup_speed_ = param.backup_speed;         // Backward speed (m/s)/*param*/
    turn_angle_ = param.turn_angle;             // Rotation angle (45 degrees)/*param*/
    forward_speed_ = param.forward_speed;       // Forward speed (m/s)/*param*/
    backup_duration_ = param.backup_duration;   // Backward duration (ms)/*param*/
    turn_duration_ = param.turn_duration;       // Rotation duration (ms)/*param*/
    forward_duration_ = param.forward_duration; // Forward duration (ms)/*param*/
}

void NavigationBehaviorAlg::GetBehaviorAlgParam(BehaviorAlgParam &param)
{
    /****************************************Collision Recovery****************************************************/
    param.collision_backup_speed = collision_backup_speed_;       // Backward speed (m/s)/*param*/
    param.collision_backup_duration = collision_backup_duration_; // Backward duration (ms)/*param*/
    param.collision_turn_angle = collision_turn_angle_;           // Turning angle (45 degrees)/*param*/
    param.collision_turn_speed = collision_turn_speed_;           // Turning speed (rad/s)/*param*/

    /****************************************Lift Recovery****************************************************/
    param.lift_backup_speed = lift_backup_speed_;       // Backward speed during recovery phase m/s/*param*/
    param.lift_backup_duration = lift_backup_duration_; // Backward duration during recovery phase ms/*param*/

    /****************************************Unstuck Recovery****************************************************/
    param.backup_speed = backup_speed_;         // Backward speed (m/s)/*param*/
    param.turn_angle = turn_angle_;             // Rotation angle (45 degrees)/*param*/
    param.forward_speed = forward_speed_;       // Forward speed (m/s)/*param*/
    param.backup_duration = backup_duration_;   // Backward duration (ms)/*param*/
    param.turn_duration = turn_duration_;       // Rotation duration (ms)/*param*/
    param.forward_duration = forward_duration_; // Forward duration (ms)/*param*/
}

void NavigationBehaviorAlg::SetAlgoRunningState(MowerRunningState state)
{
    LOG_INFO("NavigationBehaviorAlg running state: {}", static_cast<int>(state));
    mower_running_state_ = state;
    if (state == MowerRunningState::RUNNING)
    {
        ResumeVelocity();
    }
    else if (state == MowerRunningState::PAUSE)
    {
        PauseVelocity();
    }
    else
    {
        LOG_DEBUG("[NavigationBehaviorAlg] Unknown state {}!", static_cast<int>(state));
    }
}

void NavigationBehaviorAlg::ProhibitVelPublisher()
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(0, 0);
        vel_publisher_->SetProhibitFlag(true);
    }
}

void NavigationBehaviorAlg::PublishVelocity(float linear, float angular, uint64_t duration_ms)
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(linear, angular, duration_ms);
        if (duration_ms > 0)
        {
            while (!vel_publisher_->IsExecutionCompleted())
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
    }
}

void NavigationBehaviorAlg::PublishZeroVelocity()
{
    PublishVelocity(0, 0, 50);
}

void NavigationBehaviorAlg::PauseVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->PauseVelocity();
    }
}

void NavigationBehaviorAlg::ResumeVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->ResumeVelocity();
    }
}

void NavigationBehaviorAlg::ShowMowerRunningInfo()
{
    // Print the time difference
    // LOG_WARN("[Behavior] The elapsed time (seconds) for edge perception drive cooling: ({})!", perception_drive_duration_.count());
}

void NavigationBehaviorAlg::ResetBehaviorFlags()
{
    behavior_status_ = BehaviorRunningState::UNDEFINED;

    /****************************************Collision Recovery****************************************************/
    collision_state_ = CollisionRecoveryState::NORMAL;
    collision_recovery_completed_ = false;
    collision_recovery_succeed_ = false;

    /****************************************Lift Recovery****************************************************/
    lift_state_ = LiftState::NORMAL;
    lift_completed_ = false;
    lift_succeed_ = false;

    /****************************************Unstuck Recovery****************************************************/
    unstuck_state_ = UnstuckState::NORMAL;
    unstuck_completed_ = false;
    unstuck_succeed_ = false;
    unstuck_attempts_ = 0; // Current number of attempts

    /****************************************Slip and Stall Detection and Recovery****************************************************/
    wheel_slip_state_ = WheelSlipRecoveryState::NORMAL;
    slipping_recovery_completed_ = false;
    slipping_recovery_succeed_ = false;
}

void NavigationBehaviorAlg::SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback)
{
    feature_select_callback_ = callback;
}

void NavigationBehaviorAlg::SetBehaviorRunningStateCallback(std::function<void(BehaviorRunningState)> callback)
{
    behavior_running_state_callback_ = callback;
}

const char *NavigationBehaviorAlg::GetVersion()
{
    return "V1.1.0";
}

void NavigationBehaviorAlg::DealFeatureSelect(ThreadControl control, bool state)
{
    std::vector<FeatureSelectData> feature_data;
    feature_data.clear();
    FeatureSelectData feature{control, static_cast<int>(NavAlgCtrlState::IGNORE)};

    feature.alg_status = state ? static_cast<int>(NavAlgCtrlState::ENABLE) : static_cast<int>(NavAlgCtrlState::DISABLE);

    feature_data.push_back(feature);

    if (feature_select_callback_ && !feature_data.empty())
    {
        feature_select_callback_(feature_data);
    }
}

void NavigationBehaviorAlg::UpdateBehaviorRunningState(BehaviorRunningState state)
{
    if (behavior_running_state_callback_)
    {
        behavior_running_state_callback_(state);
    }
}

/**
 * @brief Rotary motion control
 *
 * @param yaw_des
 * @param yaw_first
 */
void NavigationBehaviorAlg::ControlRotaryMotion(const float &yaw_des, const float &yaw_first, const float &vel_angular)
{
    float sign = UnifyAngle(yaw_des - yaw_first) >= 0.0 ? 1.0 : -1.0; // Default rotation direction 1.0: left, -1.0: right
    if (sign > 0)
    {
        LOG_DEBUG("[Behavior] Rotation direction: Left");
    }
    else
    {
        LOG_DEBUG("[Behavior] Rotation direction: Right");
    }

    float ang_err = fabsf(UnifyAngle(yaw_des - yaw_first));
    uint64_t t = (ang_err / vel_angular) * 1000; // Turning duration ms
    LOG_DEBUG("[Behavior] Rotation angle = {}", Radians2Degrees(ang_err));
    LOG_DEBUG("[Behavior] Angular velocity = {}, Time = {}", sign * vel_angular, ang_err / vel_angular);
    PublishVelocity(0, sign * vel_angular, t);
}

/**
 * @brief Linear motion control
 *
 * @param pass_point
 * @param location
 */
void NavigationBehaviorAlg::ControlLinearMotion(const float &pass_point, const float &location,
                                                const float &vel_linear, const int &reverse)
{
    float dis = fabsf(pass_point - location);
    uint64_t t = (dis / vel_linear) * 1000;
    LOG_DEBUG("[Behavior] Straight distance dis = {}", dis);
    LOG_DEBUG("[Behavior] Straight speed = {}, Time = {}", reverse * vel_linear, dis / vel_linear);
    PublishVelocity(reverse * vel_linear, 0, t);
}

bool NavigationBehaviorAlg::IsStuck(/*const SensorData &sensor_data*/)
{
    // // If the wheel speed is greater than the threshold but the actual moving speed is less than the threshold, it is considered stuck
    // return sensor_data.wheel_speed > wheel_speed_threshold_ &&
    //        sensor_data.actual_speed < actual_speed_threshold_;

    is_stucked_ = false;
    return false;
}

void NavigationBehaviorAlg::SetCollisionStatus(const McuExceptionStatus &mcu_exception_status)
{
    if (mcu_exception_status == McuExceptionStatus::COLLISION)
    {
        LOG_DEBUG("[SetCollisionStatus] Collision detected");
        is_collision_detected_ = true;
    }
    else
    {
        LOG_DEBUG("[SetCollisionStatus] No collision detected");
        is_collision_detected_ = false;
    }
}

void NavigationBehaviorAlg::SetLiftedStatus(const McuExceptionStatus &mcu_exception_status)
{
    if (mcu_exception_status == McuExceptionStatus::LIFTING)
    {
        LOG_DEBUG("[SetLiftedStatus] Lifting detected");
        is_lifted_ = true;
    }
    else
    {
        LOG_DEBUG("[SetLiftedStatus] No lifting detected");
        is_lifted_ = false;
    }
}

void NavigationBehaviorAlg::ShowBehaviorPrint(BehaviorRunningState &behavior_state)
{
    std::string state_str;
    switch (behavior_state)
    {
    case BehaviorRunningState::RUNNING:
        state_str = "RUNNING";
        break;
    case BehaviorRunningState::SUCCESS:
        state_str = "SUCCESS";
        break;
    case BehaviorRunningState::FAILURE:
        state_str = "FAILURE";
        break;
    default:
        state_str = "UNDEFINED";
    }
    LOG_INFO_THROTTLE(1000, "[DoBehavior] behavior_state：({}) {}", static_cast<int>(behavior_state), state_str);
}

BehaviorAlgResult NavigationBehaviorAlg::DoBehavior(PerceptionFusionResult &fusion_result,
                                                    McuExceptionStatus &mcu_exception_status,
                                                    const ImuData &imu_data,
                                                    const MotorSpeedData &motor_speed_data,
                                                    const MotionDetectionResult &motion_detection_result)
{
    (void)imu_data;
    (void)motor_speed_data;
    (void)motion_detection_result;
    if (mower_running_state_ == MowerRunningState::PAUSE)
    {
        LOG_WARN_THROTTLE(3000, "[Behavior] DoBehavior() is PAUSE!");
        return BehaviorAlgResult(false, BehaviorStatus::InProgress);
    }

    // Handle lifting and collision data
    SetCollisionStatus(mcu_exception_status);
    SetLiftedStatus(mcu_exception_status);

    // Slipping and stalling handling
    // bool is_stalling = IsMotorStalling(motor_speed_data);
    // bool is_slipping = IsWheelSlipping(motor_speed_data, imu_data, wheel_radius_, wheel_base_);

    // if (is_stalling)
    // {
    //     HandleMotorStall();
    //     return BehaviorAlgResult(true, BehaviorStatus::InProgress);
    // }

    // Print
    ShowBehaviorPrint(behavior_status_);

    // Wet slip handling
    HandleWheelSlip(fusion_result);
    if (slipping_recovery_completed_)
    {
        if (slipping_recovery_succeed_)
        {
            {
                behavior_status_ = BehaviorRunningState::SUCCESS;
                UpdateBehaviorRunningState(behavior_status_);

                ShowBehaviorPrint(behavior_status_);
            }
            return BehaviorAlgResult(true, BehaviorStatus::Successed);
        }
        else
        {
            {
                behavior_status_ = BehaviorRunningState::FAILURE;
                UpdateBehaviorRunningState(behavior_status_);

                ShowBehaviorPrint(behavior_status_);
            }
            return BehaviorAlgResult(true, BehaviorStatus::Failed);
        }
    }

    // Handle lift recovery
    HandleLift();
    if (lift_completed_)
    {
        if (lift_succeed_)
        {
            {
                behavior_status_ = BehaviorRunningState::SUCCESS;
                UpdateBehaviorRunningState(behavior_status_);

                ShowBehaviorPrint(behavior_status_);
            }
            return BehaviorAlgResult(true, BehaviorStatus::Successed);
        }
        else
        {
            {
                behavior_status_ = BehaviorRunningState::FAILURE;
                UpdateBehaviorRunningState(behavior_status_);

                ShowBehaviorPrint(behavior_status_);
            }
            return BehaviorAlgResult(true, BehaviorStatus::Failed);
        }
    }

    // // Handle getting unstuck and slipping
    // PerformUnstuck();
    // if (unstuck_completed_)
    // {
    //     if (unstuck_succeed_)
    //     {
    //         return BehaviorAlgResult(true, BehaviorStatus::Successed);
    //     }
    //     else
    //     {
    //         return BehaviorAlgResult(true, BehaviorStatus::Failed);
    //     }
    // }

    // Perform collision recovery
    PerformCollisionRecovery(fusion_result);
    if (collision_recovery_completed_)
    {
        if (collision_recovery_succeed_)
        {
            {
                behavior_status_ = BehaviorRunningState::SUCCESS;
                UpdateBehaviorRunningState(behavior_status_);

                ShowBehaviorPrint(behavior_status_);
            }
            return BehaviorAlgResult(true, BehaviorStatus::Successed);
        }
        else
        {
            {
                behavior_status_ = BehaviorRunningState::FAILURE;
                UpdateBehaviorRunningState(behavior_status_);

                ShowBehaviorPrint(behavior_status_);
            }
            return BehaviorAlgResult(true, BehaviorStatus::Failed);
        }
    }

    return BehaviorAlgResult(false, BehaviorStatus::InProgress);
}

bool NavigationBehaviorAlg::IsWheelSlipping(const MotorSpeedData &motor_data,
                                            const ImuData &imu_data,
                                            float wheel_radius, float wheel_base)
{
    // Convert motor speed from RPM to rad/s
    float w_left = motor_data.motor_speed_left * 2 * M_PI / 60.0f;
    float w_right = motor_data.motor_speed_right * 2 * M_PI / 60.0f;
    float v_left = w_left * wheel_radius;
    float v_right = w_right * wheel_radius;
    float theoretical_linear = (v_right + v_left) / 2.0f;
    float theoretical_angular = (v_right - v_left) / wheel_base;

    // Integrate IMU acceleration to estimate velocity (simplified form, assuming periodic updates)
    UpdatePreintegration(imu_data);
    float actual_linear = std::hypot(preint_state_.velocity_x, preint_state_.velocity_y);

    // Avoid division by zero and invalid cases
    if ((theoretical_linear < min_valid_linear_) && (theoretical_angular < min_valid_angular_))
    {
        return false;
    }

    float slip_ratio = std::abs(theoretical_linear - actual_linear) / theoretical_linear;
    // float slip_ratio = std::abs(theoretical_angular - actual_angular) / theoretical_angular;
    LOG_DEBUG("[Slip] Theoretical speed: {:.3f}, Actual speed: {:.3f}, Slip ratio: {:.3f}",
              theoretical_linear, actual_linear, slip_ratio);
    return slip_ratio > slip_ratio_threshold_;
}

bool NavigationBehaviorAlg::IsWheelSlipping(const MotorSpeedData &motor_data,
                                            const MotionDetectionResult &motion_detection_result,
                                            float wheel_radius, float wheel_base)
{
    static bool is_slipping = false; // Track whether it is currently slipping
    static int slip_counter = 0;
    const int slip_threshold = 200;

    // Convert motor speed from RPM to rad/s
    float w_left = motor_data.motor_speed_left * 2 * M_PI / 60.0f;
    float w_right = motor_data.motor_speed_right * 2 * M_PI / 60.0f;
    // LOG_ERROR("[MowerAlg] [IsWheelSlipping1] w_left({}), w_right({})", w_left, w_right);

    float v_left = w_left * wheel_radius;
    float v_right = w_right * wheel_radius;

    float act_linear = (v_right + v_left) / 2.0f;
    float act_angular = (v_right - v_left) / wheel_base;

    // Time synchronization check
    // LOG_INFO("[MowerAlg] [IsWheelSlipping1] motor_data.timestamp({}), motion_detection_result.timestamp({})",
    //          motor_data.system_timestamp, motion_detection_result.timestamp);
    // LOG_INFO("[MowerAlg] [IsWheelSlipping1] Timestamp difference ({})",
    //          std::abs(static_cast<int64_t>(motor_data.system_timestamp - motion_detection_result.timestamp)));

    // bool is_data_synced =
    //     std::abs(static_cast<int64_t>(motor_data.system_timestamp - motion_detection_result.timestamp)) < 1000; // ms

    // if (!is_data_synced)
    // {
    //     LOG_ERROR("[MowerAlg] [IsWheelSlipping1] Time synchronization check failed");
    //     return false;
    // }

    bool is_has_speed = (fabs(act_linear) > min_valid_linear_) || (fabs(act_angular) > min_valid_angular_);
    bool potential_slip = is_has_speed && !motion_detection_result.is_motion;

    if (!is_slipping)
    {
        if (potential_slip)
        {
            slip_counter++;
        }
        else
        {
            slip_counter = 0;
        }
        if (slip_counter >= slip_threshold)
        {
            is_slipping = true;
            slip_counter = 0;
            // LOG_INFO("[MowerAlg] [IsWheelSlipping1] Slipping state detected");
        }
    }
    else // is_slipping == true
    {
        if (!potential_slip)
        {
            slip_counter++;
        }
        else
        {
            slip_counter = 0;
        }
        if (slip_counter >= slip_threshold)
        {
            is_slipping = false;
            slip_counter = 0;
            // LOG_INFO("[MowerAlg] [IsWheelSlipping1] Slipping state ended");
        }
    }

    // LOG_INFO("[nav_behavior] [IsWheelSlipping1] act_linear({}), act_angular({}), is_motion({}), is_slipping({}), slip_counter({})",
    //          act_linear, act_angular, motion_detection_result.is_motion, is_slipping, slip_counter);

    return is_slipping;
}
void NavigationBehaviorAlg::UpdatePreintegration(const ImuData &imu_data)
{
    float dt = (imu_data.system_timestamp - preint_state_.last_timestamp) / 1000.0f; // ms to s
    if (dt <= 0 || dt > 0.1f)
        dt = 0.01f; // Limit the time step

    // Simple pre-integration: only update velocity (ignore rotation and bias)
    preint_state_.velocity_x += imu_data.linear_acceleration_x * dt;
    preint_state_.velocity_y += imu_data.linear_acceleration_y * dt;
    preint_state_.last_timestamp = imu_data.system_timestamp;
}

bool NavigationBehaviorAlg::IsMotorStalling(const MotorSpeedData &motor_data)
{
    float left_speed_mps = (motor_data.motor_speed_left / 99.5) * 2 * M_PI / 60.0f * wheel_radius_;
    float right_speed_mps = (motor_data.motor_speed_right / 99.5) * 2 * M_PI / 60.0f * wheel_radius_;
    float min_speed = std::min(std::abs(left_speed_mps), std::abs(right_speed_mps));
    float max_current = std::max(motor_data.current_left, motor_data.current_right); // Current

    bool stalled = (max_current > stall_current_threshold_) &&
                   (min_speed < stall_speed_threshold_);
    LOG_DEBUG("[Stall] Current: {:.2f} A, Speed: {:.3f} m/s, Is stalled: {}",
              max_current, min_speed, stalled);
    return stalled;
}

void NavigationBehaviorAlg::HandleMotorStall()
{
    switch (unstuck_state_)
    {
    case UnstuckState::NORMAL:
    {
        LOG_WARN("[Stall] Detected stall, starting to back up");
        PublishVelocity(-backup_speed_, 0.0, 2000);
        unstuck_attempts_++;
        unstuck_state_ = UnstuckState::BACKING_UP;
        break;
    }

    case UnstuckState::BACKING_UP:
    {
        if (vel_publisher_ && vel_publisher_->IsExecutionCompleted())
        {
            float turn_angle = (unstuck_attempts_ % 2) ? M_PI / 6 : -M_PI / 6; // Alternate 30°
            LOG_INFO("[Stall] Turning {:.1f} degrees", Radians2Degrees(turn_angle));
            PublishVelocity(0.0, turn_angle, 1000);
            unstuck_state_ = UnstuckState::TURNING;
        }
        break;
    }

    case UnstuckState::TURNING:
    {
        if (vel_publisher_ && vel_publisher_->IsExecutionCompleted())
        {
            LOG_INFO("[Stall] Attempting to move forward");
            PublishVelocity(forward_speed_, 0.0, 2000);
            unstuck_state_ = UnstuckState::FORWARDING;
        }
        break;
    }

    case UnstuckState::FORWARDING:
    {
        if (vel_publisher_ && vel_publisher_->IsExecutionCompleted())
        {
            if (unstuck_attempts_ >= max_stall_retries_)
            {
                LOG_ERROR("[Stall] Reached maximum retries, stopping");
                // ProhibitVelPublisher();
                unstuck_state_ = UnstuckState::NORMAL;
                unstuck_attempts_ = 0;
            }
            else
            {
                unstuck_state_ = UnstuckState::NORMAL; // Reset for the next check
            }
        }
        break;
    }

    default:
    {
        break;
    }
    }
}

void NavigationBehaviorAlg::HandleWheelSlip(const PerceptionFusionResult &fusion_result)
{
    switch (wheel_slip_state_)
    {
    case WheelSlipRecoveryState::NORMAL:
    {
        if (is_slipping_detected_.load())
        {
            // Reset
            behavior_status_ = BehaviorRunningState::RUNNING;
            slipping_recovery_completed_ = false;
            slipping_recovery_succeed_ = false;

            LOG_INFO("[HandleWheelSlip1] Slipping detected, starting to back up for recovery");
            wheel_slip_state_ = WheelSlipRecoveryState::BACKING_UP;

            // PublishZeroVelocity();           // Stop immediately
            // PublishVelocity(0.0, 0.0, 100); // Send continuously for 100ms
        }
        break;
    }
    case WheelSlipRecoveryState::BACKING_UP:
    {
        LOG_INFO("[HandleWheelSlip1] Backing up");
        PublishVelocity(-slipping_backup_speed_, 0.0, slipping_backup_duration_);

        LOG_INFO("[HandleWheelSlip1] Starting to turn for recovery");
        wheel_slip_state_ = WheelSlipRecoveryState::TURNING;

        break;
    }
    case WheelSlipRecoveryState::TURNING:
    {
        LOG_INFO("[HandleWheelSlip1] Turning");
        float turn_angle = GetTurnAngle(fusion_result);
        float turn_duration = std::abs(turn_angle) / slipping_turn_speed_ * 1000;
        PublishVelocity(0.0, turn_angle > 0 ? slipping_turn_speed_ : -slipping_turn_speed_, turn_duration);

        LOG_INFO("[HandleWheelSlip1] After turning, starting recovery");
        wheel_slip_state_ = WheelSlipRecoveryState::RECOVERING;

        // PublishVelocity(0.0, 0.0, 100); // Send continuously for 100ms

        break;
    }
    case WheelSlipRecoveryState::RECOVERING:
    {
        LOG_INFO("[HandleWheelSlip1] Recovery task");
        wheel_slip_state_ = WheelSlipRecoveryState::NORMAL; // Restore normal task

        slipping_recovery_completed_ = true;
        slipping_recovery_succeed_ = true;
        break;
    }
    }
}

/**
 * @brief Handle getting unstuck or slipping
 *
 */
void NavigationBehaviorAlg::PerformUnstuck()
{
    switch (unstuck_state_)
    {
    case UnstuckState::NORMAL:
    {
        if (is_stucked_)
        {
            LOG_DEBUG("[Unstuck] Detected stuck, starting to get unstuck");
            unstuck_state_ = UnstuckState::BACKING_UP;
            unstuck_attempts_++;

            // PublishZeroVelocity();           // Stop immediately
            PublishVelocity(0.0, 0.0, 100); // Send continuously for 100ms
        }
        break;
    }
    case UnstuckState::BACKING_UP:
    {
        LOG_DEBUG("[Unstuck] Unstuck stage: Backing up");
        PublishVelocity(-backup_speed_, 0.0, backup_duration_);

        unstuck_state_ = UnstuckState::TURNING;
        break;
    }
    case UnstuckState::TURNING:
    {
        LOG_DEBUG("[Unstuck] Unstuck stage: Rotating");
        // Alternate left and right rotation
        float turn_angle = (unstuck_attempts_ % 2 == 0) ? turn_angle_ : -turn_angle_;
        PublishVelocity(0.0, turn_angle, turn_duration_);
        unstuck_state_ = UnstuckState::FORWARDING;
        break;
    }
    case UnstuckState::FORWARDING:
    {
        LOG_DEBUG("[Unstuck] Unstuck stage: Moving forward");
        PublishVelocity(forward_speed_, 0.0, forward_duration_);
        if (!is_stucked_)
        {
            LOG_DEBUG("[Unstuck] Unstuck successfully, resetting state");
            unstuck_state_ = UnstuckState::NORMAL;
            unstuck_attempts_ = 0;

            unstuck_completed_ = true;
            unstuck_succeed_ = true;
        }
        else if (unstuck_attempts_ >= max_unstuck_attempts_)
        {
            LOG_WARN("[Unstuck] Reached maximum attempts, unstuck failed, stopping attempt");
            unstuck_state_ = UnstuckState::NORMAL;
            unstuck_attempts_ = 0;
            // PublishZeroVelocity();          // Stop moving
            PublishVelocity(0.0, 0.0, 100); // Send continuously for 100ms

            unstuck_completed_ = true;
            unstuck_succeed_ = false; // Failed

            // TODO : Notify the user or switch to other strategies
        }
        else
        {
            LOG_DEBUG("[Unstuck] Not unstuck, entering the next round of attempts");
            unstuck_state_ = UnstuckState::BACKING_UP;
        }
        break;
    }
    }
}

/**
 * @brief Handle lifting situation
 *
 * @param sensor_data
 */
void NavigationBehaviorAlg::HandleLift(/*const SensorData &sensor_data*/)
{
    switch (lift_state_)
    {
    case LiftState::NORMAL:
    {
        if (is_lifted_)
        {
            behavior_status_ = BehaviorRunningState::RUNNING;
            lift_succeed_ = false;
            lift_completed_ = false;

            LOG_INFO("[Behavior] Lifting detected, stopping movement");
            lift_state_ = LiftState::LIFTED;

            // PublishZeroVelocity();           // Stop immediately
            // PublishVelocity(0.0, 0.0, 100); // Send continuously for 100ms
        }
        break;
    }
    case LiftState::LIFTED:
    {
        LOG_INFO("[Behavior] Lifting detected, starting to back up for recovery");
        PublishVelocity(-lift_backup_speed_, 0.0, lift_backup_duration_); // Add backward recovery action

        if (!is_lifted_)
        {
            LOG_INFO("[Behavior] Detected being placed back on the ground, entering recovery phase");
            lift_state_ = LiftState::RECOVERING;
        }
        break;
    }
    case LiftState::RECOVERING:
    {
        LOG_INFO("[Behavior] Recovery task");
        lift_state_ = LiftState::NORMAL;

        lift_succeed_ = true;
        lift_completed_ = true;

        break;
    }
    }
}

/**
 * @brief Handle collision recovery
 *
 * @param fusion_result
 */
void NavigationBehaviorAlg::PerformCollisionRecovery(const PerceptionFusionResult &fusion_result)
{
    switch (collision_state_)
    {
    case CollisionRecoveryState::NORMAL:
    {
        if (is_collision_detected_)
        {
            behavior_status_ = BehaviorRunningState::SUCCESS;
            collision_recovery_completed_ = false;
            collision_recovery_succeed_ = false;

            LOG_INFO("[Collision] Collision detected, starting to back up for recovery");
            collision_state_ = CollisionRecoveryState::BACKING_UP;

            // PublishZeroVelocity();           // Stop immediately
            // PublishVelocity(0.0, 0.0, 100); // Send continuously for 100ms
        }
        break;
    }
    case CollisionRecoveryState::BACKING_UP:
    {
        LOG_INFO("[Collision] Backing up");
        PublishVelocity(-collision_backup_speed_, 0.0, collision_backup_duration_);

        if (is_collision_detected_)
        {
            LOG_INFO("[Collision] Collision detected, starting to turn for recovery");
            collision_state_ = CollisionRecoveryState::TURNING;
        }
        else
        {
            LOG_INFO("[Collision] No collision detected after backing up, starting recovery");
            collision_state_ = CollisionRecoveryState::RECOVERING;
        }

        // if (!is_collision_detected_)
        // {
        //     LOG_INFO("[Collision] No collision detected after backing up, starting recovery");
        //     collision_state_ = CollisionRecoveryState::RECOVERING;
        // }

        break;
    }
    case CollisionRecoveryState::TURNING:
    {
        LOG_INFO("[Collision] Turning");
        float turn_angle = GetTurnAngle(fusion_result);
        float turn_duration = std::abs(turn_angle) / collision_turn_speed_ * 1000;
        PublishVelocity(0.0, turn_angle > 0 ? collision_turn_speed_ : -collision_turn_speed_, turn_duration);

        if (is_collision_detected_)
        {
            LOG_INFO("[Collision] Collision detected, turning recovery failed");
            collision_state_ = CollisionRecoveryState::BACKING_UP;
        }
        else
        {
            LOG_INFO("[Collision] No collision detected after turning, starting recovery");
            collision_state_ = CollisionRecoveryState::RECOVERING;

            // PublishVelocity(0.0, 0.0, 100); // Send continuously for 100ms
        }

        break;
    }
    case CollisionRecoveryState::RECOVERING:
    {
        LOG_INFO("[Collision] Recovery task");
        collision_state_ = CollisionRecoveryState::NORMAL; // Restore normal task

        collision_recovery_completed_ = true;
        collision_recovery_succeed_ = true;
        break;
    }
    }
}

float NavigationBehaviorAlg::GetTurnAngle(const PerceptionFusionResult &fusion_result)
{
    // Prioritize obstacle status information for decision-making
    const auto &obstacle = fusion_result.obstacle_result;

    // Case 1: Clear single-sided obstacle detection
    if (obstacle.left_obstacle_status == ObstacleDetectStatus::HAVE_OBSTACLE)
    {
        LOG_DEBUG("[Collision] Obstacle detected on the left, turning right");
        return -collision_turn_angle_; // Turn right
    }
    if (obstacle.right_obstacle_status == ObstacleDetectStatus::HAVE_OBSTACLE)
    {
        LOG_DEBUG("[Collision] Obstacle detected on the right, turning left");
        return collision_turn_angle_; // Turn left
    }

    // Case 2: Random turning decision (when all sensor information is unclear)
    const bool random_turn = (rand() % 2 == 0); // 50% probability
    LOG_DEBUG("[Collision] No clear obstacle information, random turning direction: %s", random_turn ? "Right" : "Left");
    return random_turn ? -collision_turn_angle_ : collision_turn_angle_;
}

} // namespace fescue_iox
