#ifndef NAVIGATION_BEHAVIOR_NODE_HPP
#define NAVIGATION_BEHAVIOR_NODE_HPP

#include "behavior.hpp"
#include "data_type.hpp"
#include "geometry_msgs/twist__struct.h"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/server.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "mower_msgs/msg/mcu_exception.hpp"
#include "mower_msgs/msg/mcu_imu.hpp"
#include "mower_msgs/msg/mcu_motor_speed.hpp"
#include "mower_msgs/msg/soc_imu.hpp"
#include "ob_mower_msgs/localization_motion_detection_result__struct.h"
#include "ob_mower_msgs/nav_alg_ctrl__struct.h"
#include "ob_mower_msgs/nav_behavior_final_result__struct.h"
#include "ob_mower_msgs/nav_behavior_state__struct.h"
#include "ob_mower_msgs/nav_running_state__struct.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "ob_mower_srvs/nav_behavior_alg_param_service__struct.h"
#include "ob_mower_srvs/node_common_param_service.h"
#include "opencv2/opencv.hpp"
#include "utils/heartbeat_publisher.hpp"
#include "utils/iceoryx_publisher_mower.hpp"
#include "utils/iceoryx_server_mower.hpp"
#include "utils/iceoryx_subscriber_mower.hpp"

#include <memory>
#include <mutex>
#include <thread>

namespace fescue_iox
{

// Filter State
struct FilterState
{
    float accel_x = 0.0f, accel_y = 0.0f, accel_z = 0.0f;
    float gyro_x = 0.0f, gyro_y = 0.0f, gyro_z = 0.0f;
    float motor_speed_left = 0.0f, motor_speed_right = 0.0f;
};

class NavigationBehaviorNode
{
    using iox_behavior_final_result_publisher = iox::popo::Publisher<fescue_msgs__msg__NavBehaviorFinalResult>;
    using iox_nav_alg_ctrl_publisher = iox::popo::Publisher<fescue_msgs__msg__NavigationAlgoCtrlData>;
    using iox_behavior_state_publisher = iox::popo::Publisher<fescue_msgs__msg__BehaviorStateData>;

    using get_node_param_request = ob_mower_srvs::GetNodeParamRequest;
    using get_node_param_response = ob_mower_srvs::GetNodeParamResponse;
    using set_node_param_request = ob_mower_srvs::SetNodeParamRequest;
    using set_node_param_response = ob_mower_srvs::SetNodeParamResponse;

    using get_alg_param_request = fescue_msgs__srv__GetNavigationBehaviorAlgParam_Request;
    using get_alg_param_response = fescue_msgs__srv__GetNavigationBehaviorAlgParam_Response;
    using set_alg_param_request = fescue_msgs__srv__SetNavigationBehaviorAlgParam_Request;
    using set_alg_param_response = fescue_msgs__srv__SetNavigationBehaviorAlgParam_Response;

public:
    NavigationBehaviorNode(const std::string &node_name);
    ~NavigationBehaviorNode();

private:
    void InitWorkingDirectory();
    void InitParam();
    void InitAlgorithmParam();
    void InitAlgorithm();
    void DeinitAlgorithm();
    void InitLogger();
    void InitSubscriber();
    void InitPublisher();
    void InitService();
    void CheckMCUExeceptionTimeout();
    void BehaviorThread();
    void InitHeartbeat();

    void SetMotorSpeedData(const MotorSpeedData &motor_speed_data);
    void SetMotionDetectionResult(const MotionDetectionResult &motion_detection_result);

private:
    void DealMCUException(const mower_msgs::msg::McuException &data);
    void DealPerceptionFusionResult(const fescue_msgs__msg__PerceptionFusionResult &msg);
    void DealNavAlgCtrlResult(const fescue_msgs__msg__NavigationAlgoCtrlData &msg);
    void DealBehaviorState(const fescue_msgs__msg__BehaviorStateData &msg);
    void DealNavRunningState(const fescue_msgs__msg__NavigationRunningStateData &data);
    void DealMotionDetectionResult(const fescue_msgs__msg__LocalizationMotionDetectionResult &data);
    void DealMcuMotorSpeed(const mower_msgs::msg::McuMotorSpeed &data);
    void DealMcuImu(const mower_msgs::msg::McuImu &data);
    void DealSocImu(const mower_msgs::msg::SocImu &data);

    bool GetBehaviorNodeParam(ob_mower_srvs::NodeParamData &data);
    bool SetBehaviorNodeParam(const ob_mower_srvs::NodeParamData &data);
    bool GetBehaviorAlgParam(fescue_msgs__msg__NavigationBehaviorAlgParam &data);
    bool SetBehaviorAlgParam(const fescue_msgs__msg__NavigationBehaviorAlgParam &data);
    void PublishBehaviorFinalResult(const BehaviorAlgResult &result);
    void DealFeatureSelectCallback(const std::vector<FeatureSelectData> &data);
    void DealBehaviorRunningStateCallback(BehaviorRunningState state);
    void SetBehaviorVelPublisherProhibit(bool prohibit)
    {
        if (behavior_alg_)
        {
            behavior_alg_->SetVelPublisherProhibit(prohibit);
        }
    }
    float LowPassFilter(float new_value, float prev_value, float alpha);

private:
    FilterState filter_state_;
    float alpha_imu_ = 0.1f;   // IMU filter coefficient
    float alpha_speed_ = 0.2f; // Motor speed filter coefficient
    bool enable_imu_filter_ = false;
    bool enable_motor_speed_filter_ = false;

private:
    // Subscriber
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>> sub_fusion_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>> sub_nav_alg_ctrl_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__BehaviorStateData>> sub_behavior_state_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationRunningStateData>> sub_nav_running_state_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuException>> sub_mcu_exception_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuImu>> sub_mcu_imu_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::SocImu>> sub_soc_imu_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuMotorSpeed>> sub_mcu_motor_speed_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__LocalizationMotionDetectionResult>> sub_motion_detection_result_{nullptr};

    // Publisher
    std::unique_ptr<iox_nav_alg_ctrl_publisher> pub_nav_alg_ctrl_{nullptr};
    std::unique_ptr<iox_behavior_state_publisher> pub_behavior_state_{nullptr};
    std::unique_ptr<iox_behavior_final_result_publisher> pub_behavior_final_result_{nullptr}; // 发布跨区域最终执行结果

    // service
    std::unique_ptr<IceoryxServerMower<get_node_param_request, get_node_param_response>> service_get_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_node_param_request, set_node_param_response>> service_set_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_alg_param_request, get_alg_param_response>> service_get_alg_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_alg_param_request, set_alg_param_response>> service_set_alg_param_{nullptr};

    std::atomic_bool behavior_enable_{false};

private:
    std::mutex fusion_mutex_;
    PerceptionFusionResult fusion_result_;
    std::mutex behavior_mtx_;
    BehaviorRunningState behavior_state_{BehaviorRunningState::UNDEFINED};

    McuExceptionStatus mcu_exception_status_{McuExceptionStatus::NORMAL};
    std::mutex mcu_exception_mutex_;

    std::mutex imu_mtx_;
    ImuData imu_data_;
    std::mutex motor_speed_mtx_;
    MotorSpeedData motor_speed_data_;

    std::mutex motion_detection_result_mtx_;
    MotionDetectionResult motion_detection_result_;

    std::thread behavior_thread_;
    std::atomic_bool thread_running_{true};

    BehaviorAlgParam behavior_param_;
    std::unique_ptr<NavigationBehaviorAlg> behavior_alg_{nullptr};

    // New add
    std::chrono::steady_clock::time_point last_mcu_exception_time_;

    // Parameters
    std::string node_name_{"navigation_behavior_node"};
    std::string log_dir_{"/userdata/log"};
    std::string console_log_level_{"info"};
    std::string file_log_level_{"warn"};
    std::string behavior_alg_conf_file_{"conf/navigation_behavior_node/behavior.yaml"};
    int run_frequency_{20};
    std::unique_ptr<NodeHeartbeatPublisher> pub_heartbeat_{nullptr};
};

} // namespace fescue_iox

#endif