#ifndef NAVIGATION_RANDOM_MOWER_HPP
#define NAVIGATION_RANDOM_MOWER_HPP

#include "data_type.hpp"
#include "mower_msgs/msg/soc_exception.hpp"
#include "ob_mower_msgs/nav_random_mower_state__struct.h"
#include "obstacle_detector.hpp"
#include "velocity_publisher.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{

struct RandomMowerAlgParam
{
    float dead_zone;
};

struct RandomMowerAlgResult
{
    float linear_vel;
    float angular_vel;
};

class NavigationRandomMowerAlg
{
    using iox_exception_publisher = iox::popo::Publisher<mower_msgs::msg::SocException>;

public:
    NavigationRandomMowerAlg(const RandomMowerAlgParam &param);
    ~NavigationRandomMowerAlg();
    void Run(const PerceptionFusionResult &fusion_result);
    void ResetRandomMowerFlags();
    void ProhibitVelPublisher();
    void SetRandomMowerAlgParam(const RandomMowerAlgParam &param);
    void SetRandomMowerStateCallback(std::function<void(RandomMowerRunningState state)> callback);
    void SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback);
    void SetVelPublisherProhibit(bool prohibit)
    {
        if (vel_publisher_)
        {
            vel_publisher_->SetProhibitFlag(prohibit);
        }
    }
    void SetAlgoRunningState(MowerRunningState state);
    void SetFusionPose(const ob_mower_msgs::NavFusionPose &fusion_pose);
    void SetOccupancyResult(const OccupancyResult &occupancy_result);
    const char *GetVersion();

private:
    void ProcessAvoidObstacleReverse(const double reverse_distance, const double reverse_v, const bool is_surrounded);
    void ProcessAvoidObstacleRotateSingleWheel(const double wheel_v);
    void ProcessAvoidObstacleRotateSingleWheel(const std::vector<std::vector<uint8_t>> grid, int height, int width, int y_slow);
    void ProcessAvoidObstacleRotate(const std::vector<std::vector<uint8_t>> grid, int height, int width, int y_slow);
    void ProcessTrapAvoidObstacleRotate(const std::vector<std::vector<uint8_t>> grid, int height, int width);
    void ProcessAvoidObstacleFlags();
    void ProcessTrapAvoidObstacleFlags();
    void ProcessAvoidObstacleSlowdown();
    void ProcessAvoidObstacleSlowdown(int height, int y_obs, float res);

    void PublishVelocity(float linear, float angular, uint64_t duration_ms = 0);
    void PublishZeroVelocity();
    void PublishRandomMowerState(RandomMowerRunningState state);
    void PauseVelocity();
    void ResumeVelocity();
    void check_trap(ObstacleDetectionResult result);
    void InitPublisher();
    void PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);
    // add edge follow
    // void DealRandomMowerStateSelect(RandomMowerRunningState state);
    void EdgeFollowDisable();
    void EdgeFollowEnable();
    void DealFeatureSelect(ThreadControl control, bool state);
    void AdjustHeading(float turning_angle_offset);

private:
    std::unique_ptr<VelocityPublisher> vel_publisher_{nullptr};
    std::unique_ptr<iox_exception_publisher> pub_exception_;
    std::function<void(RandomMowerRunningState state)> random_mower_state_callback_;
    std::function<void(const std::vector<FeatureSelectData> &)> feature_select_callback_;

    // 运行状态量
    MowerRunningState mower_running_state_{MowerRunningState::STOP}; // 0-未开始，1-运行中，2-暂停
    RandomMowerRunningState random_mower_running_state_{RandomMowerRunningState::UNDEFINED};
    bool have_obs_in_grass_ = false;
    bool is_surrounded_ = false;
    bool reach_edge_ = false;

    bool avoid_obstacle_reverse_complete_ = false;
    float perception_ctrl_reverse_distance_ = 0.2;
    bool avoid_obstacle_rotate_complete_ = false;
    float turning_angle_min_{0.523}; // 2.1
    float turning_angle_max_{2.1};   // 3.14//1.046
    uint64_t avoid_obstacle_rotate_duration_ = 0;
    uint64_t avoid_obstacle_rotate_start_time_ = 0;
    bool avoid_obstacle_ = false;

    bool avoid_obstacle_slowdown_complete_ = false;
    bool avoid_obstacle_slowdown_start_ = false;
    bool avoid_obstacle_rotate_start_ = false;
    float avoid_obstacle_slowdown_vel_linear_ = 0.0;
    float slow_down_time_gap_{0.01}; // 减速过程时间

    // 随机割草
    double random_v_{0.3};
    float start_slow_down_dis_{0.5};
    float stop_slow_down_dis_{0.3};
    double reverse_v_{0.1};
    double reverse_dis_{0.15}; // 0.5
    double wheel_base_{0.4};
    double wheel_v_{0.2};
    float danger_dis_{0.3};
    int find_target_{4};
    double last_v_{0};
    double last_w_{0};
    float rotation_w_{0};
    float spot_w_{0.5};
    float dead_zone_{0};
    int left_line_x_{11};
    int right_line_x_{28};
    std::unique_ptr<ObstacleDetector> obstacle_detector_{nullptr};
    bool is_save_data_{false};
    std::string file_path_{"/userdata/"};
    bool is_trap_{false};
    int is_danger_num_{0};
    int danger_threshold_{2};
    int straight_num_{0};
    int straight_threshold_{1000};
    bool mark_trap_timestamp_{false};
    std::chrono::high_resolution_clock::time_point trap_timestamp_;
    double max_trap_duration_{360.0};
    float trap_spot_w_{0.3};
    bool trap_rotate_start_{false};
    float trap_rotate_angle_{0};
    std::chrono::high_resolution_clock::time_point trap_rotate_timestamp_;
    int trap_forward_num_{0};
    int trap_forward_threshold_{188};
    float trap_v_{0.1};
    float trap_reverse_dis_{0.1};
    float trap_turning_angle_min_{2.6};
    float trap_turning_angle_max_{3.14};
    std::chrono::high_resolution_clock::time_point trap_edge_follow_timestamp_;
    double max_trap_edge_follow_duration_{120.0};
    double wait_for_bias_time_{2.5};
    bool bias_first_stop_complete_{false};
};

} // namespace fescue_iox

#endif
