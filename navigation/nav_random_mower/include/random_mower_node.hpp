#ifndef NAVIGATION_RANDOM_MOWER_NODE_HPP
#define NAVIGATION_RANDOM_MOWER_NODE_HPP

#include "data_type.hpp"
#include "geometry_msgs/twist__struct.h"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/server.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "mower_msgs/srv/camera_bev_params.hpp"
#include "ob_mower_msgs/nav_alg_ctrl__struct.h"
#include "ob_mower_msgs/nav_random_mower_state__struct.h"
#include "ob_mower_msgs/nav_running_state__struct.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "ob_mower_srvs/nav_random_mower_node_param_service__struct.h"
#include "random_mower.hpp"
#include "thirdparty/ob_mower_fusion/include/common_defs.h"
#include "utils/heartbeat_publisher.hpp"
#include "utils/iceoryx_client_mower.hpp"
#include "utils/iceoryx_publisher_mower.hpp"
#include "utils/iceoryx_server_mower.hpp"
#include "utils/iceoryx_subscriber_mower.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{
struct CameraBevParam
{
    float blind_zone_dist; // 盲区距离(单位: m).
    ObPoint lLine_ptStart; // 左侧车道线起点(此处"起点"是指车道线上远离图片底边的一个点)
    ObPoint lLine_ptEnd;   // 左侧车道线终点(此处"终点"是指车道线上靠近图片底边的一个点)
    ObPoint rLine_ptStart; // 右侧车道线起点.
    ObPoint rLine_ptEnd;   // 右侧车道线终点.
};

class NavigationRandomMowerNode
{
    using iox_random_mower_state_publisher = iox::popo::Publisher<fescue_msgs__msg__RandomMowerStateData>;
    using iox_nav_alg_ctrl_publisher = iox::popo::Publisher<fescue_msgs__msg__NavigationAlgoCtrlData>;
    using get_node_param_request = fescue_msgs__srv__GetNavigationRandomMowerNodeParam_Request;
    using get_node_param_response = fescue_msgs__srv__GetNavigationRandomMowerNodeParam_Response;
    using set_node_param_request = fescue_msgs__srv__SetNavigationRandomMowerNodeParam_Request;
    using set_node_param_response = fescue_msgs__srv__SetNavigationRandomMowerNodeParam_Response;

public:
    NavigationRandomMowerNode(const std::string &node_name);
    ~NavigationRandomMowerNode();

private:
    void InitWorkingDirectory();
    void InitParam();
    void InitLogger();
    void InitPublisher();
    void InitSubscriber();
    void InitAlgorithm();
    void InitAlgorithmParam();
    void DeinitAlgorithm();
    void InitService();
    void InitHeartbeat();

private:
    void DealPerceptionFusionResult(const fescue_msgs__msg__PerceptionFusionResult &msg);
    void DealNavAlgCtrlResult(const fescue_msgs__msg__NavigationAlgoCtrlData &msg);
    void DealNavRunningState(const fescue_msgs__msg__NavigationRunningStateData &data);
    void DealNavFusionPose(const ob_mower_msgs::NavFusionPose &data);
    void RandomMowerThread();
    void DealRandomMowerStateCallback(RandomMowerRunningState state);
    void DealFeatureSelectCallback(const std::vector<FeatureSelectData> &data);
    bool GetRandomMowerNodeParam(fescue_msgs__msg__NavigationRandomMowerNodeParam &data);
    bool SetRandomMowerNodeParam(const fescue_msgs__msg__NavigationRandomMowerNodeParam &data);
    float GetBEVBlindZoneDist();
    void SetRandomMowerVelPublisherProhibit(bool prohibit)
    {
        if (random_mower_alg_)
        {
            random_mower_alg_->SetVelPublisherProhibit(prohibit);
        }
    }

private:
    // 订阅
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>> sub_fusion_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>> sub_nav_alg_ctrl_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationRunningStateData>> sub_nav_running_state_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<ob_mower_msgs::NavFusionPose>> sub_nav_fusion_pose_{nullptr};

    // 发布
    std::unique_ptr<iox_random_mower_state_publisher> pub_random_mower_state_{nullptr};
    std::unique_ptr<iox_nav_alg_ctrl_publisher> pub_nav_alg_ctrl_{nullptr};
    // 服务
    std::unique_ptr<IceoryxServerMower<get_node_param_request, get_node_param_response>> service_get_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_node_param_request, set_node_param_response>> service_set_node_param_{nullptr};

    // 数据
    std::mutex fusion_result_mtx_;
    PerceptionFusionResult fusion_result_;

private:
    std::atomic_bool random_mower_enable_{false};

    std::thread random_mower_thread_;
    std::atomic_bool thread_running_{true};

    std::string node_name_{"navigation_random_mower_node"};
    std::string log_dir_{"/userdata/log"};
    std::string console_log_level_{"info"};
    std::string file_log_level_{"warn"};
    std::string random_mower_alg_conf_file_{"conf/navigation_random_mower_node/random_mower.yaml"};

    RandomMowerAlgParam random_mower_alg_param_;
    std::unique_ptr<NavigationRandomMowerAlg> random_mower_alg_{nullptr};
    std::unique_ptr<NodeHeartbeatPublisher> pub_heartbeat_{nullptr};
};

} // namespace fescue_iox

#endif
