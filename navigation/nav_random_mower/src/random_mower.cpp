#include "random_mower.hpp"

#include "mower_sdk_version.h"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "random_mower_config.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

namespace fescue_iox
{

NavigationRandomMowerAlg::NavigationRandomMowerAlg(const RandomMowerAlgParam &param)
    : vel_publisher_(std::make_unique<VelocityPublisher>("RandomMower"))
{
    SetRandomMowerAlgParam(param);
    obstacle_detector_ = std::make_unique<ObstacleDetector>(dead_zone_, danger_dis_, start_slow_down_dis_);
    InitPublisher();
}

NavigationRandomMowerAlg::~NavigationRandomMowerAlg()
{
    PublishZeroVelocity();
    LOG_WARN("NavigationRandomMowerAlg exit!");
}

void NavigationRandomMowerAlg::SetAlgoRunningState(MowerRunningState state)
{
    LOG_INFO("NavigationRandomMowerAlg running state: {}", static_cast<int>(state));
    mower_running_state_ = state;
    if (state == MowerRunningState::RUNNING)
    {
        ResumeVelocity();
    }
    else if (state == MowerRunningState::PAUSE)
    {
        PauseVelocity();
    }
    else
    {
        LOG_ERROR("[NavigationRandomMowerAlg] Unknown state {}!", static_cast<int>(state));
    }
}

const char *NavigationRandomMowerAlg::GetVersion()
{
    return "V1.0.0";
}

#if 0
void NavigationRandomMowerAlg::Run(const PerceptionFusionResult &fusion_result)
{
    if (mower_running_state_ == MowerRunningState::PAUSE)
    {
        LOG_WARN_THROTTLE(3000, "Random mower Run() is PAUSE!");
        return;
    }

    LOG_DEBUG("NavigationRandomMowerAlg Run ................");
    double ctrl_v = random_v_;
    double ctrl_w = 0;

    auto start = std::chrono::high_resolution_clock::now();
    cv::Mat img = fusion_result.boundary_result.inverse_perspect_mask.image;
    int radius = int(start_slow_down_dis_ / 0.0025);
    int danger_radius = int(danger_dis_ / 0.0025);
    cv::Mat mask = cv::Mat::zeros(img.rows, img.cols, CV_8UC1);
    cv::Mat left_mask = cv::Mat::zeros(img.rows, img.cols, CV_8UC1);
    cv::Mat right_mask = cv::Mat::zeros(img.rows, img.cols, CV_8UC1);
    cv::Mat front_mask = cv::Mat::zeros(img.rows, img.cols, CV_8UC1);
    cv::Mat danger_mask = cv::Mat::zeros(img.rows, img.cols, CV_8UC1);
    cv::ellipse(mask, cv::Point(img.cols / 2, img.rows - 1), cv::Size(radius, radius), 180, 0, 180, cv::Scalar(255), -1);
    cv::ellipse(left_mask, cv::Point(img.cols / 2, img.rows - 1), cv::Size(radius, radius), 180, 0, 60, cv::Scalar(255), -1);
    cv::ellipse(front_mask, cv::Point(img.cols / 2, img.rows - 1), cv::Size(radius, radius), 240, 0, 60, cv::Scalar(255), -1);
    cv::ellipse(right_mask, cv::Point(img.cols / 2, img.rows - 1), cv::Size(radius, radius), 300, 0, 60, cv::Scalar(255), -1);
    cv::ellipse(danger_mask, cv::Point(img.cols / 2, img.rows - 1), cv::Size(danger_radius, danger_radius), 180, 0, 180, cv::Scalar(255), -1);
    cv::Mat res;
    cv::Mat danger_res;
    cv::Mat l_res;
    cv::Mat r_res;
    cv::Mat f_res;
    cv::bitwise_and(img, mask, res);
    cv::bitwise_and(img, danger_mask, danger_res);
    cv::bitwise_and(img, left_mask, l_res);
    cv::bitwise_and(img, front_mask, f_res);
    cv::bitwise_and(img, right_mask, r_res);
    /*judge whether reach edge*/
    if (cv::countNonZero(danger_res) > 0)
    {
        reach_edge_ = true;
        is_surrounded_ = true;
        ctrl_w = wheel_v_;
    }
    else
    {
        if (cv::countNonZero(res) > 0)
        {
            if ((cv::countNonZero(l_res) > 0 && cv::countNonZero(f_res) == 0 && cv::countNonZero(r_res) == 0) ||
                (cv::countNonZero(l_res) > 0 && cv::countNonZero(f_res) > 0 && cv::countNonZero(r_res) == 0))
            {
                ctrl_w = -wheel_v_;
            }
            else
            {
                ctrl_w = wheel_v_;
            }
            have_obs_in_grass_ = true;

            if (cv::countNonZero(l_res) > 0 && cv::countNonZero(f_res) > 0 && cv::countNonZero(r_res) > 0)
            {
                is_surrounded_ = true;
            }
        }
        else
        {
            // no obs,go ahead
            ctrl_w = 0;
        }
    }

    if (reach_edge_)
    {
        LOG_DEBUG("reach edge!");
        avoid_obstacle_slowdown_complete_ = true;
        PublishZeroVelocity();
        ProcessAvoidObstacleReverse(reverse_dis_, reverse_v_, is_surrounded_);
        ProcessAvoidObstacleRotateSingleWheel(ctrl_w);
        ProcessAvoidObstacleFlags();
    }
    else
    {
        if (have_obs_in_grass_)
        {
            // avoid_obstacle_ = true;
            LOG_DEBUG("have obs in grass");
            ProcessAvoidObstacleSlowdown();
            ProcessAvoidObstacleReverse(reverse_dis_, reverse_v_, is_surrounded_);
            ProcessAvoidObstacleRotateSingleWheel(ctrl_w);
            ProcessAvoidObstacleFlags();
        }
        else
        {
            PublishVelocity(random_v_, 0);
        }
    }

    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double> duration = end - start;
    LOG_DEBUG("cost time: {} seconds", duration.count());
}
#endif
// make sure the dead zone is correct
#if 1

void NavigationRandomMowerAlg::SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback)
{
    feature_select_callback_ = callback;
}

void NavigationRandomMowerAlg::DealFeatureSelect(ThreadControl control, bool state)
{
    std::vector<FeatureSelectData> feature_data;
    feature_data.clear();
    FeatureSelectData feature{control, static_cast<int>(NavAlgCtrlState::IGNORE)};

    feature.alg_status = state ? static_cast<int>(NavAlgCtrlState::ENABLE) : static_cast<int>(NavAlgCtrlState::DISABLE);

    feature_data.push_back(feature);

    if (feature_select_callback_ && !feature_data.empty())
    {
        feature_select_callback_(feature_data);
    }
}

void NavigationRandomMowerAlg::EdgeFollowDisable()
{
    DealFeatureSelect(ThreadControl::PERCEPTION_EDGE_THREAD, false);
}

void NavigationRandomMowerAlg::EdgeFollowEnable()
{
    DealFeatureSelect(ThreadControl::PERCEPTION_EDGE_THREAD, true);
}

void NavigationRandomMowerAlg::Run(const PerceptionFusionResult &fusion_result)
{
    if (mower_running_state_ == MowerRunningState::PAUSE)
    {
        LOG_WARN_THROTTLE(3000, "Random mower Run() is PAUSE!");
        return;
    }

    LOG_WARN_THROTTLE(3000, "NavigationRandomMowerAlg Run ................");

    if (!obstacle_detector_)
    {
        LOG_ERROR_THROTTLE(1000, "obstacle_detector is nullptr!");
        return;
    }

    auto start = std::chrono::high_resolution_clock::now();
    std::vector<std::vector<uint8_t>> grid = fusion_result.occupancy_grid.grid;
    float resolution = fusion_result.occupancy_grid.resolution;
    int height = fusion_result.occupancy_grid.height;
    int width = fusion_result.occupancy_grid.width;
    auto result = obstacle_detector_->DetectObstacles(grid, height, width, resolution);
    check_trap(result);
    LOG_ERROR_THROTTLE(1000, "-----is trap {}", is_trap_);
    if (!result.result_valid)
    {
        PublishZeroVelocity();
    }
    else
    {
        if (is_trap_)
        {
            if (result.is_danger || result.is_slow)
            {
                if (result.is_danger)
                {
                    is_surrounded_ = trap_forward_num_ > trap_forward_threshold_ ? true : false;
                }
                else
                {
                    is_surrounded_ = false;
                }
                trap_forward_num_ = 0;
                if (!avoid_obstacle_slowdown_complete_)
                {
                    PublishZeroVelocity();
                    avoid_obstacle_slowdown_complete_ = true;
                }
                ProcessAvoidObstacleReverse(trap_reverse_dis_, reverse_v_, is_surrounded_);
                ProcessTrapAvoidObstacleRotate(grid, height, width); // maybe this function will not complete.
                if (avoid_obstacle_rotate_complete_)
                {
                    ProcessTrapAvoidObstacleFlags();
                }
            }
            else
            {
                ProcessTrapAvoidObstacleFlags();
                PublishVelocity(trap_v_, 0);
                trap_forward_num_++;
                LOG_ERROR("Trap! try to  go forward,num:{}", trap_forward_num_);
            }
#if 0
            std::chrono::duration<double> trap_edge_follow_duration = std::chrono::high_resolution_clock::now() - trap_timestamp_;
            if (trap_edge_follow_duration.count() <= wait_for_bias_time_)
            {
                if (!bias_first_stop_complete_)
                {
                    PublishVelocity(0, 0, 500);
                    bias_first_stop_complete_ = true;
                }
                else
                {
                    PublishVelocity(0, 0);
                }

                random_mower_running_state_ = RandomMowerRunningState::TRAP_WAIT_BIAS;
                PublishRandomMowerState(RandomMowerRunningState::TRAP_WAIT_BIAS);
                LOG_ERROR_THROTTLE(1000, "PublishRandomMowerState:TRAP_WAIT_BIAS");
            }
            else if (wait_for_bias_time_ < trap_edge_follow_duration.count() && trap_edge_follow_duration.count() <= max_trap_edge_follow_duration_)
            {
                EdgeFollowEnable();
                random_mower_running_state_ = RandomMowerRunningState::TRAP_EDGE_FOLLOW;
                PublishRandomMowerState(RandomMowerRunningState::TRAP_EDGE_FOLLOW);
                LOG_ERROR_THROTTLE(1000, "PublishRandomMowerState:TRAP_EDGE_FOLLOW");
            }
            else
            {
                random_mower_running_state_ = RandomMowerRunningState::NORMAL;
                PublishRandomMowerState(RandomMowerRunningState::NORMAL);
                LOG_ERROR_THROTTLE(1000, "PublishRandomMowerState:NORMAL");
                trap_forward_num_ = straight_threshold_;
                bias_first_stop_complete_ = false;
            }
#endif
        }
        else
        {
            // random_mower_running_state_ = RandomMowerRunningState::NORMAL;
            // PublishRandomMowerState(RandomMowerRunningState::NORMAL);
            // LOG_ERROR_THROTTLE(1000, "PublishRandomMowerState:NORMAL");
            if (result.is_danger && !reach_edge_)
            {
                reach_edge_ = true;
                is_surrounded_ = true;
            }
            else if (result.is_slow && !have_obs_in_grass_)
            {
                have_obs_in_grass_ = true;
            }
            /*judge whether reach edge*/
            if (reach_edge_)
            {
                LOG_DEBUG_THROTTLE(1000, "reach edge!");
                avoid_obstacle_slowdown_complete_ = true;
                PublishZeroVelocity();
                ProcessAvoidObstacleReverse(reverse_dis_, reverse_v_, is_surrounded_);
                ProcessAvoidObstacleRotate(grid, height, width, result.y_slow);
                ProcessAvoidObstacleFlags();
            }
            else if (have_obs_in_grass_)
            {
                LOG_DEBUG_THROTTLE(1000, "have obs in grass");
                ProcessAvoidObstacleSlowdown(height, result.y_in_forward, resolution);
                ProcessAvoidObstacleReverse(reverse_dis_, reverse_v_, is_surrounded_);
                ProcessAvoidObstacleRotate(grid, height, width, result.y_slow);
                ProcessAvoidObstacleFlags();
            }
            else
            {
                PublishVelocity(random_v_, 0);
            }
        }
    }

    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double> duration = end - start;
    LOG_DEBUG_THROTTLE(10000, "new random mower cost time: {} seconds", duration.count());
}
#endif
void NavigationRandomMowerAlg::ResetRandomMowerFlags()
{
    avoid_obstacle_ = false;
    have_obs_in_grass_ = false;
    reach_edge_ = false;
    is_surrounded_ = false;
    avoid_obstacle_slowdown_start_ = false;
    avoid_obstacle_slowdown_complete_ = false;
    avoid_obstacle_reverse_complete_ = false;
    avoid_obstacle_rotate_complete_ = false;
    is_trap_ = false;
    bias_first_stop_complete_ = false;
    mark_trap_timestamp_ = false;
    is_danger_num_ = 0;
    trap_forward_num_ = 0;
    trap_rotate_start_ = false;
}

void NavigationRandomMowerAlg::SetRandomMowerAlgParam(const RandomMowerAlgParam &param)
{
    dead_zone_ = param.dead_zone;
}

void NavigationRandomMowerAlg::SetRandomMowerStateCallback(std::function<void(RandomMowerRunningState state)> callback)
{
    random_mower_state_callback_ = callback;
}

// void NavigationRandomMowerAlg::DealRandomMowerStateSelect(RandomMowerRunningState state)
// {
//     if (random_mower_state_callback_)
//     {
//         random_mower_state_callback_(state);
//     }
// }

void NavigationRandomMowerAlg::ProcessAvoidObstacleReverse(const double reverse_distance, const double reverse_v, const bool is_surrounded)
{
    // 减速过程已经完成
    if (avoid_obstacle_slowdown_complete_ &&
        !avoid_obstacle_reverse_complete_)
    {
        if (is_surrounded)
        {
            uint64_t duration_ms = CalculateDuration(reverse_distance, reverse_v);
            PublishVelocity(-reverse_v, 0, duration_ms);
        }
        avoid_obstacle_reverse_complete_ = true;
    }
}

void NavigationRandomMowerAlg::ProcessAvoidObstacleRotateSingleWheel(const double wheel_v)
{
    if (avoid_obstacle_slowdown_complete_ &&
        avoid_obstacle_reverse_complete_ &&
        !avoid_obstacle_rotate_complete_)
    {
        float rotate_angle = GenerateRandomNumber<float>(turning_angle_min_, turning_angle_max_);              // 计算转弯随机角度
        avoid_obstacle_rotate_duration_ = static_cast<uint64_t>(rotate_angle * wheel_base_ / wheel_v_ * 1000); // 转弯持续时间 ms
        PublishVelocity(fabs(wheel_v / 2), wheel_v / wheel_base_, avoid_obstacle_rotate_duration_);
        avoid_obstacle_rotate_complete_ = true;
    }
}

void NavigationRandomMowerAlg::ProcessAvoidObstacleRotateSingleWheel(const std::vector<std::vector<uint8_t>> grid, int height, int width, int y_slow)
{
    (void)y_slow;
    if (avoid_obstacle_slowdown_complete_ &&
        avoid_obstacle_reverse_complete_ &&
        !avoid_obstacle_rotate_complete_)
    {
        int obs_num_left = 0;
        int obs_num_right = 0;
        for (int y = height - 1; y >= 0; y--)
        {
            for (int l_x = 0; l_x <= left_line_x_; l_x++)
            {
                if (grid[y][l_x] == 1)
                {
                    obs_num_left++;
                }
            }
            for (int r_x = right_line_x_; r_x < width; r_x++)
            {
                if (grid[y][r_x] == 1)
                {
                    obs_num_right++;
                }
            }
        }
        if (obs_num_left <= obs_num_right)
        {
            rotation_w_ = wheel_v_;
        }
        else
        {
            rotation_w_ = -wheel_v_;
        }
        float rotate_angle = GenerateRandomNumber<float>(turning_angle_min_, turning_angle_max_);              // 计算转弯随机角度
        avoid_obstacle_rotate_duration_ = static_cast<uint64_t>(rotate_angle * wheel_base_ / wheel_v_ * 1000); // 转弯持续时间 ms
        PublishVelocity(fabs(rotation_w_ / 2), rotation_w_ / wheel_base_, avoid_obstacle_rotate_duration_);
        avoid_obstacle_rotate_complete_ = true;
    }
}

void NavigationRandomMowerAlg::ProcessAvoidObstacleRotate(const std::vector<std::vector<uint8_t>> grid, int height, int width, int y_slow)
{
    (void)y_slow;
    if (avoid_obstacle_slowdown_complete_ &&
        avoid_obstacle_reverse_complete_ &&
        !avoid_obstacle_rotate_complete_)
    {
        int obs_num_left = 0;
        int obs_num_right = 0;
        for (int y = height - 1; y >= 0; y--)
        {
            for (int l_x = 0; l_x <= left_line_x_; l_x++)
            {
                if (grid[y][l_x] == 1)
                {
                    obs_num_left++;
                }
            }
            for (int r_x = right_line_x_; r_x < width; r_x++)
            {
                if (grid[y][r_x] == 1)
                {
                    obs_num_right++;
                }
            }
        }
        if (obs_num_left <= obs_num_right)
        {
            rotation_w_ = spot_w_;
        }
        else
        {
            rotation_w_ = -spot_w_;
        }
        float rotate_angle = GenerateRandomNumber<float>(turning_angle_min_, turning_angle_max_); // 计算转弯随机角度
        avoid_obstacle_rotate_duration_ = static_cast<uint64_t>(rotate_angle / spot_w_ * 1000);   // 转弯持续时间 ms
        LOG_DEBUG("rotate_angle:{},rotate_duration:{}", rotate_angle, avoid_obstacle_rotate_duration_);

        // PublishVelocity(0, rotation_w_, avoid_obstacle_rotate_duration_);

        float signed_rotate_angle = abs(rotate_angle);
        if (rotation_w_ < 0)
        {
            signed_rotate_angle = -signed_rotate_angle;
        }
        AdjustHeading(signed_rotate_angle);

        avoid_obstacle_rotate_complete_ = true;
        if (is_save_data_)
        {
            std::ofstream datafile(file_path_ + "rotate_angle.txt", std::ofstream::app);
            if (!datafile.is_open())
            {
                LOG_ERROR_THROTTLE(1000, "can not open file rotate_angle.txt!!!!");
            }
            else
            {
                datafile << std::fixed << rotate_angle << ", " << rotate_angle / M_PI * 180 << std::endl;
            }
            datafile.close();
        }
    }
}

void NavigationRandomMowerAlg::ProcessTrapAvoidObstacleRotate(const std::vector<std::vector<uint8_t>> grid, int height, int width)
{
    if (avoid_obstacle_slowdown_complete_ &&
        avoid_obstacle_reverse_complete_ &&
        !avoid_obstacle_rotate_complete_)
    {
        if (!trap_rotate_start_)
        {
            int obs_num_left = 0;
            int obs_num_right = 0;
            for (int y = height - 1; y >= 0; y--)
            {
                for (int l_x = 0; l_x <= left_line_x_; l_x++)
                {
                    if (grid[y][l_x] == 1)
                    {
                        obs_num_left++;
                    }
                }
                for (int r_x = right_line_x_; r_x < width; r_x++)
                {
                    if (grid[y][r_x] == 1)
                    {
                        obs_num_right++;
                    }
                }
            }
            if (obs_num_left <= obs_num_right)
            {
                rotation_w_ = trap_spot_w_;
            }
            else
            {
                rotation_w_ = -trap_spot_w_;
            }
            trap_rotate_angle_ = GenerateRandomNumber<float>(trap_turning_angle_min_, trap_turning_angle_max_); // 计算转弯随机角度
            trap_rotate_timestamp_ = std::chrono::high_resolution_clock::now();
            trap_rotate_start_ = true;
        }
        std::chrono::duration<double> rotate_duration = std::chrono::high_resolution_clock::now() - trap_rotate_timestamp_;
        float rotate_angle = rotate_duration.count() * trap_spot_w_;
        if (rotate_angle < trap_rotate_angle_)
        {
            PublishVelocity(0, rotation_w_);
            LOG_ERROR_THROTTLE(100, "Trap rotate target:{} , actual:{}", trap_rotate_angle_, rotate_angle);
        }
        else
        {
            PublishVelocity(0, 0);
            avoid_obstacle_rotate_complete_ = true;
        }
    }
}

void NavigationRandomMowerAlg::ProcessTrapAvoidObstacleFlags()
{
    if (avoid_obstacle_slowdown_complete_ &&
        avoid_obstacle_reverse_complete_)
    {
        avoid_obstacle_slowdown_complete_ = false;
        avoid_obstacle_reverse_complete_ = false;
        avoid_obstacle_rotate_complete_ = false;
        trap_rotate_start_ = false;
        is_surrounded_ = false;
    }
}

void NavigationRandomMowerAlg::ProcessAvoidObstacleFlags()
{
    if (avoid_obstacle_slowdown_complete_ &&
        avoid_obstacle_reverse_complete_ &&
        avoid_obstacle_rotate_complete_)
    {
        avoid_obstacle_ = false;
        avoid_obstacle_slowdown_start_ = false;
        avoid_obstacle_slowdown_complete_ = false;
        avoid_obstacle_reverse_complete_ = false;
        avoid_obstacle_rotate_complete_ = false;
        have_obs_in_grass_ = false;
        is_surrounded_ = false;
        reach_edge_ = false;
    }
}

// when obs_dis is less than stop_slow_down_dis whether should add zeros vel publish
void NavigationRandomMowerAlg::ProcessAvoidObstacleSlowdown()
{
    if (!avoid_obstacle_slowdown_complete_)
    {
        if (!avoid_obstacle_slowdown_start_)
        {
            avoid_obstacle_slowdown_vel_linear_ = random_v_;
            avoid_obstacle_slowdown_start_ = true;
        }
        float temp = 0.5 * (random_v_ * random_v_) / (start_slow_down_dis_ - stop_slow_down_dis_) * slow_down_time_gap_;
        float vel_linear = avoid_obstacle_slowdown_vel_linear_ - temp;
        if (vel_linear < 0)
        {
            vel_linear = 0;
        }
        else
        {
            avoid_obstacle_slowdown_vel_linear_ = vel_linear;
        }
        PublishVelocity(vel_linear, 0);
        if (fabs(vel_linear) < 1e-6)
        {
            PublishZeroVelocity();
            avoid_obstacle_slowdown_start_ = false;
            avoid_obstacle_slowdown_complete_ = true;
        }
    }
}

void NavigationRandomMowerAlg::ProcessAvoidObstacleSlowdown(int height, int y_obs, float res)
{
    if (!avoid_obstacle_slowdown_complete_)
    {
        float obs_dis = (height - y_obs) * res + dead_zone_;
        LOG_WARN_THROTTLE(100, "****obs_dis:{},y_obs:{},dead_zone:{}", obs_dis, y_obs, dead_zone_);
        if (obs_dis <= stop_slow_down_dis_ - 0.05)
        {
            PublishZeroVelocity();
            avoid_obstacle_slowdown_complete_ = true;
        }
        else
        {
            if (obs_dis > start_slow_down_dis_)
            {
                PublishZeroVelocity();
                avoid_obstacle_slowdown_complete_ = true;
            }
            else // normally obs_dis should be less than  start_slow_down_dis_
            {
                float vel_linear = random_v_ * (obs_dis - stop_slow_down_dis_) / (start_slow_down_dis_ - stop_slow_down_dis_);
                LOG_WARN_THROTTLE(100, "****vel_linear:{},", vel_linear);
                PublishVelocity(vel_linear, 0);
                if (fabs(vel_linear) < 1e-1)
                {
                    PublishZeroVelocity();
                    avoid_obstacle_slowdown_complete_ = true;
                }
            }
        }
    }
}

void NavigationRandomMowerAlg::ProhibitVelPublisher()
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(0, 0);
        vel_publisher_->SetProhibitFlag(true);
    }
}

void NavigationRandomMowerAlg::PublishVelocity(float linear, float angular, uint64_t duration_ms)
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(linear, angular, duration_ms);
        if (duration_ms > 0)
        {
            while (!vel_publisher_->IsExecutionCompleted())
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
    }
}

void NavigationRandomMowerAlg::AdjustHeading(float turning_angle_offset)
{
    LOG_INFO("AdjustHeading:{}", turning_angle_offset);
    if (vel_publisher_)
    {
        vel_publisher_->AdjustHeading(turning_angle_offset);
        while (!vel_publisher_->IsExecutionCompleted())
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }
}

void NavigationRandomMowerAlg::PublishZeroVelocity()
{
    PublishVelocity(0, 0);
}

void NavigationRandomMowerAlg::PublishRandomMowerState(RandomMowerRunningState state)
{
    if (random_mower_state_callback_)
    {
        random_mower_state_callback_(state);
    }
}

void NavigationRandomMowerAlg::PauseVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->PauseVelocity();
    }
}

void NavigationRandomMowerAlg::ResumeVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->ResumeVelocity();
    }
}

void NavigationRandomMowerAlg::PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.node_name = "navigation_random_mowing_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception)
            .or_else([](auto &error) {
                LOG_ERROR("Navigation random_mowing publish soc exception Unable to publishCopyOf, error: {}", error);
            });
    }
}

void NavigationRandomMowerAlg::SetFusionPose(const ob_mower_msgs::NavFusionPose &fusion_pose)
{
    if (vel_publisher_)
    {
        vel_publisher_->SetFusionPose(fusion_pose);
    }
}

void NavigationRandomMowerAlg::SetOccupancyResult(const OccupancyResult &occupancy_result)
{
    if (vel_publisher_)
    {
        vel_publisher_->SetOccupancyResult(occupancy_result);
    }
}

void NavigationRandomMowerAlg::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_exception_ = std::make_unique<iox_exception_publisher>(
        iox::capro::ServiceDescription{kSocExceptionIox[0],
                                       kSocExceptionIox[1],
                                       kSocExceptionIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
}

void NavigationRandomMowerAlg::check_trap(ObstacleDetectionResult result)
{
    if (is_danger_num_ >= danger_threshold_ && !is_trap_)
    {
        is_trap_ = true;
        trap_forward_num_ = 0;
    }
    if (!is_trap_)
    {
        if (result.is_danger)
        {
            is_danger_num_ += 1;
        }
        else
        {
            is_danger_num_ -= 1;
            if (is_danger_num_ < 0)
            {
                is_danger_num_ = 0;
            }
        }
    }
    else // trap
    {
        if (!mark_trap_timestamp_)
        {
            trap_timestamp_ = std::chrono::high_resolution_clock::now();
            mark_trap_timestamp_ = true;
        }

        std::chrono::duration<double> trap_duration = std::chrono::high_resolution_clock::now() - trap_timestamp_;
        if (trap_duration.count() >= max_trap_duration_)
        {
            PublishException(mower_msgs::msg::SocExceptionLevel::ERROR, mower_msgs::msg::SocExceptionValue::ALG_PNC_RANDOM_MOWING_TRAP_EXCEPTION);
            LOG_ERROR_THROTTLE(1000, "random mowing trap!!");
        }

        if (trap_forward_num_ >= straight_threshold_)
        {
            is_trap_ = false;
            mark_trap_timestamp_ = false;
            is_danger_num_ = 0;
        }

        // if (!result.is_danger && !result.is_slow)
        // {
        //     straight_num_ += 1;
        //     LOG_ERROR("straight_num_ : {}---------", straight_num_);
        // }
        // else
        // {
        //     straight_num_ = 0;
        // }
    }
}

} // namespace fescue_iox
