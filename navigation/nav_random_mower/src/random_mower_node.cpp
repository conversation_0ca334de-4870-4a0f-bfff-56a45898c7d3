#include "random_mower_node.hpp"

#include "mower_sdk_version.h"
#include "process_fusion.hpp"
#include "random_mower_node_config.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

namespace fescue_iox
{

NavigationRandomMowerNode::NavigationRandomMowerNode(const std::string &node_name)
    : node_name_(node_name)
{
    InitWorkingDirectory();
    InitParam();
    InitLogger();
    InitSubscriber();
    InitPublisher();
    InitService();
    InitAlgorithm();
    InitHeartbeat();
}

NavigationRandomMowerNode::~NavigationRandomMowerNode()
{
    DeinitAlgorithm();
    LOG_WARN("NavigationRandomMowerNode exit!");
}

void NavigationRandomMowerNode::InitParam()
{
    const std::string conf_file{"conf/navigation_random_mower_node/navigation_random_mower_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("NavigationRandomMowerNode create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("NavigationRandomMowerNode create config path failed!!!");
        }
    }
    if (!Config<NavigationRandomMowerNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init NavigationRandomMowerNode config parameters failed!");
    }
    NavigationRandomMowerNodeConfig config = Config<NavigationRandomMowerNodeConfig>::GetConfig();
    LOG_INFO("[navigation_random_mower_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[navigation_random_mower_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[navigation_random_mower_node] compile time: {}", _COMPILE_TIME_);
    LOG_INFO("{}", config.toString().c_str());

    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    random_mower_alg_conf_file_ = config.random_mower_alg_conf_file;

    if (!Config<NavigationRandomMowerNodeConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set NavigationRandomMowerNodeConfig config parameters failed!");
    }

    CreateDirectories(log_dir_);
}

void NavigationRandomMowerNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void NavigationRandomMowerNode::InitLogger()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void NavigationRandomMowerNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

void NavigationRandomMowerNode::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_random_mower_state_ = std::make_unique<iox_random_mower_state_publisher>(
        iox::capro::ServiceDescription{kNavigationRandomMowerStateIox[0],
                                       kNavigationRandomMowerStateIox[1],
                                       kNavigationRandomMowerStateIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
    pub_nav_alg_ctrl_ = std::make_unique<iox_nav_alg_ctrl_publisher>(
        iox::capro::ServiceDescription{kNavigationNavAlgCtrlIox[0],
                                       kNavigationNavAlgCtrlIox[1],
                                       kNavigationNavAlgCtrlIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
}

void NavigationRandomMowerNode::InitSubscriber()
{
    sub_fusion_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>>(
        "fusion_result", 1, [this](const fescue_msgs__msg__PerceptionFusionResult &data, const std::string &event) {
            (void)event;
            DealPerceptionFusionResult(data);
        });
    sub_nav_alg_ctrl_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>>(
        "navigation_nav_alg_ctrl", 10, [this](const fescue_msgs__msg__NavigationAlgoCtrlData &data, const std::string &event) {
            (void)event;
            DealNavAlgCtrlResult(data);
        });
    sub_nav_running_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavigationRunningStateData>>(
        "navigation_running_state", 10, [this](const fescue_msgs__msg__NavigationRunningStateData &data, const std::string &event) {
            (void)event;
            DealNavRunningState(data);
        });
    sub_nav_fusion_pose_ = std::make_unique<IceoryxSubscriberMower<ob_mower_msgs::NavFusionPose>>(
        "navigation_fusion_pose", 1, [this](const ob_mower_msgs::NavFusionPose &data, const std::string &event) {
            (void)event;
            DealNavFusionPose(data);
        });
}

void NavigationRandomMowerNode::InitAlgorithmParam()
{
}

void NavigationRandomMowerNode::DealFeatureSelectCallback(const std::vector<FeatureSelectData> &data)
{
    if (!data.empty() && pub_nav_alg_ctrl_)
    {
        size_t data_size = data.size() > MAX_NAVIGATION_ALGO_NUM ? MAX_NAVIGATION_ALGO_NUM : data.size();
        fescue_msgs__msg__NavigationAlgoCtrlData nav_alg_ctrl_data;
        nav_alg_ctrl_data.sender.unsafe_assign("NavigationRandomMowerNode");
        for (size_t i = 0; i < data_size; i++)
        {
            fescue_msgs__msg__NavigationAlgoCtrlInfo info;
            info.type = static_cast<fescue_msgs__enum__NavigationAlgoType>(data.at(i).alg_id);
            info.state = static_cast<fescue_msgs__enum__NavigationAlgoState>(data.at(i).alg_status);
            nav_alg_ctrl_data.data.push_back(info);
        }
        pub_nav_alg_ctrl_->publishCopyOf(nav_alg_ctrl_data)
            .or_else([](auto &error) { std::cerr << "NavigationRandomMowerNode nav_alg_ctrl_data Unable to publishCopyOf, error: " << error << std::endl; });
    }
}
void NavigationRandomMowerNode::InitAlgorithm()
{
    random_mower_alg_param_.dead_zone = GetBEVBlindZoneDist();
    random_mower_alg_ = std::make_unique<NavigationRandomMowerAlg>(random_mower_alg_param_);
    random_mower_alg_->SetFeatureSelectCallback([this](const std::vector<FeatureSelectData> &data) {
        this->DealFeatureSelectCallback(data);
    });
    random_mower_alg_->SetRandomMowerStateCallback([this](RandomMowerRunningState state) -> void {
        this->DealRandomMowerStateCallback(state);
    });
    thread_running_.store(true);
    random_mower_thread_ = std::thread(&NavigationRandomMowerNode::RandomMowerThread, this);
}

void NavigationRandomMowerNode::DeinitAlgorithm()
{
    if (random_mower_alg_)
    {
        random_mower_alg_->ProhibitVelPublisher();
    }

    thread_running_.store(false);
    if (random_mower_thread_.joinable())
    {
        random_mower_thread_.join();
    }
}

void NavigationRandomMowerNode::DealNavRunningState(const fescue_msgs__msg__NavigationRunningStateData &data)
{
    if (random_mower_alg_)
    {
        random_mower_alg_->SetAlgoRunningState(static_cast<MowerRunningState>(data.state));
    }
}

void NavigationRandomMowerNode::DealPerceptionFusionResult(const fescue_msgs__msg__PerceptionFusionResult &msg)
{
    LOG_DEBUG("NavigationRandomMowerNode fusion boundary_status: {} inverse_perspect_mask w {} h {} pixels_to_meters {}",
              msg.boundary_state, msg.inverse_perspect_mask.width, msg.inverse_perspect_mask.height,
              msg.pixels_to_meters);

    std::lock_guard<std::mutex> lock(fusion_result_mtx_);
    GetFusionGrassDetectStatus(msg, fusion_result_.grass_detecte_status);
    GetFusionObstacleResult(msg, fusion_result_.obstacle_result);
    GetFusionBoundaryResult(msg, fusion_result_.boundary_result);
    GetOptFusionOccupancyResult(msg, fusion_result_.occupancy_grid);
    // GetFusionOccupancyResult(msg, fusion_result_.occupancy_grid);
    fusion_result_.mower_point.x = msg.mower_point.x;
    fusion_result_.mower_point.y = msg.mower_point.y;
    fusion_result_.min_dist_point.x = msg.min_dist_point.x;
    fusion_result_.min_dist_point.y = msg.min_dist_point.y;
    fusion_result_.input_timestamp = msg.timestamp;
    fusion_result_.output_timestamp = msg.output_timestamp;

    if (random_mower_alg_)
    {
        random_mower_alg_->SetOccupancyResult(fusion_result_.occupancy_grid);
    }
}

void NavigationRandomMowerNode::DealNavAlgCtrlResult(const fescue_msgs__msg__NavigationAlgoCtrlData &msg)
{
    for (size_t i = 0; i < msg.data.size(); i++)
    {
        if (msg.data[i].type == FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_RANDOM_MOWER)
        {
            switch (msg.data[i].state)
            {
            case FESCUE_MSGS_ENUM_NAV_ALGO_STATE_DISABLE:
                random_mower_enable_.store(false);
                SetRandomMowerVelPublisherProhibit(true);
                break;
            case FESCUE_MSGS_ENUM_NAV_ALGO_STATE_ENABLE:
                random_mower_enable_.store(true);
                SetRandomMowerVelPublisherProhibit(false);
                break;
            default:
                break;
            }
            break;
        }
    }
}

void NavigationRandomMowerNode::DealNavFusionPose(const ob_mower_msgs::NavFusionPose &data)
{
    if (random_mower_alg_ == nullptr)
    {
        return;
    }
    random_mower_alg_->SetFusionPose(data);
}

void NavigationRandomMowerNode::RandomMowerThread()
{
    PerceptionFusionResult fusion_result;

    while (thread_running_.load())
    {
        if (random_mower_enable_.load())
        {
            {
                std::lock_guard<std::mutex> lock(fusion_result_mtx_);
                fusion_result = fusion_result_;
            }
            if (random_mower_alg_)
            {
                random_mower_alg_->Run(fusion_result);
            }
        }
        else
        {
            LOG_DEBUG_THROTTLE(2000, "NavigationRandomMowerAlg is disable!");
            if (random_mower_alg_)
            {
                random_mower_alg_->ResetRandomMowerFlags();
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
    }
}

void NavigationRandomMowerNode::DealRandomMowerStateCallback(RandomMowerRunningState state)
{
    fescue_msgs__msg__RandomMowerStateData data;
    data.state = static_cast<fescue_msgs_enum__RandomMowerState>(state);
    pub_random_mower_state_->publishCopyOf(data).or_else(
        [](auto &error) { std::cerr << "pub_random_mower_state_ Unable to publishCopyOf, error: "
                                    << error << std::endl; });
}

void NavigationRandomMowerNode::InitService()
{
    service_get_node_param_ = std::make_unique<IceoryxServerMower<get_node_param_request, get_node_param_response>>(
        "get_navigation_random_mower_node_param_request", 10U,
        [this](const get_node_param_request &request, get_node_param_response &response) {
            (void)request;
            response.success = GetRandomMowerNodeParam(response.data);
            LOG_INFO("Get navigation random mower node param execute {}", response.success);
        });

    service_set_node_param_ = std::make_unique<IceoryxServerMower<set_node_param_request, set_node_param_response>>(
        "set_navigation_random_mower_node_param_request", 10U,
        [this](const set_node_param_request &request, set_node_param_response &response) {
            response.success = SetRandomMowerNodeParam(request.data);
            LOG_INFO("Set navigation random mower node param execute {}", response.success);
        });
}

bool NavigationRandomMowerNode::GetRandomMowerNodeParam(fescue_msgs__msg__NavigationRandomMowerNodeParam &data)
{
    data.console_log_level.unsafe_assign(console_log_level_.c_str());
    data.file_log_level.unsafe_assign(file_log_level_.c_str());
    return true;
}

bool NavigationRandomMowerNode::SetRandomMowerNodeParam(const fescue_msgs__msg__NavigationRandomMowerNodeParam &data)
{
    console_log_level_ = std::string(data.console_log_level.c_str());
    file_log_level_ = std::string(data.file_log_level.c_str());
    InitLogger();
    NavigationRandomMowerNodeConfig config = Config<NavigationRandomMowerNodeConfig>::GetConfig();
    config.common_conf.console_log_level = console_log_level_;
    config.common_conf.file_log_level = file_log_level_;
    if (!Config<NavigationRandomMowerNodeConfig>::SetConfig(config))
    {
        LOG_WARN("Set NavigationRandomMowerNodeConfig config parameters failed!");
        return false;
    }
    LOG_INFO("New NavigationRandomMowerNodeConfig params: {}", config.toString().c_str());
    return true;
}

float NavigationRandomMowerNode::GetBEVBlindZoneDist()
{
    constexpr float DEFAULT_BEV_ZONE_DIST = 0.15;
    float bev_zone_dist = DEFAULT_BEV_ZONE_DIST;
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::CameraBevParamsRequest,
                                                      mower_msgs::srv::CameraBevParamsResponse>>("get_union_rgb_camera_bev_params");

    auto response_handler = [](const mower_msgs::srv::CameraBevParamsResponse &response_receive,
                               mower_msgs::srv::CameraBevParamsResponse &response_output) -> bool {
        response_output.bev_params.scotoma_distance_ = response_receive.bev_params.scotoma_distance_;
        response_output.success = response_receive.success;
        return response_output.success;
    };

    mower_msgs::srv::CameraBevParamsRequest request_input;
    mower_msgs::srv::CameraBevParamsResponse response_output;
    if (client->SendRequest(request_input, response_output, nullptr, response_handler))
    {
        bev_zone_dist = response_output.bev_params.scotoma_distance_;
        LOG_INFO("Random mower alg get bev zone dist success, use {:.2f}!", bev_zone_dist);
    }
    else
    {
        LOG_WARN("Random mower alg get bev zone dist fail!, use default {:.2f}", bev_zone_dist);
    }
    return bev_zone_dist;
}

} // namespace fescue_iox
