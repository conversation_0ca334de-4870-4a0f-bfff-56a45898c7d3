#include "edge_follow.hpp"

#include "edge_follow_config.hpp"
#include "mower_sdk_version.h"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>
using namespace mower_msgs::msg;
namespace fescue_iox
{

NavigationEdgeFollowAlg::NavigationEdgeFollowAlg(const EdgeFollowAlgParam &param)
    : vel_publisher_(std::make_unique<VelocityPublisher>("EdgeFollow"))
{
    SetAlgParam(param);
    InitAlgorithmParam();
    InitAlgorithm();
    InitPublisher();
}

NavigationEdgeFollowAlg::~NavigationEdgeFollowAlg()
{
    LOG_WARN("NavigationEdgeFollowAlg exit!");
    PublishVelocity(0, 0);
}

void NavigationEdgeFollowAlg::SetAlgoRunningState(MowerRunningState state)
{
    LOG_INFO("NavigationEdgeFollowAlg running state: {}", static_cast<int>(state));
    mower_running_state_ = state;
    if (state == MowerRunningState::PAUSE)
    {
        PauseVelocity();
    }
    else if (state == MowerRunningState::RUNNING)
    {
        ResumeVelocity();
    }
    else
    {
        LOG_ERROR("[NavigationEdgeFollowAlg] Unknown state {}!", static_cast<int>(state));
    }
}

const char *NavigationEdgeFollowAlg::GetVersion()
{
    return "V1.0.0";
}

void NavigationEdgeFollowAlg::PublishVelocity(float linear_vel, float angular_vel)
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(linear_vel, angular_vel);
    }
}

void NavigationEdgeFollowAlg::PauseVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->PauseVelocity();
    }
}

void NavigationEdgeFollowAlg::ResumeVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->ResumeVelocity();
    }
}

void NavigationEdgeFollowAlg::DoEdgeFollow(const PerceptionFusionResult &fusion_result,
                                           const OdomResult &odom_result, const ob_mower_msgs::NavEscapeResult &escape_result)
{
    if (mower_running_state_ == MowerRunningState::PAUSE)
    {
        LOG_WARN_THROTTLE(3000, "DoEdgeFollow() is PAUSE!");
        return;
    }

    EdgeFollowAlgResult result;

    LOG_DEBUG_THROTTLE(10000, "edge_follow_alg_type_ {}", edge_follow_alg_type_.c_str());

    if (edge_follow_alg_type_ == "predict_trajectory")
    {
        result = DealEdgeFollowWithPredictTrajectory(fusion_result, odom_result);
    }
    else if (edge_follow_alg_type_ == "path_follow")
    {
        result = DealEdgeFollowWithPathTrack(fusion_result, odom_result);
    }
    else if (edge_follow_alg_type_ == "occupancy_grid")
    {
        result = DealEdgeFollowWithBEV(fusion_result, odom_result, escape_result);
    }

    // LOG_DEBUG("turn_right_times_ {}", turn_right_times_);

    // 如果右转次数超过了最大允许的右转次数
    // if (turn_right_times_ > max_turn_right_times_)
    // {
    //     LOG_DEBUG("turn_right_times_ {}, reset flags!", turn_right_times_);
    //     is_turn_right_times_greater_threshold_ = true; // 设置右转次数超过阈值的标志位为真
    //     SetEdgeFollowFlags(false);
    // }

    // 发布最终控制量
    if (!is_start_wait_)
    {
        is_start_wait_ = true;
        start_wait_t_ = std::chrono::high_resolution_clock::now();
    }
    if (!wait_for_bias_)
    {
        std::chrono::duration<double> duration = std::chrono::high_resolution_clock::now() - start_wait_t_;
        if (duration.count() >= wait_for_bias_time_)
        {
            wait_for_bias_ = true;
        }
        PublishVelocity(0, 0);
    }
    else
    {
        PublishVelocity(result.linear, result.angular);
    }
}

void NavigationEdgeFollowAlg::ResetEdgeFollowFlags()
{
    is_turn_right_times_greater_threshold_ = false; // 重置右转次数超过阈值的标志位
    SetEdgeFollowFlags(false);                      // 停止沿边行走
}

bool NavigationEdgeFollowAlg::SetAlgParam(const EdgeFollowAlgParam &param)
{
    edge_follow_alg_type_ = param.edge_follow_alg_type;
    flAction_type_ = param.flAction_type;
    vel_smooth_enable_ = param.vel_smooth_enable;

    edge_follow_angular_ = param.edge_follow_angular;
    edge_follow_linear_ = param.edge_follow_linear;

    dividing_line_left_ = param.dividing_line_left;
    dividing_line_right_ = param.dividing_line_right;
    max_obstacle_avoid_duration_times_ = param.max_obstacle_avoid_duration_times; // 避障走直线持续发送速度次数
    max_turn_right_times_ = param.max_turn_right_times;                           // 右转次数最大次数
    min_vel_angular_ = param.min_vel_angular;                                     // 最小有效角速度
    max_zero_vel_times_ = param.max_zero_vel_times;

    // 定义感兴趣区域(ROI)，以矩形的左上角点（x, y）和宽度、高度
    x_ = param.x;
    y_ = param.y;
    width_ = param.width;
    height_ = param.height;
    dead_zone_ = param.dead_zone;
    // 依赖算法配置文件路径
    predict_trajectory_conf_ = param.predict_trajectory_conf;
    velocity_smooth_conf_ = param.velocity_smooth_conf;
    path_track_conf_ = param.path_track_conf;
    // occupancy grid parameters
    acc_ = param.acc;
    slow_acc_ = param.slow_acc;
    kp_r_ = param.kp_r;
    kp_l_ = param.kp_l;
    wheel_base_ = param.wheel_base;
    left_line_x_ = param.left_line_x;
    right_line_x_ = param.right_line_x;
    resolution_ = param.resolution;
    look_ahead_dis_ = param.look_ahead_dis;
    danger_dis_ = param.danger_dis;
    grid_width_ = param.grid_width;
    grid_height_ = param.grid_height;
    v_max_ = param.v_max;
    max_fl_time_ = param.max_fl_time;
    fl_back_right_target_dis_ = param.fl_back_right_target_dis;
    fl_turn_right_target_ = param.fl_turn_right_target;

    fl_forward_target_dis_ = param.fl_forward_target_dis;
    fl_forward_r_target_dis_ = param.fl_forward_r_target_dis;
    fl_go_straight_linear_ = param.fl_go_straight_linear;
    fl_go_straight_angluar_ = param.fl_go_straight_angluar;
    fl_turn_r_linear_ = param.fl_turn_r_linear;
    fl_turn_r_angular_ = param.fl_turn_r_angular;

    fl_spot_turn_r_angular_ = param.fl_spot_turn_r_angular;
    fl_turn_right_target_ = param.fl_turn_right_target;
    fl_turn_right_target_new_ = param.fl_turn_right_target_new;
    fl_turn_r_angular_new_ = param.fl_turn_r_angular_new;
    fl_tuning_linear_ = param.fl_tuning_linear;
    fl_tuning_angular_ = param.fl_tuning_angular;

    max_ao_turn_l_spot_num_ = param.max_ao_turn_l_spot_num;
    max_ao_turn_l_num_ = param.max_ao_turn_l_num;
    ao_turn_l_spot_angular_ = param.ao_turn_l_spot_angular;
    ao_turn_l_wheel_r_ = param.ao_turn_l_wheel_r;
    ao_turn_l_wheel_l_ = param.ao_turn_l_wheel_l;
    max_ao_time_ = param.max_ao_time;

    return true;
}

bool NavigationEdgeFollowAlg::GetAlgParam(EdgeFollowAlgParam &param) const
{
    param.edge_follow_alg_type = edge_follow_alg_type_;
    param.flAction_type = flAction_type_;
    param.vel_smooth_enable = vel_smooth_enable_;

    param.edge_follow_angular = edge_follow_angular_;
    param.edge_follow_linear = edge_follow_linear_;

    param.dividing_line_left = dividing_line_left_;
    param.dividing_line_right = dividing_line_right_;
    param.max_obstacle_avoid_duration_times = max_obstacle_avoid_duration_times_; // 避障走直线持续发送速度次数
    param.max_turn_right_times = max_turn_right_times_;                           // 右转次数最大次数
    param.min_vel_angular = min_vel_angular_;                                     // 最小有效角速度
    param.max_zero_vel_times = max_zero_vel_times_;

    // 定义感兴趣区域(ROI)，以矩形的左上角点（x, y）和宽度、高度
    param.x = x_;
    param.y = y_;
    param.width = width_;
    param.height = height_;
    param.dead_zone = dead_zone_;
    // 依赖算法配置文件路径
    param.predict_trajectory_conf = predict_trajectory_conf_;
    param.velocity_smooth_conf = velocity_smooth_conf_;
    param.path_track_conf = path_track_conf_;
    param.acc = acc_;
    param.slow_acc = slow_acc_;
    param.kp_r = kp_r_;
    param.kp_l = kp_l_;
    param.wheel_base = wheel_base_;
    param.left_line_x = left_line_x_;
    param.right_line_x = right_line_x_;
    param.resolution = resolution_;
    param.look_ahead_dis = look_ahead_dis_;
    param.danger_dis = danger_dis_;
    param.grid_width = grid_width_;
    param.grid_height = grid_height_;
    param.v_max = v_max_;
    param.max_fl_time = max_fl_time_;
    param.fl_back_right_target_dis = fl_back_right_target_dis_;
    param.fl_turn_right_target = fl_turn_right_target_;

    param.fl_forward_target_dis = fl_forward_target_dis_;
    param.fl_forward_r_target_dis = fl_forward_r_target_dis_;
    param.fl_go_straight_linear = fl_go_straight_linear_;
    param.fl_go_straight_angluar = fl_go_straight_angluar_;
    param.fl_turn_r_linear = fl_turn_r_linear_;
    param.fl_turn_r_angular = fl_turn_r_angular_;

    param.fl_spot_turn_r_angular = fl_spot_turn_r_angular_;
    param.fl_turn_right_target = fl_turn_right_target_;
    param.fl_turn_r_angular_new = fl_turn_r_angular_new_;
    param.fl_tuning_linear = fl_tuning_linear_;
    param.fl_tuning_angular = fl_tuning_angular_;

    param.max_ao_turn_l_spot_num = max_ao_turn_l_spot_num_;
    param.max_ao_turn_l_num = max_ao_turn_l_num_;
    param.ao_turn_l_spot_angular = ao_turn_l_spot_angular_;
    param.ao_turn_l_wheel_r = ao_turn_l_wheel_r_;
    param.ao_turn_l_wheel_l = ao_turn_l_wheel_l_;
    param.max_ao_time = max_ao_time_;

    return true;
}

void NavigationEdgeFollowAlg::InitAlgorithmParam()
{
    InitPredictTrajectoryAlgParam();
    InitVelocitySmoothAlgParam();
}

void NavigationEdgeFollowAlg::InitPredictTrajectoryAlgParam()
{
    std::string conf_path = GetDirectoryPath(predict_trajectory_conf_);
    if (!conf_path.empty())
    {
        LOG_INFO("Navigation edge follow predict trajectory algo create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("Navigation edge follow predict trajectory algo create config path failed!!!");
        }
    }
    if (!Config<NavigationPredictTrajectoryAlgConfig>::Init(predict_trajectory_conf_))
    {
        LOG_WARN("Init Navigation edge follow predict trajectory config parameters failed!");
    }
    NavigationPredictTrajectoryAlgConfig config = Config<NavigationPredictTrajectoryAlgConfig>::GetConfig();
    LOG_INFO("{}", config.toString().c_str());
    PredictTrajectoryConfigParamToPredictTrajectoryAlgParam(config, predict_trajectory_alg_param_);
    if (!Config<NavigationPredictTrajectoryAlgConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set Navigation edge follow predict trajectory algo config parameters failed!");
    }
}

void NavigationEdgeFollowAlg::InitVelocitySmoothAlgParam()
{
    // std::string conf_path = GetDirectoryPath(velocity_smooth_conf_);
    // if (!conf_path.empty())
    // {
    //     LOG_INFO("Navigation edge follow velocity smooth algo create config path: {}", conf_path.c_str());
    //     if (!CreateDirectories(conf_path))
    //     {
    //         LOG_ERROR("Navigation edge follow velocity smooth algo create config path failed!!!");
    //     }
    // }
    // if (!Config<NavigationPredictTrajectoryAlgConfig>::Init(predict_trajectory_conf_))
    // {
    //     LOG_WARN("Init Navigation edge follow velocity smooth config parameters failed!");
    // }
    // NavigationPredictTrajectoryAlgConfig config = Config<NavigationPredictTrajectoryAlgConfig>::GetConfig();
    // LOG_INFO("{}", config.toString().c_str());
    // PredictTrajectoryConfigParamToPredictTrajectoryAlgParam(config, predict_trajectory_alg_param_);
    // if (!Config<NavigationPredictTrajectoryAlgConfig>::SetConfig(config, true))
    // {
    //     LOG_WARN("Set Navigation edge follow velocity smooth algo config parameters failed!");
    // }
}

void NavigationEdgeFollowAlg::PredictTrajectoryConfigParamToPredictTrajectoryAlgParam(const NavigationPredictTrajectoryAlgConfig &config, PredictTrajectoryAlgParam &param)
{
    param.twirling_scale = config.twirling_scale;
    param.perfer_forward_scale = config.perfer_forward_scale;
    param.path_follow_scale = config.path_follow_scale;
    param.path_angle_scale = config.path_angle_scale;
    param.expand_dis = config.expand_dis; // 0.25
    param.expand_dis_check = config.expand_dis_check;
    param.min_counter_size = config.min_counter_size; // 10
    param.look_ahead_dis = config.look_ahead_dis;
    param.positive_sdf_gain = config.positive_sdf_gain;
    param.negative_sdf_gain = config.negative_sdf_gain;
    param.max_yaw_speed = config.max_yaw_speed;
    param.predict_time = config.predict_time;
    param.d_prox = config.d_prox;
    param.v_max = config.v_max;
    param.alpha = config.alpha;
    param.beta = config.beta;
    param.find_target = config.find_target;
}

void NavigationEdgeFollowAlg::InitAlgorithm()
{
    predict_trajectory_ = std::make_unique<PredictTrajectory>(predict_trajectory_alg_param_);
    velocity_smooth_alg_ = std::make_unique<NavigationVelocitySmoothAlg>(velocity_smooth_alg_param_);
    path_track_alg_ = std::make_unique<NavigationPathTrackAlg>(path_track_alg_param_);
}

void NavigationEdgeFollowAlg::SetEdgeFollowFlags(bool status)
{
    is_detect_boundary_ = status;
    turn_right_times_ = 0;
    obstacle_avoid_duration_times_ = 0;
    all_grass_go_ahead_times_ = 0;
}

VelocitySmoothAlgResult NavigationEdgeFollowAlg::DealVelocitySmooth(float linear, float angular)
{
    VelocitySmoothAlgInput input{linear, angular, GetTimestampMs()};
    VelocitySmoothAlgResult result = velocity_smooth_alg_->DoVelocitySmooth(input);
    if (result.angular.z > min_vel_angular_)
    {
        turn_right_times_--;
    }
    else if (result.angular.z < 0)
    {
        if (fabs(result.angular.z) > min_vel_angular_)
        {
            turn_right_times_++;
        }
    }

    return result;
}

PredictTrajectoryAlgResult NavigationEdgeFollowAlg::DealPredictTrajectory(cv::Mat &imageROI, const OdomResult &odom_result)
{
    float linear = 0;
    float angular = 0;

    LOG_DEBUG("******* Non grass on right, walk along Edge! ********");

    /*****version:1.1*****/

    is_detect_boundary_ = true;
    /*利用动作采样方法进行沿边*/
    Twist2D current_velocity;
    Pose start_pose;
    current_velocity.x = odom_result.linear;
    current_velocity.theta = odom_result.angular;
    /*每一个规划周期，机器人都位于车身坐标系原点*/
    start_pose.x = 0;
    start_pose.y = 0;
    start_pose.theta = 0;
    std::vector<Twist2D> sample_Twists = predict_trajectory_->SampleVelocity(current_velocity);
    LOG_DEBUG("sample_Twists size: {}", sample_Twists.size());
    std::vector<std::vector<Pose>> TrajectoryList = predict_trajectory_->Generator(current_velocity, start_pose, sample_Twists);
    LOG_DEBUG("TrajectoryList size: {}", TrajectoryList.size());

    /*生成dm以及完成碰撞检测*/
    std::vector<int> free_idx = predict_trajectory_->CheckSafeAndGenerateDm(imageROI, TrajectoryList);
    LOG_DEBUG("free_idx size: {}", free_idx.size());

    if (!free_idx.empty() && predict_trajectory_->get_ref_path_)
    {
        int best_idx = predict_trajectory_->ChooseBestTraj(sample_Twists, TrajectoryList, free_idx);
        LOG_DEBUG("best_idx: {}, vel_linear: {}, vel_angular: {}", best_idx, sample_Twists.at(best_idx).x, sample_Twists.at(best_idx).theta);
        if (!vel_smooth_enable_)
        {
            linear = sample_Twists.at(best_idx).x;
            angular = sample_Twists.at(best_idx).theta;
        }
        else
        {
            // 使用速度平滑算法
            auto vel_smooth_result = DealVelocitySmooth(sample_Twists.at(best_idx).x, sample_Twists.at(best_idx).theta);
            linear = vel_smooth_result.linear.x;
            angular = vel_smooth_result.angular.z;
        }
    }
    else
    {
        if (!predict_trajectory_->get_ref_path_)
        {
            LOG_DEBUG("predict_trajectory_->get_ref_path_ == false!!!!");
        }
        linear = 0;
        angular = 0;
        zero_vel_times_++;
        if (zero_vel_times_ > max_zero_vel_times_)
        {
            is_start_edge_follow_ = false;
            LOG_DEBUG("-------------------------------------- is_start_walk_along_edge is: {} ------------------", is_start_edge_follow_);
            zero_vel_times_ = 0;
        }
        LOG_DEBUG("******* walk along Edge but free_idx is empty!!********");
    }

    return PredictTrajectoryAlgResult(linear, angular);
}

PredictTrajectoryAlgResult NavigationEdgeFollowAlg::DealPredictTrajectory(cv::Mat &imageROI)
{
    float linear = 0;
    float angular = 0;

    LOG_DEBUG("******* Non grass on right, walk along Edge! ********");

    is_detect_boundary_ = true;
    is_vel_smooth_velocity_valid_ = true;

    /*judge whether run in clockwise*/
    cv::Rect roi1(0, 0, 50, 120);
    cv::Mat area1 = imageROI(roi1);
    cv::Rect roi2(0, 120, 50, 120);
    cv::Mat area2 = imageROI(roi2);
    cv::Rect roi3(0, 240, 50, 120);
    cv::Mat area3 = imageROI(roi3);
    cv::Rect roi4(160, 0, 240, 360);
    cv::Mat area4 = imageROI(roi4);
    if (cv::countNonZero(area4) == 0 && cv::countNonZero(area1) > 0 && cv::countNonZero(area2) > 0 && cv::countNonZero(area3) > 0)
    {
        clockwise_ = true;
        // PubVelocity(0.1,0,clockwise_walk_t_);
    }
    else if (cv::countNonZero(area1) == 0 && cv::countNonZero(area2) == 0 && cv::countNonZero(area3) == 0)
    {
        clockwise_ = false;
    }
    LOG_ERROR("******* clockwise_ ******** {}", clockwise_);
    if (!clockwise_)
    {
        /*生成dm以及完成碰撞检测*/
        auto ctrl = predict_trajectory_->GenerateAdpVelCtr(imageROI);

        LOG_DEBUG("predict_trajectory_->get_ref_path_ {}, predict_trajectory_->all_traj_crash_ {}",
                  predict_trajectory_->get_ref_path_, predict_trajectory_->all_traj_crash_);

        LOG_DEBUG("******** ctrl linear {} angular {}", ctrl.first, ctrl.second);

        if (predict_trajectory_->get_ref_path_ && !predict_trajectory_->all_traj_crash_)
        {
            if (!vel_smooth_enable_)
            {
                linear = ctrl.first;
                angular = ctrl.second;
            }
            else
            {
                // 使用速度平滑算法
                auto vel_smooth_result = DealVelocitySmooth(ctrl.first, ctrl.second);
                linear = vel_smooth_result.linear.x;
                angular = vel_smooth_result.angular.z;
            }
        }
        else
        {
            linear = 0;
            angular = 0;
            zero_vel_times_++;
            if (zero_vel_times_ > max_zero_vel_times_)
            {
                is_start_edge_follow_ = false;
                LOG_DEBUG("is_start_edge_follow_ is {}", is_start_edge_follow_);
                zero_vel_times_ = 0;
            }
            LOG_DEBUG("******* walk along Edge but untravelable or ctrl v == 0!!******** zero_vel_time: {}", zero_vel_times_);
        }
    }
    else
    {
        linear = std::fabs(wheel_v_ / 2);
        angular = -wheel_v_ / wheel_base_;
    }
    return PredictTrajectoryAlgResult(linear, angular);
}
#if 0
EdgeFollowAlgResult NavigationEdgeFollowAlg::DealEdgeFollowWithPredictTrajectory(const PerceptionFusionResult &fusion_result,
                                                                                 const OdomResult &odom_result)
{
    float linear = 0;
    float angular = 0;
    bool is_clockwise = false;
    int rows = fusion_result.boundary_result.inverse_perspect_mask.image.rows; // height
    int cols = fusion_result.boundary_result.inverse_perspect_mask.image.cols; // width
    // 跳过前面 5 帧图像
    if (skip_bev_image_nums_++ < 5)
    {
        return EdgeFollowAlgResult(linear, angular);
    }

    cv::Mat imageROI = fusion_result.boundary_result.inverse_perspect_mask.image;
    // cv::imwrite("/userdata/image/imageROI.png", imageROI);
    // 存在非草地
    if (isHaveNonGrass(imageROI))
    {
        obstacle_avoid_duration_times_ = 0;
        all_grass_go_ahead_times_ = 0;
        // 非草地在机器人左边，左转
        if (isNonGrassOnLeft(imageROI, dividing_line_left_, dividing_line_right_) && !is_start_edge_follow_)
        {
            LOG_DEBUG("******* Non grass on left, turn left! ********");
            is_detect_boundary_ = is_turn_right_times_greater_threshold_ ? false : true;
            turn_right_times_--;
            linear = 0;
            angular = edge_follow_angular_;
        }
        else // 非草地在右边，沿非草地行走
        {
            // 持续绕障碍物走了一圈后，如果又检测到了右边非草地，此时要保持走直线， 逃离障碍物。
            if (is_turn_right_times_greater_threshold_)
            {
                LOG_DEBUG("Obstacle on right and turn_right_times_ greater threshold, turn left!");
                turn_right_times_--;
                linear = 0.1;
                angular = edge_follow_angular_;
            }
            else
            {
                if (!is_start_edge_follow_)
                {
                    is_start_edge_follow_ = true;
                }
                /*利用动作采样方法进行沿边*/
                // auto pre_trajectory_result = DealPredictTrajectory(imageROI, odom_result);
                auto pre_trajectory_result = DealPredictTrajectory(imageROI);
                linear = pre_trajectory_result.linear;
                angular = pre_trajectory_result.angular;
            }
        }
    }
    else // 全是草地
    {
        LOG_ERROR("All grass is_detect_boundary_ {}!", is_detect_boundary_);
        LOG_ERROR("max_all_grass_go_ahead_times_ {}", max_all_grass_go_ahead_times_);
        LOG_ERROR("max_obstacle_avoid_duration_times_ {}!", max_obstacle_avoid_duration_times_);
        is_turn_right_times_greater_threshold_ = false;
        if (!is_detect_boundary_)
        {
            LOG_DEBUG("All grass is_detect_boundary_ is false turn_right_times_ 0, go ahead!");
            // turn_right_times_ = 0;
            LOG_DEBUG("&&&& all_grass_go_ahead_times_ ++ {}****", all_grass_go_ahead_times_);
            if (all_grass_go_ahead_times_ < max_all_grass_go_ahead_times_)
            {
                obstacle_avoid_duration_times_ = 0;
                linear = edge_follow_linear_;
                angular = 0;
                all_grass_go_ahead_times_++;
            }
            else
            {
                linear = 0;
                angular = -edge_follow_angular_;
                turn_right_times_++;
                LOG_DEBUG("&&&& go ahead too long , turn right****");
            }
        }
        else
        {
            if (obstacle_avoid_duration_times_ < max_obstacle_avoid_duration_times_)
            {
                LOG_DEBUG("All grass is_detect_boundary_ is true, obstacle_avoid_duration_times_ {} go ahead!", obstacle_avoid_duration_times_);
                linear = edge_follow_linear_;
                angular = 0;
                obstacle_avoid_duration_times_++;
            }
            else
            {
                linear = 0;
                angular = -edge_follow_angular_;
                turn_right_times_++;
                LOG_DEBUG("All grass is_detect_boundary_ is true, turn_right_times_ {} true right!", turn_right_times_);
            }
        }
    }
    LOG_ERROR("all_grass_go_ahead_times_ {}", all_grass_go_ahead_times_);
    LOG_ERROR("obstacle_avoid_duration_times_ {}", obstacle_avoid_duration_times_);
    return EdgeFollowAlgResult(linear, angular);
}
#endif
#if 1
EdgeFollowAlgResult NavigationEdgeFollowAlg::DealEdgeFollowWithPredictTrajectory(const PerceptionFusionResult &fusion_result,
                                                                                 const OdomResult &odom_result)
{
    (void)odom_result;
    float linear = 0;
    float angular = 0;
    // bool is_clockwise = false;
    // int rows = fusion_result.boundary_result.inverse_perspect_mask.image.rows; // height
    // int cols = fusion_result.boundary_result.inverse_perspect_mask.image.cols; // width
    cv::Mat imageROI = fusion_result.boundary_result.inverse_perspect_mask.image;
    // 存在非草地
    if (isHaveNonGrass(imageROI))
    {
        all_grass_go_ahead_times_ = 0;
        turn_right_times_ = 0;
        // 非草地在机器人左边，左转
        if (isNonGrassOnLeft(imageROI, dividing_line_left_, dividing_line_right_) && !is_start_edge_follow_)
        {
            LOG_DEBUG("******* Non grass on left, turn left! ********");
            linear = 0;
            angular = edge_follow_angular_;
        }
        else // 非草地在右边，沿非草地行走
        {
            if (!is_start_edge_follow_)
            {
                is_start_edge_follow_ = true;
            }
            /*利用动作采样方法进行沿边*/
            auto pre_trajectory_result = DealPredictTrajectory(imageROI);
            linear = pre_trajectory_result.linear;
            angular = pre_trajectory_result.angular;
        }
    }
    else // 全是草地
    {
        LOG_DEBUG("&&&& all_grass_go_ahead_times_ ++ {}****", all_grass_go_ahead_times_);
        if (all_grass_go_ahead_times_ < max_all_grass_go_ahead_times_)
        {
            linear = edge_follow_linear_;
            angular = 0;
            all_grass_go_ahead_times_++;
        }
        else
        {
            linear = 0;
            angular = -edge_follow_angular_;
            turn_right_times_++;
            LOG_DEBUG("&&&& go ahead too long , turn right****");
        }
        if (turn_right_times_ > 2000)
        {
            linear = edge_follow_linear_;
            angular = 0;
        }
    }
    return EdgeFollowAlgResult(linear, angular);
}
#endif

EdgeFollowAlgResult NavigationEdgeFollowAlg::DealEdgeFollowWithPathTrack(const PerceptionFusionResult &fusion_result, const OdomResult &odom_result)
{
    (void)odom_result;
    float linear = 0;
    float angular = 0;
    float pixels_to_meters = fusion_result.boundary_result.inverse_perspect_mask.pixels_to_meters;
    Point mower_point = fusion_result.mower_point;
    int rows = fusion_result.boundary_result.inverse_perspect_mask.image.rows; // height
    int cols = fusion_result.boundary_result.inverse_perspect_mask.image.cols; // width

    // 跳过前面 5 帧图像
    if (skip_bev_image_nums_++ < 5)
    {
        return EdgeFollowAlgResult(linear, angular);
    }

    if (rows > height_ && cols > width_)
    {
        // 创建ROI
        cv::Rect roi(x_, y_, width_, height_);
        // 提取ROI对应的图像部分
        cv::Mat imageROI = fusion_result.boundary_result.inverse_perspect_mask.image(roi);
        // 存在非草地
        if (isHaveNonGrass(imageROI))
        {
            obstacle_avoid_duration_times_ = 0;
            // 非草地在机器人左边，左转
            if (isNonGrassOnLeft(imageROI, dividing_line_left_, dividing_line_right_))
            {
                LOG_DEBUG("******* Non grass on left, turn left! ********");
                is_detect_boundary_ = is_turn_right_times_greater_threshold_ ? false : true;
                turn_right_times_--;
                linear = 0;
                angular = edge_follow_angular_;
            }
            else // 非草地在右边，沿非草地行走
            {
                // 持续绕障碍物走了一圈后，如果又检测到了右边非草地，此时因保持走直线， 逃离障碍物。
                if (is_turn_right_times_greater_threshold_)
                {
                    LOG_DEBUG("Obstacle on right and turn_right_times_ greater threshold, turn left!");
                    turn_right_times_--;
                    linear = 0.1;
                    angular = edge_follow_angular_;
                }
                else
                {
                    is_detect_boundary_ = true;
                    std::vector<cv::Point> path = GetEdgeFollowPath(fusion_result.boundary_result);
                    PathTrackAlgResult path_track_result;
                    if (0 != DealEdgeFollowTrackPath(path, pixels_to_meters, mower_point, path_track_result))
                    {
                        LOG_ERROR("DealEdgeFollowTrackPath error!");
                        return EdgeFollowAlgResult(linear, angular);
                    }
                    // PathTrack 方式沿边必须使用速度平滑
                    auto vel_smooth_result = DealVelocitySmooth(path_track_result.linear, path_track_result.angular);
                    linear = vel_smooth_result.linear.x;
                    angular = vel_smooth_result.angular.z;
                }
            }
        }
        else // 不存在非草地
        {
            is_turn_right_times_greater_threshold_ = false;
            if (!is_detect_boundary_)
            {
                LOG_DEBUG("All grass is_detect_boundary_ is false turn_right_times_ 0, go ahead!");
                // turn_right_times_ = 0;
                obstacle_avoid_duration_times_ = 0;
                linear = edge_follow_linear_;
                angular = 0;
            }
            else
            {
                if (obstacle_avoid_duration_times_ < max_obstacle_avoid_duration_times_)
                {
                    LOG_DEBUG("All grass is_detect_boundary_ is true, obstacle_avoid_duration_times_ {} go ahead!", obstacle_avoid_duration_times_);
                    linear = edge_follow_linear_;
                    angular = 0;
                    obstacle_avoid_duration_times_++;
                }
                else
                {
                    linear = 0;
                    angular = -edge_follow_angular_;
                    turn_right_times_++;
                    LOG_DEBUG("All grass is_detect_boundary_ is true, turn_right_times_ {} true right!", turn_right_times_);
                }
            }
        }
    }
    else
    {
        LOG_ERROR("DealEdgeFollowWithPathTrack mask width {} height {} error", cols, rows);
    }

    return EdgeFollowAlgResult(linear, angular);
}

// Distinguish left turn, straight ahead, right turn
#if 0
EdgeFollowAlgResult NavigationEdgeFollowAlg::DealEdgeFollowWithBEV(const PerceptionFusionResult &fusion_result, const OdomResult &odom_result)
{
    auto start = std::chrono::high_resolution_clock::now();
    float linear = 0;
    float angular = 0;
    int width = fusion_result.occupancy_grid.width;
    int height = fusion_result.occupancy_grid.height;
    float resolution = fusion_result.occupancy_grid.resolution;
    std::vector<std::vector<uint8_t>> grid = fusion_result.occupancy_grid.grid;
    // float dead_zone = 0.178;
    float wheel_base = 0.4;
    int left_line_x = 19 - ceil(wheel_base / resolution / 2);
    int right_line_x = 20 + ceil(wheel_base / resolution / 2);
    float look_ahead_dis = 1.178; // physical distance
    float danger_dis = 0.3;       // physical distance    slow down time = 0.85,if v_max = 0.3
    int slow_down_y = height - ceil((look_ahead_dis - dead_zone_) / resolution);
    int danger_y = height - ceil((danger_dis - dead_zone_) / resolution);
    float forward_obs_dis = look_ahead_dis; // when forward_obs_dis < look_ahead_dis should slow down
    int forward_obs_y = 0;
    int forward_obs_x = 0;
    int right_obs_y = 0;
    int interested_obs_y = 0;
    int left_obs_y = 0;
    float v_max = 0.4;//0.2 // if forward_obs_dis < 0.3 then v = 0 ; else v = 0.3/0.85*forward_obs_dis-0.3*0.3/0.85;
    float acc = 0.005;
    bool is_slow_down;
    bool turn_right;
    bool turn_left;
    bool found_obs_in_head = false;
    bool found_obs_in_danger = false;
    bool found_obs_in_left = false;
    bool found_obs_in_right = false;
    bool found_obs_in_interested = false;

    // fabs(wheel_v / 2), wheel_v / wheel_base_
    float wheel_v = 0.1;

    // base on occupany grid Distinguish left ,right and forward whether have obs
    for (int y = height - 1; y >= 0; y--)
    {
        if (found_obs_in_head)
        {
            break;
        }

        for (int x = left_line_x + 1; x < right_line_x; x++)
        {
            if (grid[y][x] == 1) // find obs
            {
                // forward_obs_dis = resolution * sqrt((height-y)*(height-y)+(19.5-x)*(19.5-x));//the closest distance in forward
                forward_obs_dis = resolution * (height - 1 - y) + dead_zone_;
                forward_obs_y = y;
                forward_obs_x = x;
                if (forward_obs_y > danger_y)
                {
                    found_obs_in_danger = true;
                }

                found_obs_in_head = true;
                break;
            }
        }
    }
    LOG_DEBUG("danger_y-------------{}", danger_y);
    for (int y = height - 1; y >= danger_y; y--)
    {
        if (found_obs_in_right)
        {
            break;
        }
        for (int x = right_line_x; x < width; x++)
        {
            if (grid[y][x] == 1)
            {
                found_obs_in_right = true;
                right_obs_y = y;
                break;
            }
        }
    }
    int grid_num = 0;
    int obs_num = 0;
    for (int y = height - 1; y >= danger_y; y--)
    {
        for (int x = right_line_x; x < right_line_x + 8; x++)
        {
            grid_num += 1;
            if (grid[y][x] == 1)
            {
                obs_num += 1;
            }
        }
    }
    if (float(obs_num) / float(grid_num) >= 0.1)
    {
        found_obs_in_interested = true;
    }

    for (int y = height - 1; y >= danger_y; y--)
    {
        if (found_obs_in_left)
        {
            break;
        }
        for (int x = 0; x < left_line_x; x++)
        {
            if (grid[y][x] == 1)
            {
                found_obs_in_left = true;
                left_obs_y = y;
                break;
            }
        }
    }

    if (found_obs_in_danger)
    {
        is_obs_avoid_ = true;
        // is_go_ahead_ = false;
        start_find_line_ = false;
    }
    else if (!found_obs_in_danger && found_obs_in_interested)
    {
        // is_obs_avoid_ = false;
        is_go_ahead_ = true;
        start_find_line_ = false;
    }
    else if (!found_obs_in_danger && !found_obs_in_interested && found_obs_in_right)
    {
        is_turn_right_ = true;
        start_find_line_ = false;
    }
    else if (!found_obs_in_danger && !found_obs_in_right && !found_obs_in_left)
    {
        is_find_line_ = true;
    }
    else if (!found_obs_in_danger && !found_obs_in_right && found_obs_in_left)
    {
        is_find_line_ = true;
    }

    // depend th current status take difference action
    if (is_go_ahead_)
    {
        // no obs in farword,go ahead  or turn right ,it depend on whether have obs in right pass

        // if (!found_obs_in_head)
        // {
        //     linear = v_max;
        //     angular = 0;
        // }
        // else
        // {
        //     // it mean there is obs in forward,should take difference action depend on forward_obs_y
        //     // if ( slow_down_y < forward_obs_y && forward_obs_y < danger_y)
        //     // {
        //     //     // linear = 0.3/0.85*forward_obs_dis-0.3*0.3/0.85;
        //     //     linear = v_max;
        //     //     angular = 0;
        //     // }
        //     // else if (forward_obs_y < slow_down_y)
        //     // {
        //     //     linear = v_max;
        //     //     angular = 0;
        //     // }
        //     // else//  danger_y < forward_obs_y
        //     // {
        //     //     linear = 0;
        //     //     angular = 0;
        //     //     start_obs_avoid_ = true;
        //     // }

        //     // linear = v_max/2;
        //     linear = v_max;
        //     angular = 0;
        // }
        linear = last_linear_ + acc;
        if (linear >= v_max)
        {
            linear = v_max;
        }
        angular = 0;
        turn_right_bev_ = 0;
    }

    if (is_obs_avoid_)
    {
        linear = last_linear_ - acc;        
        if (forward_obs_x < 20)
        {
            wheel_v = 0.2;
        }
        else if (20 <= forward_obs_x <= 24)
        {
            wheel_v = 0.15;//0.1
        }
        else
        {
            wheel_v = 0.1;//0.05
        }
        if (linear <= fabs(wheel_v / 2))
        {
            linear = fabs(wheel_v / 2);
            angular = wheel_v / wheel_base;
        }
        else
        {
            angular = 0;
        }
        turn_right_bev_ = 0;
    }

    if (is_turn_right_)
    {
        linear = last_linear_ -acc;
        wheel_v = 0.1;//0.05
        if (linear <= fabs(wheel_v / 2))
        {
            linear = fabs(wheel_v / 2);
            angular = -wheel_v / wheel_base;
        }
        else
        {
            angular = 0;
        }
        turn_right_bev_ = 0;
    }

    if (is_find_line_)
    {
        if (!start_find_line_)
        {
            start_find_line_ = true;
            find_line_t1_ = std::chrono::high_resolution_clock::now();
        }
        std::chrono::duration<double> duration = std::chrono::high_resolution_clock::now() - find_line_t1_;
        float go_straight_dis = duration.count() * 0.2;
        if (go_straight_dis < 0.54)
        {
            linear = 0.2;
            angular = 0;
            LOG_DEBUG("find line go straight");
        }
        else
        {
            LOG_DEBUG("find line turn right find line turn_right_line {}", turn_right_bev_);
            if (turn_right_bev_ > 2000)
            {
                linear = edge_follow_linear_;
                angular = 0;
            }
            else
            {
                linear = fabs(wheel_v / 2);
                angular = -wheel_v / wheel_base;
                turn_right_bev_++;
            }
        }
    }

    if (is_go_ahead_)
    {
        last_state_ = 0;
        LOG_DEBUG("-----is go ahead-----");
    }
    else if (is_obs_avoid_)
    {
        last_state_ = 1;
        LOG_DEBUG("-----is obs avoid-----");
    }
    else if (is_find_line_)
    {
        last_state_ = 2;
        LOG_DEBUG("-----is find line-----");
    }
    else if (is_turn_right_)
    {
        last_state_ = 3;
        LOG_DEBUG("-----is turn right-----");
    }
    else
    {
        LOG_ERROR("-----error can not take action-----");
    }
    LOG_DEBUG("go ahead:{} , obs avoid:{} , find line: {}", is_go_ahead_, is_obs_avoid_, is_find_line_);
    is_go_ahead_ = false;
    is_obs_avoid_ = false;
    is_find_line_ = false;
    is_turn_right_ = false;
    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double> duration = end - start;
    LOG_DEBUG("OCCUPANCY FOLLOW cost {} second", duration.count());
    LOG_DEBUG("dead zone : {}", dead_zone_);
    last_linear_ = linear;
    auto vel_smooth_result = DealVelocitySmooth(linear, angular);
    linear = vel_smooth_result.linear.x;
    angular = vel_smooth_result.angular.z;
    return EdgeFollowAlgResult(linear, angular);
}
#endif

// combine straight ahead and right turn
#if 0
EdgeFollowAlgResult NavigationEdgeFollowAlg::DealEdgeFollowWithBEV(const PerceptionFusionResult &fusion_result, const OdomResult &odom_result)
{
    auto start = std::chrono::high_resolution_clock::now();
    float linear = 0;
    float angular = 0;
    int width = fusion_result.occupancy_grid.width;
    int height = fusion_result.occupancy_grid.height;
    float resolution = fusion_result.occupancy_grid.resolution;
    std::vector<std::vector<uint8_t>> grid = fusion_result.occupancy_grid.grid;
    float wheel_base = 0.4;
    int left_line_x = 19 - ceil(wheel_base/resolution/2);
    int right_line_x = 20 + ceil(wheel_base/resolution/2);
    float look_ahead_dis = 1.178;   //physical distance
    float danger_dis = 0.3; // physical distance    slow down time = 0.85,if v_max = 0.3
    int slow_down_y = height- ceil((look_ahead_dis - dead_zone_)/resolution);
    int danger_y = height - ceil((danger_dis - dead_zone_)/resolution);
    float forward_obs_dis = look_ahead_dis;//when forward_obs_dis < look_ahead_dis should slow down
    int forward_obs_y = 0;
    int forward_obs_x = 0;
    int right_obs_y = 0;
    int interested_obs_y = 0;
    int left_obs_y = 0;
    float v_max = 0.2; //if forward_obs_dis < 0.3 then v = 0 ; else v = 0.3/0.85*forward_obs_dis-0.3*0.3/0.85;
    float acc = 0.01;
    bool is_slow_down;
    bool turn_right;
    bool turn_left;
    bool found_obs_in_head = false;
    bool found_obs_in_danger = false;
    bool found_obs_in_left = false;
    bool found_obs_in_right = false;
    bool found_obs_in_interested = false;
    float k_p = 0.1;
    // fabs(wheel_v / 2), wheel_v / wheel_base_
    float wheel_v = 0.1;
    
    // base on occupany grid Distinguish left ,right and forward whether have obs
    for (int y = height-1; y >= 0; y--)
    {
        if (found_obs_in_head)
        {
            break;
        }
        
        for(int x = left_line_x + 1 ; x < right_line_x ; x++)
        {
            if (grid[y][x] == 1) //find obs
            {
                // forward_obs_dis = resolution * sqrt((height-y)*(height-y)+(19.5-x)*(19.5-x));//the closest distance in forward
                forward_obs_dis = resolution * (height-1-y) + dead_zone_;
                forward_obs_y = y;
                forward_obs_x = x;
                if (forward_obs_y > danger_y)
                {
                    found_obs_in_danger = true;
                }
                
                found_obs_in_head = true;
                break;
            }
            
        }
    }
    int grid_forward_num = 0;
    int obs_forward_num = 0;
    for(int y = height -1 ; y >= danger_y; y--)
    {
        for (int x = right_line_x -5; x < right_line_x; x++)
        {
            grid_forward_num++;
            if(grid[y][x]==1){
                obs_forward_num ++;
            }
        }
        
    }

    // LOG_DEBUG("danger_y-------------{}",danger_y);
    for (int y = height -1 ; y >= danger_y; y--)
    {
        if (found_obs_in_right)
        {
            break;
        }
        for (int x = right_line_x; x < width; x++)
        {
            if (grid[y][x] == 1)
            {
                found_obs_in_right = true;
                right_obs_y = y;
                break;
            }
        }
    }
    int grid_num = 0;
    int obs_num = 0;
    for (int y = height -1 ; y >= danger_y; y--)
    {
        for (int x = right_line_x; x < right_line_x + 1 ; x++)
        {
            grid_num += 1;
            if (grid[y][x] == 1)
            {
                obs_num += 1;
            }
        }
    }
    if (float(obs_num)/float(grid_num) >= 0.1)
    {
        found_obs_in_interested = true;
    }
    
    
    for (int y = height -1 ; y >= danger_y; y--)
    {
        if (found_obs_in_left)
        {
            break;
        }
        for (int x = left_line_x - 1; x < left_line_x; x++)
        {
            if (grid[y][x] == 1)
            {
                found_obs_in_left = true;
                left_obs_y = y;
                break;
            }
        }
    }
   
    if (found_obs_in_danger)
    {
        is_obs_avoid_ = true;
        // is_go_ahead_ = false;
        start_find_line_ = false;
    }
    else if(!found_obs_in_danger && found_obs_in_interested||
            !found_obs_in_danger && !found_obs_in_interested && found_obs_in_right)
    {
        // is_obs_avoid_ = false;
        is_go_ahead_ = true;
        start_find_line_ = false;
    }
    else if(!found_obs_in_danger && !found_obs_in_right && !found_obs_in_left)
    {
        is_find_line_ = true;
    }
    else if(!found_obs_in_danger && !found_obs_in_right && found_obs_in_left)
    {
        is_find_line_ = true;
    }
    
    //depend th current status take difference action 
    if (is_go_ahead_)
    {
        linear = last_linear_ + acc_;
        if (linear >= v_max)
        {
            linear = v_max;
        }
        double err = 1.0 - float(obs_num)/float(grid_num);
        angular = - k_p * err; 
        LOG_DEBUG("%____GO AHEAD____{}",err);
    }

    if (is_obs_avoid_)
    {
        // if(forward_obs_x < 20){
        //     wheel_v = 0.2;
        // }
        // else if(20<=forward_obs_x <= 24){
        //     wheel_v = 0.1;
        // }
        // else{
        //     wheel_v = 0.05;
        // }
        // linear = fabs(wheel_v / 2);
        // angular = wheel_v / wheel_base;
        // turn_right_bev_ = 0;
        if(forward_obs_x >= right_line_x-3){
            linear = v_max;
            double err = float(obs_forward_num)/float(grid_forward_num);
            angular = 0.1 * err;
            LOG_DEBUG("%____TURN LEFT____{}",err);
        }
        else{
            wheel_v = 0.1;
            linear = fabs(wheel_v / 2);
            angular = wheel_v / wheel_base;
        }
        turn_right_bev_ = 0;
    }
    
    if(is_turn_right_)
    {
        wheel_v = 0.05;
        linear = fabs(wheel_v / 2);
        angular = -wheel_v / wheel_base;
        turn_right_bev_ = 0;
    }


    if (is_find_line_)
    {
        if (!start_find_line_)
        {
            start_find_line_ = true;
            find_line_t1_ = std::chrono::high_resolution_clock::now();
        }
        std::chrono::duration<double> duration = std::chrono::high_resolution_clock::now() - find_line_t1_;
        float go_straight_dis = duration.count() * 0.2;
        if (go_straight_dis < 0.54)
        {
            linear = 0.2;
            angular = 0;
            LOG_DEBUG("find line go straight");
        }
        else
        {

            LOG_DEBUG("find line turn right find line turn_right_line {}",turn_right_bev_);
            if (turn_right_bev_ > 2000)
            {
                linear = edge_follow_linear_;
                angular = 0;
            }
            else
            {
                linear = fabs(wheel_v / 2);
                angular = -wheel_v / wheel_base;
                turn_right_bev_ ++;
            }
            
        }
    }


    if (is_go_ahead_)
    {
        last_state_= 0;
        LOG_DEBUG("-----is go ahead-----");
    }
    else if(is_obs_avoid_)
    {
        last_state_ = 1;
        LOG_DEBUG("-----is obs avoid-----");
    }
    else if(is_find_line_)
    {
        last_state_ = 2;
        LOG_DEBUG("-----is find line-----");
    }
    else if(is_turn_right_)
    {
        last_state_ = 3;
        LOG_DEBUG("-----is turn right-----");
    }
    else
    {
        LOG_ERROR("-----error can not take action-----");
    }
    LOG_DEBUG("go ahead:{} , obs avoid:{} , find line: {}",is_go_ahead_,is_obs_avoid_,is_find_line_);
    is_go_ahead_ = false;
    is_obs_avoid_ = false;
    is_find_line_ = false;
    is_turn_right_ = false;
    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double> duration = end - start;
    LOG_DEBUG("OCCUPANCY FOLLOW cost {} second",duration.count());
    last_linear_ = linear;
    return EdgeFollowAlgResult(linear, angular);
}
#endif

/**
 * @brief 获取沿边跟踪路径
 *
 * @param boundary_result
 * @return std::vector<cv::Point>
 */
std::vector<cv::Point> NavigationEdgeFollowAlg::GetEdgeFollowPath(const BoundaryResult &boundary_result)
{
    if (boundary_result.inverse_perspect_mask.image.rows > height_ &&
        boundary_result.inverse_perspect_mask.image.cols > width_)
    {
        // 创建ROI
        cv::Rect roi(x_, y_, width_, height_);
        // 提取ROI对应的图像部分
        cv::Mat imageROI = boundary_result.inverse_perspect_mask.image(roi);
        return GetFollowPath(imageROI);
    }

    return std::vector<cv::Point>{};
}

int NavigationEdgeFollowAlg::DealEdgeFollowTrackPath(const std::vector<cv::Point> &path,
                                                     float pixels_to_meters,
                                                     const Point &robot_pose,
                                                     PathTrackAlgResult &result)
{
    fescue_msgs__msg__NavigationEdgeFollowData edge_follow_data;
    edge_follow_data.pixels_to_meters = pixels_to_meters;
    edge_follow_data.robot_pose.position.x = robot_pose.x;
    edge_follow_data.robot_pose.position.y = robot_pose.y;
    edge_follow_data.robot_pose.position.z = 0;
    size_t point_size = path.size() > IOX_POSE_STAMPED_MAX ? IOX_POSE_STAMPED_MAX : path.size();
    for (size_t i = 0; i < point_size; i++)
    {
        geometry_msgs__msg__PoseStamped_iox pose_stamped;
        pose_stamped.header.stamp.sec = 0;
        pose_stamped.header.stamp.nanosec = 0;
        pose_stamped.pose.position.x = path[i].x;
        pose_stamped.pose.position.y = path[i].y;
        pose_stamped.pose.position.z = 0;
        edge_follow_data.path.poses.data.push_back(pose_stamped);
    }

    return path_track_alg_->DoPathTrack(edge_follow_data, result);
}

void NavigationEdgeFollowAlg::CheckLoop(const ob_mower_msgs::NavEscapeResult escape_result)
{
    LOG_ERROR_THROTTLE(1000, "NavigationEdgeFollowAlg escape_result {},{}", escape_result.is_loop, escape_result.timestamp_ms);
    if (loop_time_ > max_loop_time_)
    {
        is_escape_ = true;
        return;
    }

    if (escape_result.is_loop)
    {
        if (last_esacpe_time_ == 0)
        {
            loop_time_++;
            last_esacpe_time_ = escape_result.timestamp_ms;
        }
        else
        {
            if (escape_result.timestamp_ms - last_esacpe_time_ < escape_delta_time_)
            {
                loop_time_++;
                last_esacpe_time_ = escape_result.timestamp_ms;
            }
            else
            {
                loop_time_ = 0;
                last_esacpe_time_ = 0;
            }
        }
    }
    else
    {
        loop_time_ = 0;
        last_esacpe_time_ = 0;
    }
}

void NavigationEdgeFollowAlg::ResetObsFlags()
{
    obs_in_danger_ = false;
    obs_in_forward_ = false;
    obs_in_right_ = false;
    obs_in_interested_ = false;
    obs_in_left_ = false;

    forward_obs_x_ = 0;
    forward_obs_y_ = 0;
    forward_grid_num_ = 0;
    forward_obs_num_ = 0;

    danger_obs_x_ = 0;
    danger_obs_y_ = 0;
    danger_grid_num_ = 0;
    danger_obs_num_ = 0;
    danger_partial_obs_x_ = 0;
    danger_partial_obs_y_ = 0;
    danger_partial_grid_num_ = 0;
    danger_partial_obs_num_ = 0;

    right_obs_x_ = 0;
    right_obs_y_ = 0;
    right_grid_num_ = 0;
    right_obs_num_ = 0;

    left_obs_x_ = 0;
    left_obs_y_ = 0;
    left_grid_num_ = 0;
    left_obs_num_ = 0;

    inte_obs_x_ = 0;
    inte_obs_y_ = 0;
    inte_grid_num_ = 0;
    inte_obs_num_ = 0;
}

#if 0
// need to reset pre_s parameters!
void NavigationEdgeFollowAlg::ChangeState(STATE new_state, string pos_call)
{
    if (exec_state_ == FIND_LINE)
    {
        fl_go_ahead_num_ = 0;
        fl_turn_r_spot_num_ = 0;
        fl_turn_r_num_ = 0;
        fl_slow_complete_ = false;
        fl_time_ = 0;
        start_find_line_ = false;
        fl_back_right_complete_ = false;
        start_fl_back_right_ = false;
        fl_back_right_dis_ = 0;
        fl_theta_ = 0;
        fl_forward_target_dis_ = 0.48;
        fl_go_straight_angluar_ = 0.3;
    }
    else if (exec_state_ == AVOID_OBS)
    {
        ao_turn_l_spot_num_ = 0;
        ao_turn_l_num_ = 0;
        ao_slow_complete_ = false;
        ao_time_ = 0;
    }
    else if (exec_state_ == ESCAPE)
    {
        loop_time_ = 0;
        last_esacpe_time_ = 0;
        is_escape_ = false;
        escape_rotation_complete_ = false;
        escape_rotation_start_ = false;
        escape_rotation_angle_ = 0;
    }

    string state_str[4] = {"FIND_LINE", "GO_STRAIGHT", "AVOID_OBS", "ESCAPE"};
    int pre_s = int(exec_state_);
    exec_state_ = new_state;
    LOG_ERROR("[{}]: from {} to {}", pos_call, state_str[pre_s], state_str[int(new_state)]);
}

void NavigationEdgeFollowAlg::CheckGrid(const std::vector<std::vector<uint8_t>> grid)
{
    // every time run this function must reset those obs Flags
    ResetObsFlags();
    int look_ahead_y = grid_height_ - ceil((look_ahead_dis_ - dead_zone_) / resolution_);
    int danger_y = grid_height_ - ceil((danger_dis_ - dead_zone_) / resolution_);

    for (int y = grid_height_ - 1; y >= danger_y; y--)
    {
        for (int x = right_line_x_ - 5; x < right_line_x_; x++)
        {
            danger_partial_grid_num_++;
            if (grid[y][x] == 1)
            {
                danger_partial_obs_num_++;
            }
        }
    }
    for (int y = grid_height_ - 1; y >= danger_y; y--)
    {
        for (int x = left_line_x_ + 1; x < right_line_x_; x++)
        {
            danger_grid_num_++;
            if (grid[y][x] == 1)
            {
                danger_obs_num_++;
                if (y > danger_obs_y_)
                {
                    danger_obs_y_ = y;
                    danger_obs_x_ = x;
                }
            }
        }
    }
    for (int y = danger_y - 1; y >= look_ahead_y; y--)
    {
        for (int x = left_line_x_ + 1; x < right_line_x_; x++)
        {
            forward_grid_num_++;
            if (grid[y][x] == 1)
            {
                forward_obs_num_++;
                if (y > forward_obs_y_)
                {
                    forward_obs_y_ = y;
                    forward_obs_x_ = x;
                }
            }
        }
    }
    for (int y = grid_height_ - 1; y >= look_ahead_y; y--)
    {
        for (int x = 0; x <= left_line_x_; x++)
        {
            left_grid_num_++;
            if (grid[y][x])
            {
                left_obs_num_++;
                if (y > left_obs_y_)
                {
                    left_obs_y_ = y;
                    left_obs_x_ = x;
                }
            }
        }
    }
    for (int y = grid_height_ - 1; y >= danger_y; y--)
    {
        for (int x = right_line_x_; x < grid_width_; x++)
        {
            if (x == right_line_x_)
            {
                inte_grid_num_++;
            }
            else
            {
                right_grid_num_++;
            }

            if (grid[y][x] == 1)
            {
                if (x == right_line_x_)
                {
                    inte_obs_num_++;
                    if (y > inte_obs_y_)
                    {
                        inte_obs_y_ = y;
                        inte_obs_x_ = x;
                    }
                }
                else
                {
                    right_obs_num_++;
                    if (y > right_obs_y_)
                    {
                        right_obs_y_ = y;
                        right_obs_x_ = x;
                    }
                }
            }
        }
    }
    if (forward_obs_num_ > 0)
    {
        obs_in_forward_ = true;
    }
    if (danger_obs_num_ > 0)
    {
        obs_in_danger_ = true;
    }
    if (right_obs_num_ > 0)
    {
        obs_in_right_ = true;
    }
    if (left_obs_num_ > 0)
    {
        obs_in_left_ = true;
    }
    if (float(inte_obs_num_) / float(inte_grid_num_) >= 0.1)
    {
        obs_in_interested_ = true;
    }
}


// FSM
EdgeFollowAlgResult NavigationEdgeFollowAlg::DealEdgeFollowWithBEV(const PerceptionFusionResult &fusion_result, const OdomResult &odom_result, const ob_mower_msgs::NavEscapeResult &escape_result)
{
    auto start = std::chrono::high_resolution_clock::now();
    float linear = 0;
    float angular = 0;
    grid_width_ = fusion_result.occupancy_grid.width;
    grid_height_ = fusion_result.occupancy_grid.height;
    resolution_ = fusion_result.occupancy_grid.resolution;
    CheckGrid(fusion_result.occupancy_grid.grid);
    if (stop_escape_)
    {
        // stop escape
        PublishNavAlgCtrl(true);
        // mark time point
        if (!mark_escape_time_)
        {
            stop_escape_t_ = std::chrono::high_resolution_clock::now();
            mark_escape_time_ = true;
        }
        std::chrono::duration<double> escape_duration = std::chrono::high_resolution_clock::now() - stop_escape_t_;
        if (escape_duration.count() >= escape_cool_time_)
        {
            stop_escape_ = false;
            mark_escape_time_ = false;
        }
    }
    else
    {
        PublishNavAlgCtrl(false);
        CheckLoop(escape_result);
    }

    LOG_ERROR_THROTTLE(1000, "Exec_state_:{},obs_in_danger:{},obs_in_right_:{},obs_in_interested:{},is_escape:{}", exec_state_, obs_in_danger_, obs_in_right_, obs_in_interested_, is_escape_);
    switch (exec_state_)
    {
    case FIND_LINE:
    {
        if (is_escape_)
        {
            ChangeState(ESCAPE, "STATE");
            linear = last_linear_ - slow_acc_;
            if (linear < 0)
            {
                linear = 0;
            }
            angular = 0;
            stop_escape_ = true;
        }
        else if (obs_in_danger_)
        {
            ChangeState(AVOID_OBS, "STATE");
            // slow down
            linear = last_linear_ - slow_acc_;
            if (linear < 0)
            {
                linear = 0;
            }
            angular = 0;
        }
        else if (obs_in_interested_ || obs_in_right_)
        {
            ChangeState(GO_STRAIGHT, "STATE");
            linear = last_linear_ - slow_acc_;
            if (linear < 0)
            {
                linear = 0;
            }
            angular = 0;
        }
        else // find line action
        {
            if (flAction_type_ == "straight_right")
            {
                FlAction_sr(linear, angular);
            }
            else if (flAction_type_ == "back_right")
            {
                FlAction_br(linear, angular);
            }
        }
        break;
    }
    case GO_STRAIGHT:
    {
        if (is_escape_)
        {
            ChangeState(ESCAPE, "STATE");
            linear = last_linear_ - slow_acc_;
            if (linear < 0)
            {
                linear = 0;
            }
            angular = 0;
            stop_escape_ = true;
        }
        else if (obs_in_danger_)
        {
            ChangeState(AVOID_OBS, "STATE");
            linear = last_linear_ - slow_acc_;
            if (linear < 0)
            {
                linear = 0;
            }
            angular = 0;
        }
        else if (!obs_in_interested_ && !obs_in_right_)
        {
            ChangeState(FIND_LINE, "STATE");
            linear = last_linear_ - slow_acc_;
            if (linear < 0)
            {
                linear = 0;
            }
            angular = 0;
        }
        else
        {
            GsAction(linear, angular);
        }
        break;
    }
    case AVOID_OBS:
    {
        if (!obs_in_danger_ && (obs_in_interested_ || obs_in_right_))
        {
            ChangeState(GO_STRAIGHT, "STATE");
            linear = last_linear_ + acc_; // whether + slow_acc_ is better?
            if (linear < 0)
            {
                linear = 0;
            }
            angular = 0;
        }
        else if (!obs_in_danger_ && !obs_in_interested_ && !obs_in_right_)
        {
            ChangeState(FIND_LINE, "STATE");
            linear = last_linear_ - slow_acc_;
            if (linear < 0)
            {
                linear = 0;
            }
            angular = 0;
        }
        else
        {
            AoAction(linear, angular);
        }
        break;
    }
    case ESCAPE:
    {
        if (obs_in_danger_)
        {
            ChangeState(AVOID_OBS, "STATE");
            linear = last_linear_ - slow_acc_;
            if (linear < 0)
            {
                linear = 0;
            }
            angular = 0;
        }
        else
        {
            EsAction(linear, angular);
        }
    }
    }
    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double> duration = end - start;
    LOG_DEBUG_THROTTLE(10000, "OCCUPANCY FOLLOW cost {} second,dead zone : {}", duration.count(), dead_zone_);
    // auto vel_smooth_result = DealVelocitySmooth(linear, angular);
    // last_linear_ = vel_smooth_result.linear.x;
    // last_angular_ = vel_smooth_result.angular.z;
    // return EdgeFollowAlgResult(vel_smooth_result.linear.x, vel_smooth_result.angular.z);
    last_linear_ = linear;
    last_angular_ = angular;
    LOG_DEBUG("***linear={},angular={}****", linear, angular);
    return EdgeFollowAlgResult(linear, angular);
}

void NavigationEdgeFollowAlg::FlAction(float &linear, float &angular)
{
    if (fl_time_ > max_fl_time_) // can't find line should go strange
    {
        linear = last_linear_ + acc_;
        if (linear > v_max_)
        {
            linear = v_max_;
        }
        LOG_ERROR_THROTTLE(1000, "can't find line should go strange! linear = {}", linear);
    }
    else
    {
        // if (fl_go_ahead_num_ < max_fl_go_ahead_num_)//go ahead
        // {
        //     linear = last_linear_ + acc_;
        //     if (linear > v_max_)
        //     {
        //         linear = v_max_;
        //     }
        //     angular = 0;
        //     LOG_ERROR_THROTTLE(1000,"find line go ahead! linear = {}",linear);
        //     fl_go_ahead_num_++;
        // }

        if (!start_find_line_)
        {
            start_find_line_ = true;
            find_line_t1_ = std::chrono::high_resolution_clock::now();
        }
        std::chrono::duration<double> duration = std::chrono::high_resolution_clock::now() - find_line_t1_;
        float go_straight_dis = duration.count() * 0.2;
        if (go_straight_dis < 0.54)
        {
            linear = 0.2;
            angular = 0;
            LOG_ERROR_THROTTLE(1000, "find line go straight");
        }
        else // slow down - turn right - singlewheel turn right
        {
            if (!fl_slow_complete_)
            {
                linear = last_linear_ - slow_acc_;
                angular = 0;
                if (linear < 0)
                {
                    linear = 0;
                    fl_slow_complete_ = true;
                }
            }
            else // finish slow down
            {
                if (fl_turn_r_spot_num_ < max_fl_turn_r_spot_num_)
                {
                    linear = 0;
                    angular = -fl_turn_r_spot_angular_;
                    LOG_ERROR_THROTTLE(1000, "find line spot turn right! linear = {},angular = {}", linear, angular);
                    fl_turn_r_spot_num_++;
                }
                else if (fl_turn_r_num_ < max_fl_turn_r_num_)
                {
                    // left wheel Forward in low speed , right wheel backward
                    linear = (-fl_turn_r_wheel_r_ + fl_turn_r_wheel_l_) / 2;
                    angular = (-fl_turn_r_wheel_r_ - fl_turn_r_wheel_l_) / wheel_base_;
                    LOG_ERROR_THROTTLE(1000, "find line spot turn! linear = {},angular = {}", linear, angular);
                    fl_turn_r_num_++;
                }
                else
                {
                    // still not find line,start next time
                    linear = 0;
                    angular = 0;
                    fl_go_ahead_num_ = 0;
                    fl_turn_r_spot_num_ = 0;
                    fl_turn_r_num_ = 0;
                    fl_slow_complete_ = false;
                    start_find_line_ = false;
                }
            }
        }
        fl_time_++;
    }
}

// go right-straight then right-back
void NavigationEdgeFollowAlg::FlAction_br(float &linear, float &angular)
{
    if (fl_time_ > max_fl_time_) // can't find line should go strange
    {
        linear = last_linear_ + acc_;
        if (linear > max_fl_v_)
        {
            linear = max_fl_v_;
        }
        LOG_ERROR_THROTTLE(1000, "can't find line should go strange! linear = {}", linear);
    }
    else
    {
        if (!start_find_line_)
        {
            start_find_line_ = true;
            find_line_t1_ = std::chrono::high_resolution_clock::now();
            linear = fl_go_straight_linear_;
            angular = fl_go_straight_angluar_;
            return;
        }
        std::chrono::duration<double> duration = std::chrono::high_resolution_clock::now() - find_line_t1_;
        float go_straight_dis = duration.count() * fl_go_straight_linear_;
        if (go_straight_dis < fl_forward_r_target_dis_)
        {
            linear = fl_go_straight_linear_;
            angular = fl_go_straight_angluar_;
            LOG_ERROR_THROTTLE(1000, "find line go straight");
        }
        else // slow down - turn backward-right
        {
            if (!fl_slow_complete_)
            {
                linear = last_linear_ - slow_acc_;
                angular = 0;
                if (linear < 0)
                {
                    linear = 0;
                    fl_slow_complete_ = true;
                }
            }
            else // finish slow down
            {
                if (!fl_back_right_complete_)
                {
                    if (!start_fl_back_right_)
                    {
                        start_fl_back_right_ = true;
                        find_line_t2_ = std::chrono::high_resolution_clock::now();
                        linear = -fl_turn_r_linear_;
                        angular = -fl_turn_r_angular_;
                        return;
                    }
                    auto duration_t = std::chrono::high_resolution_clock::now();
                    std::chrono::duration<double> duration_br = duration_t - find_line_t2_;
                    fl_back_right_dis_ = fl_back_right_dis_ + last_linear_ * duration_br.count() * cos(fl_theta_);
                    fl_theta_ = fl_theta_ + duration_br.count() * last_angular_;
                    // LOG_ERROR("fl_back_right_dis_:{}", fl_back_right_dis_);
                    find_line_t2_ = duration_t;
                    if (fl_back_right_dis_ <= fl_back_right_target_dis_)
                    {
                        fl_back_right_complete_ = true;
                        angular = 0;
                        linear = 0;
                    }
                    else
                    {
                        linear = -fl_turn_r_linear_;
                        angular = -fl_turn_r_angular_;
                    }
                }
                else
                {
                    start_find_line_ = false;
                    fl_slow_complete_ = false;
                    fl_back_right_complete_ = false;
                    start_fl_back_right_ = false;
                    fl_back_right_dis_ = 0;
                    fl_theta_ = 0;
                    fl_go_straight_angluar_ = 0;
                    // fl_forward_target_dis_ = 0.225;
                    fl_forward_r_target_dis_ = 0.15;
                }
            }
        }
        fl_time_++;
        LOG_ERROR_THROTTLE(1000, "fl_time_:{}", fl_time_);
    }
}

// go straight then turn 90 degree
void NavigationEdgeFollowAlg::FlAction_sr(float &linear, float &angular)
{
    if (fl_time_ > max_fl_time_) // can't find line should go strange
    {
        linear = last_linear_ + acc_;
        if (linear > max_fl_v_)
        {
            linear = max_fl_v_;
        }
        LOG_ERROR_THROTTLE(1000, "can't find line should go strange! linear = {}", linear);
    }
    else
    {
        if (!start_find_line_)
        {
            start_find_line_ = true;
            find_line_t1_ = std::chrono::high_resolution_clock::now();
            linear = fl_go_straight_linear_;
            angular = 0;
            return;
        }
        std::chrono::duration<double> duration = std::chrono::high_resolution_clock::now() - find_line_t1_;
        float go_straight_dis = duration.count() * fl_go_straight_linear_;
        if (go_straight_dis < fl_forward_target_dis_)
        {
            linear = fl_go_straight_linear_;
            angular = 0;
            LOG_ERROR_THROTTLE(1000, "find line go straight");
        }
        else // slow down - turn backward-right
        {
            if (!fl_slow_complete_)
            {
                linear = last_linear_ - slow_acc_;
                angular = 0;
                if (linear < 0)
                {
                    linear = 0;
                    fl_slow_complete_ = true;
                }
            }
            else // finish slow down
            {
                if (!fl_back_right_complete_)
                {
                    if (!start_fl_back_right_)
                    {
                        start_fl_back_right_ = true;
                        find_line_t2_ = std::chrono::high_resolution_clock::now();
                        // linear = fabs(wheel_v_ / 2);
                        // angular = -wheel_v_ / wheel_base_;
                        linear = 0;
                        angular = -fl_spot_turn_r_angular_;
                        return;
                    }
                    auto duration_t = std::chrono::high_resolution_clock::now();
                    std::chrono::duration<double> duration_br = duration_t - find_line_t2_;
                    fl_back_right_dis_ = fl_back_right_dis_ + fabs(last_angular_) * duration_br.count();
                    // LOG_ERROR("fl_back_right_dis_:{}", fl_back_right_dis_);
                    find_line_t2_ = duration_t;
                    if (fl_back_right_dis_ >= fl_turn_right_target_)
                    {
                        fl_back_right_complete_ = true;
                        angular = 0;
                        linear = 0;
                    }
                    else
                    {
                        // linear = fabs(wheel_v_ / 2);
                        // angular = -wheel_v_ / wheel_base_;
                        linear = 0;
                        angular = -fl_spot_turn_r_angular_;
                    }
                }
                else
                {
                    start_find_line_ = false;
                    fl_slow_complete_ = false;
                    fl_back_right_complete_ = false;
                    start_fl_back_right_ = false;
                    fl_back_right_dis_ = 0;
                    fl_theta_ = 0;
                    fl_go_straight_angluar_ = 0;
                    // fl_forward_target_dis_ = 0.225;
                    fl_time_++;
                }
            }
        }
        LOG_ERROR_THROTTLE(1000, "fl_time_:{}", fl_time_);
    }
}

void NavigationEdgeFollowAlg::GsAction(float &linear, float &angular)
{
    // if (!obs_in_forward_)//adjust posture deped on right obs
    // {
    //     linear = last_linear_ + acc_;
    //     if(linear > v_max_){
    //         linear = v_max_;
    //     }
    //     float err = 1 - float(inte_obs_num_) / float(inte_grid_num_);
    //     angular = -kp_r_ * err;
    //     LOG_DEBUG_THROTTLE(1000,"NO obs in forward , linear:{} , angular:{}",linear,angular);
    // }
    // else//slow down and adjust posture depend on forward obs
    // {
    //     linear = last_linear_ - acc_;
    //     if(linear < v_max_/2){
    //         linear = v_max_/2;
    //     }
    //     float err = float(forward_obs_num_)/float(forward_grid_num_);
    //     angular = kp_l_ * err ;
    //     LOG_DEBUG_THROTTLE(1000,"Have obs in forward ,Avoid in advance , linear:{} , angular:{}",linear,angular);
    // }
    linear = last_linear_ + acc_;
    if (linear > v_max_)
    {
        linear = v_max_;
    }
    float err = 1 - float(inte_obs_num_) / float(inte_grid_num_);
    //assert(inte_obs_num_ < inte_grid_num_);
    angular = clamp(-kp_r_ * err, -kp_r_, float(0.0));
    LOG_DEBUG_THROTTLE(1000, "NO obs in forward , linear:{} , angular:{}", linear, angular);
}

void NavigationEdgeFollowAlg::AoAction(float &linear, float &angular)
{
    if (ao_time_ > max_ao_time_)
    {
        linear = 0;
        angular = 0;
        PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_EDGE_FOLLOW_OBSTACLE_AVOIDANCE_FAILURES_EXCEPTION);
        LOG_ERROR_THROTTLE(1000, "Avoid obs time is bigger than max_ao_time,trap!!, linear:{} , angular:{}", linear, angular);
    }
    else
    {
        if (danger_obs_x_ >= right_line_x_ - 3) // whether right_line_x_ - 5 is better?
        {
            linear = last_linear_ + acc_;
            if (linear > v_max_)
            {
                linear = v_max_;
            }
            float err = float(danger_partial_obs_num_) / float(danger_partial_grid_num_);
            //assert(danger_partial_obs_num_ < danger_partial_grid_num_);
            angular = clamp(kp_l_ * err, float(0.0), kp_l_);
            // LOG_ERROR_THROTTLE(1000, "%____TURN LEFT____{}", err);
            LOG_ERROR_THROTTLE(1000, "%____TURN LEFT angular____{}", angular);
        }
        else
        {
            if (!ao_slow_complete_)
            {
                linear = last_linear_ - slow_acc_;
                angular = 0;
                if (linear < 0)
                {
                    linear = 0;
                    ao_slow_complete_ = true;
                }
            }
            else // finish slow down
            {
                if (ao_turn_l_spot_num_ < max_ao_turn_l_spot_num_)
                {
                    linear = 0;
                    angular = ao_turn_l_spot_angular_;
                    LOG_ERROR_THROTTLE(1000, "avoid obs spot turn left! linear = {},angular = {}", linear, angular);
                    ao_turn_l_spot_num_++;
                }
                else if (ao_turn_l_num_ < max_ao_turn_l_num_)
                {
                    // left wheel backward,right wheel farword in low speed
                    linear = (ao_turn_l_wheel_r_ - ao_turn_l_wheel_l_) / 2;
                    angular = (ao_turn_l_wheel_r_ + ao_turn_l_wheel_l_) / wheel_base_;
                    LOG_ERROR_THROTTLE(1000, "avoid obs spot turn! linear = {},angular = {}", linear, angular);
                    ao_turn_l_num_++;
                }
                else
                {
                    // still not find line,start next time
                    linear = 0;
                    angular = 0;
                    ao_turn_l_spot_num_ = 0;
                    ao_turn_l_num_ = 0;
                    ao_slow_complete_ = false;
                }
            }
        }
        ao_time_++;
        LOG_ERROR_THROTTLE(1000, "ao_time_:{}", ao_time_);
    }
}

#endif
// new
#if 1
void NavigationEdgeFollowAlg::ChangeState(STATE new_state, string pos_call)
{
    if (exec_state_ == FIND_LINE)
    {
        start_find_line_ = false;
        start_fl_back_right_ = false;
        start_fl_tuning_ = false;
        fl_slow_complete_ = false;
        fl_back_right_complete_ = false;
        fl_tuning_complete_ = false;
        fl_back_right_dis_ = 0;
        fl_tuning_dis_ = 0;
        fl_tuning_angle_ = 0;
        fl_time_ = 0;
    }
    else if (exec_state_ == AVOID_OBS)
    {
        ao_turn_l_spot_num_ = 0;
        ao_turn_l_num_ = 0;
        ao_slow_complete_ = false;
        ao_time_ = 0;
    }
    else if (exec_state_ == ESCAPE)
    {
        loop_time_ = 0;
        last_esacpe_time_ = 0;
        is_escape_ = false;
        escape_rotation_complete_ = false;
        escape_rotation_start_ = false;
        escape_rotation_angle_ = 0;
    }

    string state_str[4] = {"FIND_LINE", "GO_STRAIGHT", "AVOID_OBS", "ESCAPE"};
    int pre_s = int(exec_state_);
    exec_state_ = new_state;
    LOG_ERROR("[{}]: from {} to {}", pos_call, state_str[pre_s], state_str[int(new_state)]);
}
void NavigationEdgeFollowAlg::CheckGrid(const std::vector<std::vector<uint8_t>> grid)
{
    // every time run this function must reset those obs Flags
    ResetObsFlags();
    int look_ahead_y = grid_height_ - ceil((look_ahead_dis_ - dead_zone_) / resolution_);
    int danger_y = grid_height_ - ceil((danger_dis_ - dead_zone_) / resolution_);

    for (int y = grid_height_ - 1; y >= danger_y; y--)
    {
        for (int x = car_right_line_x_ + 1; x < right_line_x_; x++)
        {
            danger_partial_grid_num_++;
            if (grid[y][x] == 1)
            {
                danger_partial_obs_num_++;
                if (y > danger_partial_obs_y_)
                {
                    danger_partial_obs_y_ = y;
                    danger_partial_obs_x_ = x;
                }
            }
        }
    }
    for (int y = grid_height_ - 1; y >= danger_y; y--)
    {
        for (int x = car_left_line_x_ + 1; x < car_right_line_x_; x++)
        {
            danger_grid_num_++;
            if (grid[y][x] == 1)
            {
                danger_obs_num_++;
                if (y > danger_obs_y_)
                {
                    danger_obs_y_ = y;
                    danger_obs_x_ = x;
                }
            }
        }
    }
    for (int y = danger_y - 1; y >= look_ahead_y; y--)
    {
        for (int x = car_left_line_x_ + 1; x < car_right_line_x_; x++)
        {
            forward_grid_num_++;
            if (grid[y][x] == 1)
            {
                forward_obs_num_++;
                if (y > forward_obs_y_)
                {
                    forward_obs_y_ = y;
                    forward_obs_x_ = x;
                }
            }
        }
    }
    for (int y = grid_height_ - 1; y >= look_ahead_y; y--)
    {
        for (int x = 0; x <= car_left_line_x_; x++)
        {
            left_grid_num_++;
            if (grid[y][x])
            {
                left_obs_num_++;
                if (y > left_obs_y_)
                {
                    left_obs_y_ = y;
                    left_obs_x_ = x;
                }
            }
        }
    }
    for (int y = grid_height_ - 1; y >= danger_y; y--)
    {
        for (int x = right_line_x_; x < grid_width_; x++)
        {
            if (x == right_line_x_)
            {
                inte_grid_num_++;
            }
            else
            {
                right_grid_num_++;
            }

            if (grid[y][x] == 1)
            {
                if (x == right_line_x_)
                {
                    inte_obs_num_++;
                    if (y > inte_obs_y_)
                    {
                        inte_obs_y_ = y;
                        inte_obs_x_ = x;
                    }
                }
                else
                {
                    right_obs_num_++;
                    if (y > right_obs_y_)
                    {
                        right_obs_y_ = y;
                        right_obs_x_ = x;
                    }
                }
            }
        }
    }
    if (forward_obs_num_ > 0)
    {
        obs_in_forward_ = true;
    }
    if (danger_obs_num_ > 0)
    {
        obs_in_danger_ = true;
    }
    if (right_obs_num_ > 0)
    {
        obs_in_right_ = true;
    }
    if (left_obs_num_ > 0)
    {
        obs_in_left_ = true;
    }
    if (float(inte_obs_num_) / float(inte_grid_num_) >= 0.1) // grid_num is between 4~6
    {
        obs_in_interested_ = true;
    }
}

void NavigationEdgeFollowAlg::GsAction(float &linear, float &angular)
{
    float gs_v_max = v_max_;
    if (obs_in_forward_)
    {
        gs_v_max /= 2.0; // 0.2m/s
    }
    linear = last_linear_ + acc_;
    linear = clamp(linear, float(0.0), gs_v_max);
    // calculate angular
    if (danger_partial_obs_num_ > 0)
    {
        // assert(danger_partial_obs_num_ < danger_partial_grid_num_);
        float err = float(danger_partial_obs_num_) / float(danger_partial_grid_num_);
        float obs_dis = std::hypot(danger_partial_obs_x_ - grid_width_ / 2, danger_partial_obs_y_ - grid_height_ + 1);
        angular = clamp(kp_l_ * err + kp_dis_ / (obs_dis + float(0.01)), float(0.0), float(0.25));
        LOG_ERROR_THROTTLE(1000, "%____TURN left angular____{}", angular);
    }
    else if (inte_obs_num_ < inte_grid_num_)
    {
        // assert(inte_obs_num_ < inte_grid_num_);
        float err = 1 - float(inte_obs_num_) / float(inte_grid_num_);
        angular = clamp(-kp_r_ * err, -kp_r_, float(0.0));
        LOG_ERROR_THROTTLE(1000, "%____TURN right angular____{}", angular);
    }
    else
    {
        angular = 0;
    }
    LOG_DEBUG_THROTTLE(1000, "NO obs in forward , linear:{} , angular:{}", linear, angular);
}
void NavigationEdgeFollowAlg::AoAction(float &linear, float &angular)
{
    if (ao_time_ > max_ao_time_)
    {
        linear = 0;
        angular = 0;
        PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_EDGE_FOLLOW_OBSTACLE_AVOIDANCE_FAILURES_EXCEPTION);
        LOG_ERROR_THROTTLE(1000, "Avoid obs time is bigger than max_ao_time,trap!!, linear:{} , angular:{}", linear, angular);
    }
    else
    {
        if (!ao_slow_complete_)
        {
            linear = last_linear_ - slow_acc_;
            angular = 0;
            if (linear < 0)
            {
                linear = 0;
                ao_slow_complete_ = true;
            }
        }
        else // finish slow down
        {
            if (ao_turn_l_spot_num_ < max_ao_turn_l_spot_num_)
            {
                linear = 0;
                if (danger_obs_x_ >= car_left_line_x_ / 3 + 2 * car_right_line_x_ / 3)
                {
                    angular = ao_turn_l_spot_angular_ / 2;
                }
                else
                {
                    angular = ao_turn_l_spot_angular_;
                }
                LOG_ERROR_THROTTLE(1000, "avoid obs spot turn left! linear = {},angular = {}", linear, angular);
                ao_turn_l_spot_num_++;
            }
            else if (ao_turn_l_num_ < max_ao_turn_l_num_)
            {
                // left wheel backward,right wheel farword in low speed
                linear = (ao_turn_l_wheel_r_ - ao_turn_l_wheel_l_) / 2;
                angular = (ao_turn_l_wheel_r_ + ao_turn_l_wheel_l_) / wheel_base_;
                LOG_ERROR_THROTTLE(1000, "avoid obs spot turn! linear = {},angular = {}", linear, angular);
                ao_turn_l_num_++;
            }
            else
            {
                linear = 0;
                angular = 0;
                ao_turn_l_spot_num_ = 0;
                ao_turn_l_num_ = 0;
                ao_slow_complete_ = false;
            }
        }
        ao_time_++;
        LOG_ERROR_THROTTLE(1000, "ao_time_:{}", ao_time_);
    }
}
// go straight then turn 90 degree
void NavigationEdgeFollowAlg::FlAction_sr(float &linear, float &angular)
{
    if (fl_time_ > max_fl_time_) // can't find line should go strange
    {
        linear = last_linear_ + acc_;
        if (linear > max_fl_v_)
        {
            linear = max_fl_v_;
        }
        LOG_ERROR_THROTTLE(1000, "can't find line should go strange! linear = {}", linear);
    }
    else
    {
        if (!start_find_line_)
        {
            start_find_line_ = true;
            find_line_t1_ = std::chrono::high_resolution_clock::now();
            linear = fl_go_straight_linear_;
            angular = 0;
            return;
        }
        std::chrono::duration<double> duration = std::chrono::high_resolution_clock::now() - find_line_t1_;
        float go_straight_dis = duration.count() * fl_go_straight_linear_;
        if (go_straight_dis < fl_forward_target_dis_)
        {
            linear = fl_go_straight_linear_;
            angular = 0;
            LOG_ERROR_THROTTLE(1000, "find line go straight");
        }
        else // slow down - turn backward-right
        {
            if (!fl_slow_complete_)
            {
                linear = last_linear_ - slow_acc_;
                angular = 0;
                if (linear < 0)
                {
                    linear = 0;
                    fl_slow_complete_ = true;
                }
            }
            else // finish slow down
            {
                if (!fl_back_right_complete_)
                {
                    if (!start_fl_back_right_)
                    {
                        start_fl_back_right_ = true;
                        find_line_t2_ = std::chrono::high_resolution_clock::now();
                        find_line_t2_time_out_ = std::chrono::high_resolution_clock::now();
                        // linear = fabs(wheel_v_ / 2);
                        // angular = -wheel_v_ / wheel_base_;
                        linear = 0;
                        angular = -fl_spot_turn_r_angular_;
                        return;
                    }
                    auto time_point = std::chrono::high_resolution_clock::now();
                    std::chrono::duration<double> duration_sr = time_point - find_line_t2_;
                    std::chrono::duration<double> duration_t2_time_out = time_point - find_line_t2_time_out_;
                    fl_back_right_dis_ = fl_back_right_dis_ + fabs(last_angular_) * duration_sr.count();
                    // LOG_ERROR("fl_back_right_dis_:{}", fl_back_right_dis_);
                    find_line_t2_ = time_point;
                    if (fl_back_right_dis_ >= fl_turn_right_target_ || duration_t2_time_out.count() >= fl_time_out_)
                    {
                        fl_back_right_complete_ = true;
                        angular = 0;
                        linear = 0;
                    }
                    else
                    {
                        // linear = fabs(wheel_v_ / 2);
                        // angular = -wheel_v_ / wheel_base_;
                        linear = 0;
                        angular = -fl_spot_turn_r_angular_;
                    }
                }
                else if (!fl_tuning_complete_)
                {
                    if (!start_fl_tuning_)
                    {
                        start_fl_tuning_ = true;
                        find_line_t3_ = std::chrono::high_resolution_clock::now();
                        find_line_t3_time_out_ = std::chrono::high_resolution_clock::now();
                        linear = -fl_tuning_back_linear_;
                        angular = -fl_spot_turn_r_angular_;
                        return;
                    }
                    auto time_point = std::chrono::high_resolution_clock::now();
                    std::chrono::duration<double> duration_tuning = time_point - find_line_t3_;
                    std::chrono::duration<double> duration_t3_time_out = time_point - find_line_t3_time_out_;
                    fl_tuning_dis_ = fl_tuning_dis_ + fabs(last_angular_) * duration_tuning.count();
                    LOG_ERROR_THROTTLE(1000, "tuning,tuning dis:{}", fl_tuning_dis_);
                    find_line_t3_ = time_point;
                    if (fl_tuning_dis_ >= fl_tuning_back_dis_target_ || duration_t3_time_out.count() >= fl_time_out_)
                    {
                        fl_tuning_complete_ = true;
                        start_fl_tuning_ = false;
                        angular = 0;
                        linear = 0;
                    }
                    else
                    {
                        linear = -fl_tuning_back_linear_;
                        angular = -fl_spot_turn_r_angular_;
                    }
                }
                else
                {
                    start_find_line_ = false;
                    start_fl_back_right_ = false;
                    start_fl_tuning_ = false;
                    fl_slow_complete_ = false;
                    fl_back_right_complete_ = false;
                    fl_tuning_complete_ = false;
                    fl_tuning_dis_ = 0;
                    fl_back_right_dis_ = 0;
                    fl_theta_ = 0;
                    fl_go_straight_angluar_ = 0;
                    fl_time_++;
                }
            }
        }
        LOG_ERROR_THROTTLE(1000, "fl_time_:{}", fl_time_);
    }
}
void NavigationEdgeFollowAlg::FlAction_br(float &linear, float &angular)
{
    if (fl_time_ > max_fl_time_) // can't find line should go strange
    {
        linear = last_linear_ + acc_;
        linear = clamp(linear, float(0.0), v_max_);
        LOG_ERROR_THROTTLE(1000, "can't find line should go strange! linear = {}", linear);
    }
    else
    {
        if (!start_find_line_)
        {
            start_find_line_ = true;
            find_line_t1_ = std::chrono::high_resolution_clock::now();
            linear = last_linear_ + acc_;
            linear = clamp(linear, float(0.0), fl_go_straight_linear_);
            angular = 0;
            return;
        }
        std::chrono::duration<double> duration = std::chrono::high_resolution_clock::now() - find_line_t1_;
        float go_straight_dis = duration.count() * fl_go_straight_linear_;
        if (go_straight_dis < fl_forward_target_dis_new_)
        {
            linear = last_linear_ + acc_;
            linear = clamp(linear, float(0.0), fl_go_straight_linear_);
            angular = 0;
            LOG_ERROR_THROTTLE(1000, "find line go straight,go_straight_dis:{}", go_straight_dis);
        }
        else // slow down - turn backward-right
        {
            if (!fl_slow_complete_)
            {
                linear = last_linear_ - slow_acc_;
                angular = 0;
                if (linear < 0)
                {
                    linear = 0;
                    fl_slow_complete_ = true;
                }
            }
            else // finish slow down
            {
                if (!fl_back_right_complete_)
                {
                    if (!start_fl_back_right_)
                    {
                        start_fl_back_right_ = true;
                        find_line_t2_ = std::chrono::high_resolution_clock::now();
                        linear = -fl_go_straight_linear_;
                        angular = -fl_turn_r_angular_new_;
                        return;
                    }
                    auto duration_t = std::chrono::high_resolution_clock::now();
                    std::chrono::duration<double> duration_br = duration_t - find_line_t2_;
                    fl_back_right_dis_ = fl_back_right_dis_ + fabs(last_angular_) * duration_br.count();
                    LOG_ERROR_THROTTLE(1000, "find line go straight,fl_back_right_dis_:{}", fl_back_right_dis_);
                    find_line_t2_ = duration_t;
                    if (fl_back_right_dis_ >= fl_turn_right_target_new_)
                    {
                        fl_back_right_complete_ = true;
                        angular = 0;
                        linear = 0;
                    }
                    else
                    {
                        linear = -fl_go_straight_linear_;
                        angular = -fl_turn_r_angular_new_;
                    }
                }
                else if (!fl_tuning_complete_) // reset it !!!
                {
                    // if (!start_fl_tuning_)
                    // {
                    //     start_fl_tuning_ = true;
                    //     find_line_t3_ = std::chrono::high_resolution_clock::now();
                    //     linear = fl_tuning_linear_;
                    //     angular = -fl_tuning_angular_;
                    //     return;
                    // }
                    // auto duration_t = std::chrono::high_resolution_clock::now();
                    // std::chrono::duration<double> duration_tuning = duration_t - find_line_t3_;
                    // fl_tuning_dis_ = fl_tuning_dis_ + fabs(last_linear_) * duration_tuning.count();
                    // LOG_ERROR_THROTTLE(1000, "tuning,tuning dis:{}", fl_tuning_dis_);
                    // find_line_t3_ = duration_t;
                    // if (fl_tuning_dis_ >= fl_tuning_target_)
                    // {
                    //     fl_tuning_complete_ = true;
                    //     angular = 0;
                    //     linear = 0;
                    // }
                    // else
                    // {
                    //     linear = fl_tuning_linear_;
                    //     angular = -fl_tuning_angular_;
                    // }
                    if (!fl_tuning_back_complete_)
                    {
                        if (!start_fl_tuning_)
                        {
                            start_fl_tuning_ = true;
                            find_line_t3_ = std::chrono::high_resolution_clock::now();
                            linear = -fl_tuning_back_linear_;
                            angular = 0;
                            return;
                        }
                        auto duration_t = std::chrono::high_resolution_clock::now();
                        std::chrono::duration<double> duration_tuning = duration_t - find_line_t3_;
                        fl_tuning_dis_ = fl_tuning_dis_ + fabs(last_linear_) * duration_tuning.count();
                        LOG_ERROR_THROTTLE(1000, "tuning,tuning dis:{}", fl_tuning_dis_);
                        find_line_t3_ = duration_t;
                        if (fl_tuning_dis_ >= fl_tuning_back_dis_target_)
                        {
                            fl_tuning_back_complete_ = true;
                            start_fl_tuning_ = false;
                            angular = 0;
                            linear = 0;
                        }
                        else
                        {
                            linear = -fl_tuning_back_linear_;
                            angular = 0;
                        }
                    }
                    else
                    {
                        if (!start_fl_tuning_)
                        {
                            start_fl_tuning_ = true;
                            find_line_t3_ = std::chrono::high_resolution_clock::now();
                            linear = 0;
                            angular = -fl_tuning_angular_;
                            return;
                        }
                        auto duration_t = std::chrono::high_resolution_clock::now();
                        std::chrono::duration<double> duration_tuning = duration_t - find_line_t3_;
                        fl_tuning_angle_ = fl_tuning_angle_ + fabs(last_angular_) * duration_tuning.count();
                        LOG_ERROR_THROTTLE(1000, "tuning,tuning dis:{}", fl_tuning_angle_);
                        find_line_t3_ = duration_t;
                        if (fl_tuning_angle_ >= fl_tuning_angle_target_)
                        {
                            fl_tuning_complete_ = true;
                            angular = 0;
                            linear = 0;
                        }
                        else
                        {
                            linear = 0;
                            angular = -fl_tuning_angular_;
                        }
                    }
                }
                else
                {
                    start_find_line_ = false;
                    start_fl_back_right_ = false;
                    start_fl_tuning_ = false;
                    fl_slow_complete_ = false;
                    fl_back_right_complete_ = false;
                    fl_tuning_complete_ = false;
                    fl_back_right_dis_ = 0;
                    fl_tuning_dis_ = 0;
                    fl_theta_ = 0;
                    fl_tuning_angle_ = 0;
                    fl_time_++;
                }
            }
        }
        LOG_ERROR_THROTTLE(1000, "fl_time_:{}", fl_time_);
    }
}
EdgeFollowAlgResult NavigationEdgeFollowAlg::DealEdgeFollowWithBEV(const PerceptionFusionResult &fusion_result, const OdomResult &odom_result, const ob_mower_msgs::NavEscapeResult &escape_result)
{
    auto start = std::chrono::high_resolution_clock::now();
    float linear = 0;
    float angular = 0;
    grid_width_ = fusion_result.occupancy_grid.width;
    grid_height_ = fusion_result.occupancy_grid.height;
    resolution_ = fusion_result.occupancy_grid.resolution;
    LOG_ERROR_THROTTLE(1000, "odom_result:{}", odom_result.angular);
    CheckGrid(fusion_result.occupancy_grid.grid);
    if (stop_escape_)
    {
        // stop escape
        PublishNavAlgCtrl(true);
        // mark time point
        if (!mark_escape_time_)
        {
            stop_escape_t_ = std::chrono::high_resolution_clock::now();
            mark_escape_time_ = true;
        }
        std::chrono::duration<double> escape_duration = std::chrono::high_resolution_clock::now() - stop_escape_t_;
        if (escape_duration.count() >= escape_cool_time_)
        {
            stop_escape_ = false;
            mark_escape_time_ = false;
        }
    }
    else
    {
        PublishNavAlgCtrl(false);
        CheckLoop(escape_result);
    }

    LOG_ERROR_THROTTLE(1000, "Exec_state_:{},obs_in_danger:{},obs_in_right_:{},obs_in_interested:{},is_escape:{}", exec_state_, obs_in_danger_, obs_in_right_, obs_in_interested_, is_escape_);
    switch (exec_state_)
    {
    case FIND_LINE:
    {
        if (is_escape_)
        {
            ChangeState(ESCAPE, "STATE");
            linear = last_linear_ - slow_acc_;
            if (linear < 0)
            {
                linear = 0;
            }
            angular = 0;
            stop_escape_ = true;
        }
        else if (obs_in_danger_)
        {
            ChangeState(AVOID_OBS, "STATE");
            // slow down
            linear = last_linear_ - slow_acc_;
            if (linear < 0)
            {
                linear = 0;
            }
            angular = 0;
        }
        else if (obs_in_interested_ || obs_in_right_)
        {
            ChangeState(GO_STRAIGHT, "STATE");
            linear = last_linear_ - slow_acc_;
            if (linear < 0)
            {
                linear = 0;
            }
            angular = 0;
        }
        else // find line action
        {
            if (flAction_type_ == "straight_right")
            {
                FlAction_sr(linear, angular);
            }
            else if (flAction_type_ == "back_right")
            {
                FlAction_br(linear, angular);
            }
        }
        break;
    }
    case GO_STRAIGHT:
    {
        if (is_escape_)
        {
            ChangeState(ESCAPE, "STATE");
            linear = last_linear_ - slow_acc_;
            if (linear < 0)
            {
                linear = 0;
            }
            angular = 0;
            stop_escape_ = true;
        }
        else if (obs_in_danger_)
        {
            ChangeState(AVOID_OBS, "STATE");
            linear = last_linear_ - slow_acc_;
            if (linear < 0)
            {
                linear = 0;
            }
            angular = 0;
        }
        else if (!obs_in_interested_ && !obs_in_right_)
        {
            ChangeState(FIND_LINE, "STATE");
            linear = last_linear_ - slow_acc_;
            if (linear < 0)
            {
                linear = 0;
            }
            angular = 0;
        }
        else
        {
            GsAction(linear, angular);
        }
        break;
    }
    case AVOID_OBS:
    {
        if (!obs_in_danger_ && (obs_in_interested_ || obs_in_right_))
        {
            ChangeState(GO_STRAIGHT, "STATE");
            linear = last_linear_ + acc_; // whether + slow_acc_ is better?
            if (linear < 0)
            {
                linear = 0;
            }
            angular = 0;
        }
        else if (!obs_in_danger_ && !obs_in_interested_ && !obs_in_right_)
        {
            ChangeState(FIND_LINE, "STATE");
            linear = last_linear_ - slow_acc_;
            if (linear < 0)
            {
                linear = 0;
            }
            angular = 0;
        }
        else
        {
            AoAction(linear, angular);
        }
        break;
    }
    case ESCAPE:
    {
        if (obs_in_danger_)
        {
            ChangeState(AVOID_OBS, "STATE");
            linear = last_linear_ - slow_acc_;
            if (linear < 0)
            {
                linear = 0;
            }
            angular = 0;
        }
        else
        {
            EsAction(linear, angular);
        }
    }
    }
    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double> duration = end - start;
    LOG_DEBUG_THROTTLE(10000, "OCCUPANCY FOLLOW cost {} second,dead zone : {}", duration.count(), dead_zone_);
    last_linear_ = linear;
    last_angular_ = odom_result.angular;
    // last_angular_ = angular;
    LOG_DEBUG("***linear={},angular={}****", linear, angular);
    return EdgeFollowAlgResult(linear, angular);
}

#endif

void NavigationEdgeFollowAlg::EsAction(float &linear, float &angular)
{
    if (!escape_rotation_complete_)
    {
        // whether should add slow down?
        if (!escape_rotation_start_)
        {
            escape_rotation_start_ = true;
            escape_rotation_t_ = std::chrono::high_resolution_clock::now();
            escape_rotation_time_out_ = std::chrono::high_resolution_clock::now();
            escape_rotation_angle_ = 0;
            linear = 0;
            angular = ao_turn_l_spot_angular_;
            return;
        }
        auto t_now = std::chrono::high_resolution_clock::now();
        std::chrono::duration<double> duration_rotation = t_now - escape_rotation_t_;
        std::chrono::duration<double> duration_time_out = t_now - escape_rotation_time_out_;
        escape_rotation_angle_ += last_angular_ * duration_rotation.count();
        escape_rotation_t_ = t_now;
        if (escape_rotation_angle_ >= escape_rotation_target_ || duration_time_out.count() >= es_time_out_)
        {
            escape_rotation_complete_ = true;
            linear = 0;
            angular = 0;
        }
        else
        {
            linear = 0;
            angular = ao_turn_l_spot_angular_;
        }
    }
    else
    {
        linear = max_fl_v_;
        angular = 0;
    }
    LOG_ERROR("escape_rotation_angle_:{}", escape_rotation_angle_);
    LOG_ERROR_THROTTLE(1000, "Escape!!");
}
void NavigationEdgeFollowAlg::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_exception_ = std::make_unique<iox_exception_publisher>(
        iox::capro::ServiceDescription{kSocExceptionIox[0],
                                       kSocExceptionIox[1],
                                       kSocExceptionIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
    pub_nav_alg_ctrl_ = std::make_unique<iox_nav_alg_ctrl_publisher>(
        iox::capro::ServiceDescription{kNavigationNavAlgCtrlIox[0],
                                       kNavigationNavAlgCtrlIox[1],
                                       kNavigationNavAlgCtrlIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
}

void NavigationEdgeFollowAlg::PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.node_name = "navigation_edge_follow_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception)
            .or_else([](auto &error) {
                LOG_ERROR("Navigation edge_follow publish soc exception Unable to publishCopyOf, error: {}", error);
            });
    }
}

void NavigationEdgeFollowAlg::PublishNavAlgCtrl(bool stop)
{
    if (pub_nav_alg_ctrl_)
    {
        fescue_msgs__msg__NavigationAlgoCtrlData ctrl_data;
        fescue_msgs__msg__NavigationAlgoCtrlInfo info;
        info.type = FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_ESCAPE;
        if (stop)
        {
            info.state = FESCUE_MSGS_ENUM_NAV_ALGO_STATE_DISABLE;
        }
        else
        {
            info.state = FESCUE_MSGS_ENUM_NAV_ALGO_STATE_ENABLE;
        }

        ctrl_data.sender = "edge_follow";
        ctrl_data.data.push_back(info);
        pub_nav_alg_ctrl_->publishCopyOf(ctrl_data)
            .or_else([](auto &error) {
                LOG_ERROR("Navigation edge_follow publish escape stop Unable to publishCopyOf, error: {}", error);
            });
    }
}

} // namespace fescue_iox
