#include "escape_config.hpp"

#include <sstream>

namespace fescue_iox
{

NavigationEscapeAlgConfig &NavigationEscapeAlgConfig::operator=(const NavigationEscapeAlgConfig &config)
{
    if (this != &config)
    {
    }

    return *this;
}

std::string NavigationEscapeAlgConfig::toString() const
{
    std::stringstream ss;
    ss << "------------------------- " << typeid(*this).name() << " -------------------------\r\n";
    // ss << "  spiral_rotate_angle: " << spiral_rotate_angle << "\n";
    // ss << "  forward_speed: " << forward_speed << "\n";
    // ss << "  trun_corner_speed_min: " << trun_corner_speed_min << "\n";
    // ss << "  angular_accuracy: " << angular_accuracy << "\n";
    ss << "------------------------- " << typeid(*this).name() << " -------------------------";
    return ss.str();
}

bool operator==(const NavigationEscapeAlgConfig &lhs, const NavigationEscapeAlgConfig &rhs)
{
    (void)lhs;
    (void)rhs;
    // return lhs.spiral_rotate_angle == rhs.spiral_rotate_angle &&
    //        lhs.forward_speed == rhs.forward_speed &&
    //        lhs.trun_corner_speed_min == rhs.trun_corner_speed_min &&
    //        lhs.angular_accuracy == rhs.angular_accuracy;
    return true;
}

bool operator!=(const NavigationEscapeAlgConfig &lhs, const NavigationEscapeAlgConfig &rhs)
{
    return !(lhs == rhs);
}

template <>
bool Config<NavigationEscapeAlgConfig>::LoadConfig(NavigationEscapeAlgConfig &config, const std::string &conf_file)
{
    (void)config;
    YAML::Node node;
    try
    {
        node = YAML::LoadFile(conf_file);
    }
    catch (const YAML::BadFile &e)
    {
        std::cerr << "NavigationEscapeAlgConfig load config fail (BadFile): " << e.what() << '\n';
        return false;
    }
    catch (const YAML::ParserException &e)
    {
        std::cerr << "NavigationEscapeAlgConfig load config fail (ParserException): " << e.what() << '\n';
        return false;
    }
    catch (const std::exception &e)
    {
        std::cerr << "NavigationEscapeAlgConfig load config fail (Unknown): " << e.what() << '\n';
        return false;
    }

    // config.spiral_rotate_angle = GetYamlValue<int>(node, "spiral_rotate_angle", config.spiral_rotate_angle);
    // config.forward_speed = GetYamlValue<float>(node, "forward_speed", config.forward_speed);
    // config.trun_corner_speed_min = GetYamlValue<float>(node, "trun_corner_speed_min", config.trun_corner_speed_min);
    // config.angular_accuracy = GetYamlValue<int>(node, "angular_accuracy", config.angular_accuracy);
    return true;
}

template <>
bool Config<NavigationEscapeAlgConfig>::CreateConfig(const NavigationEscapeAlgConfig &conf, const std::string &conf_file)
{
    (void)conf;
    YAML::Node node;
    // node["spiral_rotate_angle"] = conf.spiral_rotate_angle;
    // node["forward_speed"] = conf.forward_speed;
    // node["trun_corner_speed_min"] = conf.trun_corner_speed_min;
    // node["angular_accuracy"] = conf.angular_accuracy;
    return WriteYamlFile(conf_file, node);
}

} // namespace fescue_iox
