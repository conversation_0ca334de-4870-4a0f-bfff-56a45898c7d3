#include "recharge_config.hpp"

namespace fescue_iox
{

NavigationRechargeAlgConfig &NavigationRechargeAlgConfig::operator=(const NavigationRechargeAlgConfig &config)
{
    if (this != &config)
    {
        try_max_num = config.try_max_num;
        no_qr_max_num = config.no_qr_max_num;
        save_data_num = config.save_data_num;
        save_terminal_num = config.save_terminal_num;
        head_center_min_dist = config.head_center_min_dist;
        head_center_max_dist = config.head_center_max_dist;
        station_qr_direction_min_dist = config.station_qr_direction_min_dist;

        qr_code_clear_angle = config.qr_code_clear_angle;
        qr_code_detect_angle_1 = config.qr_code_detect_angle_1;
        qr_code_min_distance = config.qr_code_min_distance;
        start_recharge_distance = config.start_recharge_distance;
        qr_code_x_min_dist = config.qr_code_x_min_dist;
        qr_code_y_min_dist = config.qr_code_y_min_dist;
        circle_r_dist = config.circle_r_dist;
        explore_distance = config.explore_distance;
        explore_vel = config.explore_vel;
        kp_y = config.kp_y;
        kp_yaw = config.kp_yaw;
        kp_perception = config.kp_perception;
        recharge_adjust_linear = config.recharge_adjust_linear;
        recharge_pile_linear = config.recharge_pile_linear;
        recharge_pile_angular = config.recharge_pile_angular;
    }

    return *this;
}

std::string NavigationRechargeAlgConfig::toString() const
{
    std::stringstream ss;
    ss << "------------------------- " << typeid(*this).name() << " -------------------------\r\n";
    ss << "  try_max_num: " << try_max_num << "\n";
    ss << "  no_qr_max_num: " << no_qr_max_num << "\n";
    ss << "  save_data_num: " << save_data_num << "\n";
    ss << "  save_terminal_num: " << save_terminal_num << "\n";
    ss << "  head_center_min_dist: " << head_center_min_dist << "\n";
    ss << "  head_center_max_dist: " << head_center_max_dist << "\n";
    ss << "  station_qr_direction_min_dist: " << station_qr_direction_min_dist << "\n";
    ss << "  qr_code_clear_angle: " << qr_code_clear_angle << "\n";
    ss << "  qr_code_detect_angle_1: " << qr_code_detect_angle_1 << "\n";
    ss << "  qr_code_min_distance: " << qr_code_min_distance << "\n";
    ss << "  start_recharge_distance: " << start_recharge_distance << "\n";
    ss << "  qr_code_x_min_dist: " << qr_code_x_min_dist << "\n";
    ss << "  qr_code_y_min_dist: " << qr_code_y_min_dist << "\n";
    ss << "  circle_r_dist: " << circle_r_dist << "\n";
    ss << "  explore_distance: " << explore_distance << "\n";
    ss << "  explore_vel: " << explore_vel << "\n";
    ss << "  kp_y: " << kp_y << "\n";
    ss << "  kp_yaw: " << kp_yaw << "\n";
    ss << "  kp_perception: " << kp_perception << "\n";
    ss << "  recharge_adjust_linear: " << recharge_adjust_linear << "\n";
    ss << "  recharge_pile_linear: " << recharge_pile_linear << "\n";
    ss << "  recharge_pile_angular: " << recharge_pile_angular << "\n";
    ss << "------------------------- " << typeid(*this).name() << " -------------------------";

    return ss.str();
}

bool operator==(const NavigationRechargeAlgConfig &lhs, const NavigationRechargeAlgConfig &rhs)
{
    return lhs.try_max_num == rhs.try_max_num &&
           lhs.no_qr_max_num == rhs.no_qr_max_num &&
           lhs.save_data_num == rhs.save_data_num &&
           lhs.save_terminal_num == rhs.save_terminal_num &&
           lhs.head_center_min_dist == rhs.head_center_min_dist &&
           lhs.head_center_max_dist == rhs.head_center_max_dist &&
           lhs.station_qr_direction_min_dist == rhs.station_qr_direction_min_dist &&
           lhs.qr_code_clear_angle == rhs.qr_code_clear_angle &&
           lhs.qr_code_detect_angle_1 == rhs.qr_code_detect_angle_1 &&
           lhs.qr_code_min_distance == rhs.qr_code_min_distance &&
           lhs.start_recharge_distance == rhs.start_recharge_distance &&
           lhs.qr_code_x_min_dist == rhs.qr_code_x_min_dist &&
           lhs.qr_code_y_min_dist == rhs.qr_code_y_min_dist &&
           lhs.circle_r_dist == rhs.circle_r_dist &&
           lhs.explore_distance == rhs.explore_distance &&
           lhs.explore_vel == rhs.explore_vel &&
           lhs.kp_y == rhs.kp_y &&
           lhs.kp_yaw == rhs.kp_yaw &&
           lhs.kp_perception == rhs.kp_perception &&
           lhs.recharge_adjust_linear == rhs.recharge_adjust_linear &&
           lhs.recharge_pile_linear == rhs.recharge_pile_linear &&
           lhs.recharge_pile_angular == rhs.recharge_pile_angular;
}

bool operator!=(const NavigationRechargeAlgConfig &lhs, const NavigationRechargeAlgConfig &rhs)
{
    return !(lhs == rhs);
}

template <>
bool Config<NavigationRechargeAlgConfig>::LoadConfig(NavigationRechargeAlgConfig &config, const std::string &conf_file)
{
    YAML::Node node;
    try
    {
        node = YAML::LoadFile(conf_file);
    }
    catch (const YAML::BadFile &e)
    {
        std::cerr << "NavigationRechargeAlgConfig load config fail (BadFile): " << e.what() << '\n';
        return false;
    }
    catch (const YAML::ParserException &e)
    {
        std::cerr << "NavigationRechargeAlgConfig load config fail (ParserException): " << e.what() << '\n';
        return false;
    }
    catch (const std::exception &e)
    {
        std::cerr << "NavigationRechargeAlgConfig load config fail (Unknown): " << e.what() << '\n';
        return false;
    }

    config.try_max_num = GetYamlValue<int>(node, "try_max_num", config.try_max_num);
    config.no_qr_max_num = GetYamlValue<int>(node, "no_qr_max_num", config.no_qr_max_num);
    config.save_data_num = GetYamlValue<int>(node, "save_data_num", config.save_data_num);
    config.save_terminal_num = GetYamlValue<int>(node, "save_terminal_num", config.save_terminal_num);
    config.head_center_min_dist = GetYamlValue<int>(node, "head_center_min_dist", config.head_center_min_dist);
    config.head_center_max_dist = GetYamlValue<int>(node, "head_center_max_dist", config.head_center_max_dist);
    config.station_qr_direction_min_dist = GetYamlValue<int>(node, "station_qr_direction_min_dist", config.station_qr_direction_min_dist);
    config.qr_code_clear_angle = GetYamlValue<float>(node, "qr_code_clear_angle", config.qr_code_clear_angle);
    config.qr_code_detect_angle_1 = GetYamlValue<float>(node, "qr_code_detect_angle_1", config.qr_code_detect_angle_1);
    config.qr_code_min_distance = GetYamlValue<float>(node, "qr_code_min_distance", config.qr_code_min_distance);
    config.start_recharge_distance = GetYamlValue<float>(node, "start_recharge_distance", config.start_recharge_distance);
    config.qr_code_x_min_dist = GetYamlValue<float>(node, "qr_code_x_min_dist", config.qr_code_x_min_dist);
    config.qr_code_y_min_dist = GetYamlValue<float>(node, "qr_code_y_min_dist", config.qr_code_y_min_dist);
    config.circle_r_dist = GetYamlValue<float>(node, "circle_r_dist", config.circle_r_dist);
    config.explore_distance = GetYamlValue<float>(node, "explore_distance", config.explore_distance);
    config.explore_vel = GetYamlValue<float>(node, "explore_vel", config.explore_vel);
    config.kp_y = GetYamlValue<float>(node, "kp_y", config.kp_y);
    config.kp_yaw = GetYamlValue<float>(node, "kp_yaw", config.kp_yaw);
    config.kp_perception = GetYamlValue<float>(node, "kp_perception", config.kp_perception);
    config.recharge_adjust_linear = GetYamlValue<float>(node, "recharge_adjust_linear", config.recharge_adjust_linear);
    config.recharge_pile_linear = GetYamlValue<float>(node, "recharge_pile_linear", config.recharge_pile_linear);
    config.recharge_pile_angular = GetYamlValue<float>(node, "recharge_pile_angular", config.recharge_pile_angular);
    return true;
}

template <>
bool Config<NavigationRechargeAlgConfig>::CreateConfig(const NavigationRechargeAlgConfig &conf, const std::string &conf_file)
{
    YAML::Node node;
    node["try_max_num"] = conf.try_max_num;
    node["no_qr_max_num"] = conf.no_qr_max_num;
    node["save_data_num"] = conf.save_data_num;
    node["save_terminal_num"] = conf.save_terminal_num;
    node["head_center_min_dist"] = conf.head_center_min_dist;
    node["head_center_max_dist"] = conf.head_center_max_dist;
    node["station_qr_direction_min_dist"] = conf.station_qr_direction_min_dist;
    node["qr_code_clear_angle"] = conf.qr_code_clear_angle;
    node["qr_code_detect_angle_1"] = conf.qr_code_detect_angle_1;
    node["qr_code_min_distance"] = conf.qr_code_min_distance;
    node["start_recharge_distance"] = conf.start_recharge_distance;
    node["qr_code_x_min_dist"] = conf.qr_code_x_min_dist;
    node["qr_code_y_min_dist"] = conf.qr_code_y_min_dist;
    node["qr_code_min_distance"] = conf.qr_code_min_distance;
    node["circle_r_dist"] = conf.circle_r_dist;
    node["explore_distance"] = conf.explore_distance;
    node["explore_vel"] = conf.explore_vel;
    node["kp_y"] = conf.kp_y;
    node["kp_yaw"] = conf.kp_yaw;
    node["kp_perception"] = conf.kp_perception;
    node["recharge_adjust_linear"] = conf.recharge_adjust_linear;
    node["recharge_pile_linear"] = conf.recharge_pile_linear;
    node["recharge_pile_angular"] = conf.recharge_pile_angular;
    return WriteYamlFile(conf_file, node);
}

} // namespace fescue_iox
