#include "calibration_bev_node.hpp"

#include "calibration_bev_node_config.hpp"
#include "mower_sdk_version.h"
#include "opencv2/opencv.hpp"
#include "utils/dir.hpp"
#include "utils/file.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <cmath>      // for std::labs()
#include <filesystem> //c++17

namespace fescue_iox
{

CalibrationBevNode::CalibrationBevNode(const std::string &node_name)
    : node_name_(node_name)
{
    InitWorkingDirectory();
    InitParam();
    InitLogger();
    InitAlgorithm();
    InitSubscriber();
    InitService();
    InitHeartbeat();
}

CalibrationBevNode::~CalibrationBevNode()
{
    LOG_WARN("CalibrationBevNode stop success!");
}

void CalibrationBevNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void CalibrationBevNode::InitParam()
{
    const std::string conf_file{"conf/calibration_bev_node/calibration_bev_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("CalibrationBevNode create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("CalibrationBevNode create config path failed!!!");
        }
    }
    if (!Config<CalibrationBevNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init CalibrationBevNode config parameters failed!");
    }
    CalibrationBevNodeConfig config = Config<CalibrationBevNodeConfig>::GetConfig();
    LOG_INFO("[calibration_bev_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[calibration_bev_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[calibration_bev_node] compile time: {}", _COMPILE_TIME_);
    LOG_INFO("{}", config.toString().c_str());

    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    calibration_alg_conf_file_ = config.calibration_alg_conf_file;
    compress_quality_ = config.compress_quality;
    compress_type_ = config.compress_type;

    if (!Config<CalibrationBevNodeConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set CalibrationBevNode config parameters failed!");
    }

    CreateDirectories(log_dir_);
}

void CalibrationBevNode::InitLogger()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void CalibrationBevNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

void CalibrationBevNode::InitAlgorithm()
{
    calibration_bev_alg_ = std::make_unique<CalibrationBevAlg>(calibration_alg_conf_file_);
}

void CalibrationBevNode::InitSubscriber()
{
    sub_1280x720_image_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "camera_color_1280x720_result", 1, [this](const sensor_msgs__msg__Image_iox &data, const std::string &event) {
            (void)event;
            Deal1280x720Image(data);
        });
}

void CalibrationBevNode::Deal1280x720Image(const sensor_msgs__msg__Image_iox &img)
{
    std::unique_lock<std::mutex> lck(mutex_);
    if (cali_start_)
    {
        NV12ToGrayMat(cali_image_, img.data.data(), img.width, img.height);
        cali_start_ = false;
        cv_.notify_all();
    }
}

void CalibrationBevNode::InitService()
{
    service_get_node_param_ = std::make_unique<IceoryxServerMower<get_node_param_request, get_node_param_response>>(
        "get_calibration_node_param", 10U, [this](const get_node_param_request &request, get_node_param_response &response) {
            (void)request;
            response.success = GetNodeParam(response.data);
            LOG_INFO("Get calibration bev node param execute {}", response.success);
        });
    service_set_node_param_ = std::make_unique<IceoryxServerMower<set_node_param_request, set_node_param_response>>(
        "set_calibration_node_param", 10U, [this](const set_node_param_request &request, set_node_param_response &response) {
            response.success = SetNodeParam(request.data);
            LOG_INFO("Set calibration bev node param execute {}", response.success);
        });
    service_calibration_bev_ = std::make_unique<IceoryxServerMower<mower_msgs::srv::CalibrationBevRequest, mower_msgs::srv::CalibrationBevResponse>>(
        "set_calibration_bev", 10U, [this](const mower_msgs::srv::CalibrationBevRequest &request, mower_msgs::srv::CalibrationBevResponse &response) {
            (void)request;
            response.success = ExecuteCalibrationBevRequest(response.param, response.image);
            response.timestamp = GetTimestampMs();
            LOG_INFO("Set calibration bev request execute {}", response.success);
        });
}

void CalibrationBevNode::EncodeImage(const cv::Mat &image, mower_msgs::srv::CalibrationBevImage &img)
{
    std::vector<uchar> compress_data;
    std::vector<int> compression_params = {compress_type_ == "png" ? cv::IMWRITE_PNG_COMPRESSION : cv::IMWRITE_JPEG_QUALITY, compress_quality_}; // 设置JPEG压缩质量（0-100）
    std::string encode_type = (compress_type_ == "png" ? ".png" : ".jpg");
    cv::imencode(encode_type, image, compress_data, compression_params);
    size_t img_size = (compress_data.size() > mower_msgs::srv::BEV_IMAGE_DATA_MAX) ? mower_msgs::srv::BEV_IMAGE_DATA_MAX : compress_data.size();
    img.size = img_size;
    img.width = image.cols;
    img.height = image.rows;
    img.channel = image.channels();
    img.type = (compress_type_ == "png" ? 1 : 0);
    img.data.resize(img_size);
    memcpy(img.data.data(), compress_data.data(), img_size);
}

int CalibrationBevNode::ExecuteCalibrationBevRequest(mower_msgs::srv::BEVCalibParams &param, mower_msgs::srv::CalibrationBevImage &img)
{
    RGBCameraIntrinsic cam_intrinsics;
    BEVCalibParams bev_params;

    if (!calibration_bev_alg_)
    {
        LOG_ERROR("Calibration algorithm not initialized!");
        return CALIB_ERR_NO_ALG;
    }

    {
        std::unique_lock<std::mutex> lock(mutex_);
        cali_image_.release();
        cali_start_ = true;
        bool wait_result = cv_.wait_for(lock, CALIB_WAIT_TIMEOUT, [this] { return !cali_start_; });
        if (!wait_result)
        {
            LOG_ERROR("CalibrationBevNode wait for image timeout!");
            cali_start_ = false;
            return CALIB_ERR_TIMEOUT;
        }

        if (cali_image_.empty())
        {
            LOG_ERROR("CalibrationBevNode get image fail!");
            return CALIB_ERR_NO_IMAGE;
        }
    }

    cv::Mat img_cali, img_encode;
    {
        std::lock_guard<std::mutex> lock(mutex_);
        img_cali = cali_image_.clone();
        img_encode = cali_image_.clone();
    }

    EncodeImage(img_encode, img);

    // 获取摄像头内参
    if (!GetCameraIntrinsicsParamFromCameraNode(cam_intrinsics))
    {
        LOG_ERROR("CalibrationBevNode get intrinsics param fail!");
        return CALIB_ERR_INTRINSIC;
    }

    // 执行标定
    int ret = calibration_bev_alg_->DealCalibrationBev(img_cali, cam_intrinsics, bev_params);
    if (0 != ret)
    {
        LOG_ERROR("CalibrationBevNode execute calibration algo fail!");
        return ret;
    }

    // 将标定结果发送到camera写 eeprom
    if (!SendCalibrationBevResultToCameraNode(bev_params))
    {
        LOG_ERROR("CalibrationBevNode send calibration result to eeprom fail!");
        return CALIB_ERR_SEND_RESULT;
    }

    // param 返回给请求方
    param.img_width_ = bev_params.img_width_;
    param.img_height_ = bev_params.img_height_;
    param.scotoma_distance_ = bev_params.scotoma_distance_;
    param.bev_physical_width_ = bev_params.bev_physical_width_;
    param.bev_physical_lenght_ = bev_params.bev_physical_length_;
    param.top_left_pt_x = bev_params.top_left_pt_x_;
    param.top_left_pt_y = bev_params.top_left_pt_y_;
    param.bottom_left_pt_x = bev_params.bottom_left_pt_x_;
    param.bottom_left_pt_y = bev_params.bottom_left_pt_y_;
    param.top_right_pt_x = bev_params.top_right_pt_x_;
    param.top_right_pt_y = bev_params.top_right_pt_y_;
    param.bottom_right_pt_x = bev_params.bottom_right_pt_x_;
    param.bottom_right_pt_y = bev_params.bottom_right_pt_y_;

    LOG_INFO("CalibrationBevNode calibration success!");
    return CALIB_SUCCESS;
}

bool CalibrationBevNode::GetNodeParam(NodeParamData &data)
{
    CalibrationBevNodeConfig config = Config<CalibrationBevNodeConfig>::GetConfig();
    data.console_log_level.unsafe_assign(config.common_conf.console_log_level.c_str());
    data.file_log_level.unsafe_assign(config.common_conf.file_log_level.c_str());
    return true;
}

bool CalibrationBevNode::SetNodeParam(const NodeParamData &data)
{
    console_log_level_ = std::string(data.console_log_level.c_str());
    file_log_level_ = std::string(data.file_log_level.c_str());
    InitLogger();
    CalibrationBevNodeConfig config = Config<CalibrationBevNodeConfig>::GetConfig();
    config.common_conf.console_log_level = console_log_level_;
    config.common_conf.file_log_level = file_log_level_;
    Config<CalibrationBevNodeConfig>::SetConfig(config);
    LOG_INFO("New CalibrationBevNode params: {}", config.toString().c_str());
    return true;
}

bool CalibrationBevNode::GetCameraIntrinsicsParamFromCameraNode(RGBCameraIntrinsic &param)
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::CameraIntrinsicRequest,
                                                      mower_msgs::srv::CameraIntrinsicResponse>>("get_union_rgb_camera_intrinsic");

    auto response_handler = [](const mower_msgs::srv::CameraIntrinsicResponse &response_receive,
                               mower_msgs::srv::CameraIntrinsicResponse &response_output) -> bool {
        response_output = response_receive;
        return response_output.success;
    };

    mower_msgs::srv::CameraIntrinsicRequest request_input;
    mower_msgs::srv::CameraIntrinsicResponse response_output;
    if (!client->SendRequest(request_input, response_output, nullptr, response_handler))
    {
        return false;
    }

    param.model_ = response_output.camera_intrinsic.model_;
    param.img_width_ = response_output.camera_intrinsic.img_width_;
    param.img_height_ = response_output.camera_intrinsic.img_height_;
    param.focal_x_ = response_output.camera_intrinsic.focal_x_;
    param.focal_y_ = response_output.camera_intrinsic.focal_y_;
    param.cx_ = response_output.camera_intrinsic.cx_;
    param.cy_ = response_output.camera_intrinsic.cy_;
    param.k1_ = response_output.camera_intrinsic.k1_;
    param.k2_ = response_output.camera_intrinsic.k2_;
    param.k3_ = response_output.camera_intrinsic.k3_;
    param.k4_ = response_output.camera_intrinsic.k4_;
    if (param.model_ == 0) // K6
    {
        param.k5_ = response_output.camera_intrinsic.k5_;
        param.k6_ = response_output.camera_intrinsic.k6_;
        param.p1_ = response_output.camera_intrinsic.p1_;
        param.p2_ = response_output.camera_intrinsic.p2_;
    }
    else // KB
    {
        param.k5_ = 0.0;
        param.k6_ = 0.0;
        param.p1_ = 0.0;
        param.p2_ = 0.0;
    }
    return true;
}

bool CalibrationBevNode::SendCalibrationBevResultToCameraNode(BEVCalibParams &param)
{
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::CalibrationBevResultRequest,
                                                      mower_msgs::srv::CalibrationBevResultResponse>>("set_union_rgb_camera_bev_params");
    auto request_handler = [](const mower_msgs::srv::CalibrationBevResultRequest &request_input,
                              mower_msgs::srv::CalibrationBevResultRequest &request_send) -> void {
        request_send = request_input;
    };

    auto response_handler = [](const mower_msgs::srv::CalibrationBevResultResponse &response_receive,
                               mower_msgs::srv::CalibrationBevResultResponse &response_output) -> bool {
        response_output = response_receive;
        return response_output.success;
    };

    mower_msgs::srv::CalibrationBevResultRequest request_input;
    request_input.param.img_width_ = param.img_width_;
    request_input.param.img_height_ = param.img_height_;
    request_input.param.scotoma_distance_ = param.scotoma_distance_;
    request_input.param.bev_physical_width_ = param.bev_physical_width_;
    request_input.param.bev_physical_lenght_ = param.bev_physical_length_;
    request_input.param.top_left_pt_x = param.top_left_pt_x_;
    request_input.param.top_left_pt_y = param.top_left_pt_y_;
    request_input.param.bottom_left_pt_x = param.bottom_left_pt_x_;
    request_input.param.bottom_left_pt_y = param.bottom_left_pt_y_;
    request_input.param.top_right_pt_x = param.top_right_pt_x_;
    request_input.param.top_right_pt_y = param.top_right_pt_y_;
    request_input.param.bottom_right_pt_x = param.bottom_right_pt_x_;
    request_input.param.bottom_right_pt_y = param.bottom_right_pt_y_;
    mower_msgs::srv::CalibrationBevResultResponse response_output;
    return client->SendRequest(request_input, response_output, request_handler, response_handler);
}

} // namespace fescue_iox
