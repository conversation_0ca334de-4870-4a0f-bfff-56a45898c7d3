#pragma once

#include "Eigen/Dense"

namespace fescue_iox
{

class IMUFilter {
public:
    IMUFilter(double initial_time);

    // 更新滤波器状态
    void Update(double current_time, double ax, double ay, double az,
                double gx, double gy, double gz);

    // 获取当前姿态角 (弧度)
    Eigen::Vector3d GetAngles() const {
        return state_;
    }

private:
    // 使用陀螺仪数据进行预测
    void Predict(double gx, double gy, double gz);
    // 使用加速度计数据更新
    void UpdateFromAccel(double ax, double ay, double az);
    // 规范化欧拉角状态
    Eigen::Vector3d NormalizeState(const Eigen::Vector3d& state);

private:
    // 状态向量 [pitch, roll, yaw] (单位：弧度)
    Eigen::Vector3d state_;
    // 状态协方差矩阵
    Eigen::Matrix3d covariance_;
    // 过程噪声协方差
    Eigen::Matrix3d process_noise_;
    // 测量噪声协差
    Eigen::Matrix3d measurement_noise_;
    // 采样时间 (秒)
    double delta_time_;
    // 上次时间戳 (秒)
    double last_time_;
    // 重力加速度 (m/s^2)
    const double gravity_ = 9.81;
};

} // namespace fescue_iox