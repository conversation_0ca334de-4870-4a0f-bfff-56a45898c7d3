#pragma once

#include <atomic>
#include <chrono>
#include <memory>
#include <shared_mutex>
#include <string>
#include <unordered_map>

namespace fescue_iox
{

inline std::unordered_map<std::size_t, std::atomic<int64_t>> &get_log_time_map()
{
    static std::unordered_map<std::size_t, std::atomic<int64_t>> log_time_map;
    return log_time_map;
}

inline std::shared_mutex &get_log_mutex()
{
    static std::shared_mutex log_mutex;
    return log_mutex;
}

inline std::atomic<bool> &get_log_cleanup_flag()
{
    static std::atomic<bool> cleanup_flag{false};
    return cleanup_flag;
}

#define LOG_THROTTLE_IMPL(level, interval_ms, ...)                                                              \
    do                                                                                                          \
    {                                                                                                           \
        auto &log_time_map = get_log_time_map();                                                                \
        auto &log_mutex = get_log_mutex();                                                                      \
        auto &cleanup_flag = get_log_cleanup_flag();                                                            \
        uint64_t interval = interval_ms <= 0 ? 1000 : interval_ms;                                              \
                                                                                                                \
        std::size_t log_key = std::hash<std::string>{}(std::string(__FILE__) + ":" + std::to_string(__LINE__)); \
        auto now = std::chrono::steady_clock::now().time_since_epoch().count();                                 \
                                                                                                                \
        {                                                                                                       \
            std::shared_lock read_lock(log_mutex);                                                              \
            auto it = log_time_map.find(log_key);                                                               \
            if (it != log_time_map.end())                                                                       \
            {                                                                                                   \
                auto last_time = it->second.load(std::memory_order_relaxed);                                    \
                if (uint64_t(now - last_time) < interval * 1000000)                                             \
                {                                                                                               \
                    break; /* 未到达间隔时间，跳过日志 */                                           \
                }                                                                                               \
            }                                                                                                   \
        }                                                                                                       \
                                                                                                                \
        {                                                                                                       \
            std::unique_lock write_lock(log_mutex);                                                             \
            auto &last_log_time = log_time_map[log_key];                                                        \
            auto last_time = last_log_time.load(std::memory_order_relaxed);                                     \
            if (uint64_t(now - last_time) >= interval * 1000000)                                                \
            {                                                                                                   \
                last_log_time.store(now, std::memory_order_relaxed);                                            \
                SPDLOG_LOGGER_##level(spdlog::default_logger(), __VA_ARGS__);                                   \
            }                                                                                                   \
                                                                                                                \
            /* 定期清理旧日志数据，防止 map 过大 */                                               \
            if (log_time_map.size() > 1000 && !cleanup_flag.exchange(true))                                     \
            {                                                                                                   \
                for (auto it = log_time_map.begin(); it != log_time_map.end();)                                 \
                {                                                                                               \
                    if (now - it->second.load(std::memory_order_relaxed) > 300000000000)                        \
                        it = log_time_map.erase(it); /* 超过 5 分钟未更新，删除 */                    \
                    else                                                                                        \
                        ++it;                                                                                   \
                }                                                                                               \
                cleanup_flag.store(false); /* 清理完成，标记复位 */                                    \
            }                                                                                                   \
        }                                                                                                       \
    }                                                                                                           \
    while (0)

#define LOG_DEBUG_THROTTLE(interval_ms, ...) LOG_THROTTLE_IMPL(DEBUG, interval_ms, __VA_ARGS__)
#define LOG_INFO_THROTTLE(interval_ms, ...) LOG_THROTTLE_IMPL(INFO, interval_ms, __VA_ARGS__)
#define LOG_WARN_THROTTLE(interval_ms, ...) LOG_THROTTLE_IMPL(WARN, interval_ms, __VA_ARGS__)
#define LOG_ERROR_THROTTLE(interval_ms, ...) LOG_THROTTLE_IMPL(ERROR, interval_ms, __VA_ARGS__)

} // namespace fescue_iox