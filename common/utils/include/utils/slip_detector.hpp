#pragma once

#include <vector>
#include <deque>
#include <memory>
#include <algorithm>
#include <numeric>

#include "utils/imu_filter.hpp"

namespace fescue_iox 
{

struct AccelerationFilterData {
    double ax_filter_val = 0.0;
    double ay_filter_val = 0.0;
    std::vector<double> ax_window;
    std::vector<double> ay_window;
};

class SimpleLowPassFilter {
public:
    SimpleLowPassFilter(double alpha = 0.95, double min = -1.0, double max = 1.0)
        : alpha(alpha), min_val(min), max_val(max), filtered_val(0.0) {}

    double Update(double input) {
        // 限幅
        input = std::clamp(input, min_val, max_val);
        // 低通滤波
        filtered_val = alpha * filtered_val + (1.0 - alpha) * input;
        return filtered_val;
    }

    double GetFilteredVal() const { return filtered_val; }
    void SetFilteredVal(double val) { filtered_val = val; }

private:
    double alpha;       // 滤波系数 (0.8~0.99)
    double min_val;     // 最小值限制
    double max_val;     // 最大值限制
    double filtered_val;// 滤波后的值
};

// 滑动窗口校验（中值/均值可选）
class SlidingWindowFilter {
public:
    enum class Mode { MEAN, MEDIAN };

    SlidingWindowFilter(size_t window_size = 10, double max_deviation = 0.15, Mode mode = Mode::MEAN)
        : window_size(window_size), max_deviation(max_deviation), mode(mode) {}

    double Update(double input) {
        // 更新窗口
        window.push_back(input);
        if (window.size() > window_size) {
            window.pop_front();
        }

        // 计算参考值（均值或中值）
        double reference = (mode == Mode::MEAN) ? calc_mean() : calc_median();

        // 校验偏差
        return (std::abs(input - reference) > max_deviation) ? reference : input;
    }

    const std::deque<double>& GetWindow() const { return window; }
    void SetWindow(const std::deque<double>& window) { this->window = window; }

private:
    double calc_mean() const {
        return std::accumulate(window.begin(), window.end(), 0.0) / window.size();
    }

    double calc_median() {
        std::deque<double> sorted = window;
        std::sort(sorted.begin(), sorted.end());
        return sorted[sorted.size() / 2];
    }

    size_t window_size;     // 窗口大小
    double max_deviation;   // 最大允许偏差
    Mode mode;              // 校验模式
    std::deque<double> window;
};

class AccelerationProcessor {
public:
    AccelerationProcessor(
        double lpf_alpha = 0.95,
        double min_accel = -1.0,
        double max_accel = 1.0,
        size_t window_size = 10,
        double max_deviation = 0.15,
        SlidingWindowFilter::Mode sw_mode = SlidingWindowFilter::Mode::MEAN
    ) : lpf_x(lpf_alpha, min_accel, max_accel),
        lpf_y(lpf_alpha, min_accel, max_accel),
        sw_x(window_size, max_deviation, sw_mode),
        sw_y(window_size, max_deviation, sw_mode) {}

    void Update(double ax, double ay) {
        // 1. 低通滤波 + 限幅
        ax = lpf_x.Update(ax);
        ay = lpf_y.Update(ay);

        // 2. 滑动窗口校验（可选）
        ax = sw_x.Update(ax);
        ay = sw_y.Update(ay);

        // 存储结果
        filtered_ax = ax;
        filtered_ay = ay;
    }

    double GetAx() const { return filtered_ax; }
    double GetAy() const { return filtered_ay; }

    double GetAxFilterValue() const { return lpf_x.GetFilteredVal(); }
    double GetAyFilterValue() const { return lpf_y.GetFilteredVal(); }
    const std::deque<double>& GetAxWindow() const { return sw_x.GetWindow(); }
    const std::deque<double>& GetAyWindow() const { return sw_y.GetWindow(); }

    void SetAxFilterValue(double val) { lpf_x.SetFilteredVal(val); }
    void SetAyFilterValue(double val) { lpf_y.SetFilteredVal(val); }
    void SetAxWindow(const std::deque<double>& window) { sw_x.SetWindow(window); }
    void SetAyWindow(const std::deque<double>& window) { sw_y.SetWindow(window); }

private:
    SimpleLowPassFilter lpf_x, lpf_y;     // 独立的x/y轴低通滤波
    SlidingWindowFilter sw_x, sw_y; // 独立的x/y轴滑动窗口校验
    double filtered_ax = 0.0;
    double filtered_ay = 0.0;
};

struct SlipDataUnit 
{
    SlipDataUnit(double _time, double _imu_yaw, double _odom_linear_velocity, double _odom_angular_velocity,
                 double _ax, double _ay, double _az, double _gx, double _gy, double _gz) : 
        time(_time), imu_yaw(_imu_yaw), 
        odom_linear_velocity(_odom_linear_velocity), odom_angular_velocity(_odom_angular_velocity),
        ax(_ax), ay(_ay), az(_az), gx(_gx), gy(_gy), gz(_gz) {}
    
    double time = 0;
    double imu_yaw = 0;
    double odom_linear_velocity = 0;
    double odom_angular_velocity = 0;
    double ax = 0;
    double ay = 0;
    double az = 0;
    double gx = 0;
    double gy = 0;
    double gz = 0;
};

class SlipDetector 
{
public:
    SlipDetector();

    void Update(double current_time, double ax, double ay, double az,
                double gx, double gy, double gz, double left_motor_speed, 
                double right_motor_speed, bool is_motion);

    void Reset();

    double GetTurningSlipRatio() const {
        return turning_slip_ratio_;
    }

    double GetOdomImuAngleDiff() const {
        return odom_imu_angle_diff_;
    }

    double GetImuDisplacement() const {
        return imu_displacement_;
    }

    double GetOdomDisplacement() const {
        return odom_displacement_;
    }

    double GetDisplacementDiff() const {
        return displacement_diff_;
    }

    double GetMovingSlipRatio() const {
        return moving_slip_ratio_;
    }

    const std::vector<std::pair<double, double>>& GetFFTGxData() const {
        return fft_gx_data_;
    }

    const std::vector<std::pair<double, double>>& GetFFTGyData() const {
        return fft_gy_data_;
    }

    const std::vector<std::pair<double, double>>& GetFFTGzData() const {
        return fft_gz_data_;
    }

    bool IsFreqWheelSlip() const {
        return is_freq_wheel_slip_;
    }

private:
    void CalculateTurningSlipRatio(double current_time);
    void CalculateMovingSlipRatio(double current_time);
    Eigen::Vector2d CalculateIMUDisplacement() const;
    double CalculateOdomDisplacement(double current_time);
    std::vector<std::pair<double, double>> FFTIMUAngularVelocity(const Eigen::VectorXd& signal) const;
    void CalculateFFTData();

private:
    std::shared_ptr<IMUFilter> imu_filter_;
    double turning_slip_ratio_;
    double moving_slip_ratio_;
    std::deque<SlipDataUnit> slip_data_buffer_;
    double odom_imu_angle_diff_ = 0.0;
    double turning_slip_start_time_ = 0.0;
    double imu_displacement_ = 0.0;
    double odom_displacement_ = 0.0;
    double displacement_diff_ = 0.0;
    double moving_slip_start_time_ = 0.0;
    double last_check_moving_slip_time_ = 0.0;
    std::vector<std::pair<double, double>> fft_gx_data_;
    std::vector<std::pair<double, double>> fft_gy_data_;
    std::vector<std::pair<double, double>> fft_gz_data_;
    bool is_freq_wheel_slip_ = false;
    double no_motion_start_time_ = -1;
    double positive_displacement_start_time_ = -1;
    double positive_displacement_time_ = -1;
};

}