#ifndef UTILS_MATH_TYPE_HPP
#define UTILS_MATH_TYPE_HPP

#include <vector>

namespace fescue_iox
{

struct VecXY
{
    float x = 0;
    float y = 0;
    VecXY() {}
    VecXY(const float &x_in, const float &y_in)
        : x(x_in)
        , y(y_in)
    {
    }
};

struct VecXYW : public VecXY
{
    float w = 0;
    VecXYW() {}
    VecXYW(const float &x_in, const float &y_in, const float &w_in)
        : VecXY(x_in, y_in)
        , w(w_in)
    {
    }
};

struct VecXYZRPW : public VecXYW
{
    float z = 0;
    float r = 0;
    float p = 0;
    VecXYZRPW() {}
    VecXYZRPW(const float &x_in, const float &y_in, const float &z_in,
              const float &r_in, const float &p_in, const float &w_in)
        : VecXYW(x_in, y_in, w_in)
        , z(z_in)
        , r(r_in)
        , p(p_in)
    {
    }
};

struct VecXYZ : public VecXY
{
    float z = 0;
    VecXYZ() {}
    VecXYZ(const float &x_in, const float &y_in, const float &z_in)
        : VecXY(x_in, y_in)
        , z(z_in)
    {
    }
};

struct Quaterion
{
    float x = 0;
    float y = 0;
    float z = 0;
    float w = 1;
    Quaterion() {}
    Quaterion(const float &x_in, const float &y_in, const float &z_in, const float &w_in)
        : x(x_in)
        , y(y_in)
        , z(z_in)
        , w(w_in)
    {
    }
};

struct Region
{
    float begin;
    float end;
    Region() {}
    Region(const float &begin_in, const float &eng_in)
        : begin(begin_in)
        , end(eng_in)
    {
    }
};

int UniformDistr(const int &lower, const int &upper);
float UniformDistr(const float &lower, const float &upper);
float NormalDistr(const float &mean, const float &std_dev);

float UnifyAngle(const float &angle);
double UnifyAngle(const double &angle);
float MinorArc(const float &angle_base, const float &angle);

VecXYW G2L(const VecXYW &robot_pose, const VecXYW &in);
VecXYW L2G(const VecXYW &robot_pose, const VecXYW &in);

VecXYW World2GridMap(const VecXYW &origin_in_map, const VecXYW &in, const float &reso);
VecXYW GridMap2World(const VecXYW &origin_in_map, const VecXYW &in, const float &reso);

int Sign(const float &x);
float Sat(const float &x, const float &lower, const float &upper);

float Point2LineDist(const VecXY &point, const VecXY &start, const VecXY &end);
float Point2LineDist(const VecXY &point, const VecXY &start, const VecXY &end, VecXY &projection);
std::vector<VecXY> ExtractLine(const std::vector<VecXY> &intensive_points, const float &dist_thr);

float Point2LineSegmentDist(const VecXY &point, const VecXY &start, const VecXY &end, VecXY &projection);

bool LineSegmentsIntersection(const VecXY &line1_p1, const VecXY &line1_p2,
                              const VecXY &line2_p1, const VecXY &line2_p2,
                              VecXY &intersection);

std::vector<Region> MergeAngles(const std::vector<Region> &range_in);

std::vector<VecXY> LineSegmentCircleIntersection(const VecXY &line_p1, const VecXY &line_p2,
                                                 const VecXY &circle_center, const float &circle_radius);

// 将角度规范化到[-PI, PI]范围内
double NormalizeAngle(double angle);

class LowPassFilter
{
private:
    float alpha_;           // 滤波系数
    float prevOutput_{0.0}; // 上一次的输出值

public:
    LowPassFilter(const float &cutoffFrequency, const float &dt)
    {
        alpha_ = cutoffFrequency * dt / (cutoffFrequency * dt + 1.0);
    }

    inline float update(const float &input)
    {
        float output = alpha_ * input + (1 - alpha_) * prevOutput_;
        prevOutput_ = output;
        return output;
    }

    inline void setInitialValue(const float &initialValue)
    {
        prevOutput_ = initialValue; // 使用初始值作为 prevOutput
    }
};

} // namespace fescue_iox

#endif
