#include "utils/signal_handler.hpp"

#include "backtrace.h"
#include "logger.hpp"

#include <csignal>
#include <cstdlib>
#include <cxxabi.h>
#include <dlfcn.h>
#include <execinfo.h>
#include <filesystem>
#include <fstream>
#include <iostream>
#include <regex>
#include <string>
#include <unistd.h>

namespace fescue_iox
{

void (*g_userCallback)(int) = nullptr;
std::string g_nodeName;
struct backtrace_state *g_state = nullptr;

static void ErrorCallback(void *data, const char *msg, int errnum)
{
    auto *oss = static_cast<std::ostringstream *>(data);
    if (oss && msg)
    {
        *oss << "[libbacktrace error] " << msg << " (errnum: " << errnum << ")\n";
    }
}

static int FullCallback(void *data, uintptr_t pc, const char *filename, int lineno, const char *function)
{
    (void)pc;
    auto *oss = static_cast<std::ostringstream *>(data);
    if (oss && filename && function)
    {
        *oss << " => " << function << " at " << filename << ":" << lineno << "\n";
    }
    return 0;
}

void SignalHandler(int signo, siginfo_t *info, void * /*context*/)
{
    std::ostringstream log;
    log << "\n=== Crash Detected ===\n";
    log << "Executable: " << g_nodeName << "\n";
    if (info)
    {
        log << "Signal: " << signo << " (" << strsignal(signo)
            << "), PID: " << info->si_pid << ", UID: " << info->si_uid << "\n";
    }
    else
    {
        log << "Signal: " << signo << " (" << strsignal(signo) << ")\n";
    }

    std::time_t now = std::time(nullptr);
    log << "Timestamp: " << std::asctime(std::localtime(&now)) << "\n";
    log << StackTraceToString(64) << "\n";

    if (g_state)
    {
        log << "==== [libbacktrace symbol info] ====\n";
        backtrace_full(g_state, 0, FullCallback, ErrorCallback, &log);
        log << "=====================================\n";
    }

    try
    {
        std::filesystem::create_directories("/userdata/log/crash_logs");
        std::string filename = "crash_" + g_nodeName + "_" + std::to_string(now) + ".log";
        std::ofstream fout("/userdata/log/crash_logs/" + filename, std::ios::out);
        if (fout)
        {
            fout << log.str();
            fout.close();
            LOG_INFO("Crash log written to /userdata/log/crash_logs/{}", filename.c_str());
        }
        else
        {
            LOG_ERROR("Failed to write crash log file");
        }
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("Exception writing crash log: {}", e.what());
    }

    LOG_ERROR("{}", log.str());

    if (g_userCallback)
    {
        g_userCallback(signo); // 用户清理逻辑
    }

    // 恢复默认信号处理器，触发 core dump（可选）
    signal(signo, SIG_DFL);
    raise(signo);

    LOG_ERROR("Exiting due to signal {}", signo);

    exit(1);
}

void InstallSignalHandlers(const std::string &node_name, void (*userCallback)(int))
{
    g_userCallback = userCallback;
    g_nodeName = node_name;
    g_state = backtrace_create_state("/proc/self/exe", 1, ErrorCallback, nullptr);

    struct sigaction sa;
    sa.sa_sigaction = SignalHandler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = SA_SIGINFO;

    const int handled_signals[] = {
        SIGSEGV,
        SIGABRT,
        SIGFPE,
        SIGILL,
        SIGBUS,
        SIGPIPE,
        SIGINT,
        SIGTERM,
        SIGQUIT,
    };

    for (int sig : handled_signals)
    {
        sigaction(sig, &sa, nullptr);
    }
}

std::string StackTraceToString(unsigned int max_frames)
{
    std::vector<void *> stack(max_frames);
    int size = backtrace(stack.data(), max_frames);
    char **symbols = backtrace_symbols(stack.data(), size);

    std::string result("\n====================== Stack Trace ======================\n");

    for (int i = 0; i < size; ++i)
    {
        Dl_info info;
        if (dladdr(stack[i], &info) && info.dli_sname)
        {
            int status = -1;
            char *demangled = abi::__cxa_demangle(info.dli_sname, nullptr, nullptr, &status);
            result += "  ";
            result += (status == 0 && demangled) ? demangled : info.dli_sname;
            result += "\n";
            free(demangled);
        }
        else
        {
            result += std::string("  ") + symbols[i] + "\n";
        }
    }

    free(symbols);
    result += "==========================================================\n";
    return result;
}

} // namespace fescue_iox
