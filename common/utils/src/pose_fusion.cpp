#include "utils/pose_fusion.hpp"

#include <iostream>

namespace fescue_iox
{

PoseFusion::PoseFusion() {}

std::pair<double, double> PoseFusion::GetRobotSpeed(double motor_speed_left, double motor_speed_right) const
{
    double wheel_radius = 0.1f;
    double wheel_base = 0.335f;
    float w_left = motor_speed_left * 2 * M_PI / 60.0f;
    float w_right = motor_speed_right * 2 * M_PI / 60.0f;
    float v_left = w_left * wheel_radius;
    float v_right = w_right * wheel_radius;
    float theoretical_linear = (v_right + v_left) / 2.0f;
    float theoretical_angular = (v_right - v_left) / wheel_base;
    return std::make_pair(theoretical_linear, theoretical_angular);
}

void PoseFusion::Update(double current_time, double ax, double ay, double az,
                        double gx, double gy, double gz, double left_motor_speed,
                        double right_motor_speed, bool is_motion)
{
    if (slip_detector_ == nullptr)
    {
        slip_detector_ = std::make_shared<SlipDetector>();
    }
    if (imu_filter_ == nullptr)
    {
        Reset();
        imu_filter_ = std::make_shared<IMUFilter>(current_time);
        last_time_ = current_time;
        first_time_ = current_time;
        return;
    }
    imu_filter_->Update(current_time, ax, ay, az, gx, gy, gz);
    auto angles = imu_filter_->GetAngles();
    imu_filter_pitch_ = angles[0];
    imu_filter_roll_ = angles[1];
    imu_filter_yaw_ = angles[2];
    // x,y轴加速度，默认机器人不会平移，所以y轴加速度为0
    double imu_world_ax = ax * cos(imu_filter_yaw_);
    double imu_world_ay = ax * sin(imu_filter_yaw_);
    // x,y轴速度
    auto [linear_speed, angular_speed] = GetRobotSpeed(left_motor_speed, right_motor_speed);
    double linear_speed_sign = linear_speed > -1e-6 ? 1.0 : -1.0;
    double odom_world_vx = linear_speed * cos(imu_filter_yaw_);
    double odom_world_vy = linear_speed * sin(imu_filter_yaw_);
    // 根据外部条件，调整融合比例
    double slip_ratio = UpdateSlipRatio(current_time, ax, ay, az, gx, gy, gz, left_motor_speed, right_motor_speed, is_motion);
    double fusion_ratio = GetFusionRatio(slip_ratio);
    // double time_diff = current_time - first_time_;
    is_wheel_slip_ = slip_detector_->IsFreqWheelSlip();
    turning_slip_ratio_ = slip_detector_->GetTurningSlipRatio();
    moving_slip_ratio_ = slip_detector_->GetMovingSlipRatio();
    // std::cout << "time_diff: " << time_diff
    //           << " slip_ratio: " << slip_ratio
    //           << " fusion_ratio: " << fusion_ratio
    //           << " is_wheel_slip: " << is_wheel_slip_
    //           << " turning_slip_ratio: " << turning_slip_ratio_
    //           << " moving_slip_ratio: " << moving_slip_ratio_
    //           << std::endl;
    UpdateVelocity(slip_ratio, linear_speed);
    double delta_time = current_time - last_time_;
    last_time_ = current_time;
    vx_ = fusion_ratio * odom_world_vx + (1 - fusion_ratio) * (vx_ + imu_world_ax * delta_time);
    vy_ = fusion_ratio * odom_world_vy + (1 - fusion_ratio) * (vy_ + imu_world_ay * delta_time);
    linear_velocity_ = std::sqrt(vx_ * vx_ + vy_ * vy_);
    linear_velocity_ *= linear_speed_sign;
    angular_velocity_ = gz;
    // x,y轴位移
    x_ += vx_ * delta_time;
    y_ += vy_ * delta_time;
}

double PoseFusion::UpdateSlipRatio(double current_time, double ax, double ay, double az,
                                   double gx, double gy, double gz, double left_motor_speed,
                                   double right_motor_speed, bool is_motion)
{
    // TODO: 屏蔽打滑的影响，先只用里程计
    return 0.0;

    // 检测打滑
    slip_detector_->Update(current_time, ax, ay, az, gx, gy, gz, left_motor_speed, right_motor_speed, is_motion);
    bool is_wheel_slip = slip_detector_->IsFreqWheelSlip();
    if (is_wheel_slip)
    {
        return 1.0;
    }
    double turning_slip_ratio = slip_detector_->GetTurningSlipRatio();
    double moving_slip_ratio = slip_detector_->GetMovingSlipRatio();
    double slip_ratio = std::max(turning_slip_ratio, moving_slip_ratio);
    return slip_ratio;
}

double PoseFusion::GetFusionRatio(double slip_ratio) const
{
    double max_slip_ratio = 0.8;
    double min_slip_ratio = 0.5;
    double max_fusion_ratio = 0.9;
    double min_fusion_ratio = 0.0;
    if (slip_ratio > max_slip_ratio)
    {
        return min_fusion_ratio;
    }
    if (slip_ratio < min_slip_ratio)
    {
        return max_fusion_ratio;
    }
    double k = -3;
    double b = 2.4;
    return k * slip_ratio + b;
}

void PoseFusion::UpdateVelocity(double slip_ratio, double linear_speed)
{
    (void)linear_speed;
    double max_slip_ratio = 0.8;
    if (slip_ratio > max_slip_ratio)
    {
        double max_slip_velocity = 0.01;
        double max_slip_x_velocity = max_slip_velocity * cos(imu_filter_yaw_);
        double max_slip_y_velocity = max_slip_velocity * sin(imu_filter_yaw_);
        if (abs(vx_) > abs(max_slip_x_velocity))
        {
            vx_ = vx_ * abs(max_slip_x_velocity / vx_);
        }
        else
        {
            vx_ = 0.0;
        }
        if (abs(vy_) > abs(max_slip_y_velocity))
        {
            vy_ = vy_ * abs(max_slip_y_velocity / vy_);
        }
        else
        {
            vy_ = 0.0;
        }
        return;
    }
    double k = -1.2125;
    double b = 1.0;
    double velocity_ratio = k * slip_ratio + b;
    vx_ = vx_ * velocity_ratio;
    vy_ = vy_ * velocity_ratio;
}

void PoseFusion::Reset()
{
    imu_filter_ = nullptr;
    slip_detector_ = nullptr;
    imu_filter_yaw_ = 0;
    imu_filter_pitch_ = 0;
    imu_filter_roll_ = 0;
    x_ = 0;
    y_ = 0;
    vx_ = 0.0;
    vy_ = 0.0;
    last_time_ = -1;
    first_time_ = -1;
    is_wheel_slip_ = false;
    turning_slip_ratio_ = 0;
    moving_slip_ratio_ = 0;
    linear_velocity_ = 0;
    angular_velocity_ = 0;
}

} // namespace fescue_iox