#include "utils/imu_filter.hpp"

#include <iostream>

#include "utils/math_type.hpp"

namespace fescue_iox
{

IMUFilter::IMUFilter(double initial_time) : last_time_(initial_time) {
    // 初始化状态 (水平放置，角度为0)
    state_ = Eigen::Vector3d::Zero();
        
    // 初始化协方差矩阵
    covariance_ = Eigen::Matrix3d::Identity();
        
    // 过程噪声 (陀螺仪噪声特性)
    process_noise_ = Eigen::Matrix3d::Identity() * 0.001;
        
    // 测量噪声 (加速度计噪声特性)
    measurement_noise_ = Eigen::Matrix3d::Identity();
    measurement_noise_(0,0) = 0.1;   // pitch噪声
    measurement_noise_(1,1) = 0.1;   // roll噪声 
    measurement_noise_(2,2) = 10.0;  // yaw噪声 (加速度计无法测量yaw)
}

void IMUFilter::Update(double current_time, double ax, double ay, double az, 
                       double gx, double gy, double gz) {
    // 计算时间增量
    delta_time_ = current_time - last_time_;
    last_time_ = current_time;
    
    if(delta_time_ <= 0) return;
    // 1. 预测步骤 (使用陀螺仪数据)
    Predict(gx, gy, gz);
    // 2. 更新步骤 (使用加速度计数据)
    UpdateFromAccel(ax, ay, az);
}

void IMUFilter::Predict(double gx, double gy, double gz) {
    // 简化的状态转移矩阵
    Eigen::Matrix3d F = Eigen::Matrix3d::Identity();
    // 获取当前姿态
    double pitch = state_(0);
    double roll = state_(1);
    double sinP = sin(pitch);
    double cosP = cos(pitch);
    double sinR = sin(roll);
    double cosR = cos(roll);
    // 处理tan(pitch)在±90°附近的奇点
    double tanP = 0.0;
    if (fabs(cosP) > 0.001) {
        tanP = sinP / cosP;
    } else {
        tanP = (cosP > 0) ? 1e6 : -1e6;
    }
    Eigen::Vector3d angular_rates;
    angular_rates(0) = cosR * gy - sinR * gz;                    // pitch_rate
    angular_rates(1) = gx + sinR * tanP * gy + cosR * tanP * gz; // roll_rate
    angular_rates(2) = (sinR / cosP) * gy + (cosR / cosP) * gz;  // yaw_rate
    // 状态预测 (欧拉积分)
    state_ += angular_rates * delta_time_;
    state_ = NormalizeState(state_);
    // 协方差预测
    covariance_ = F * covariance_ * F.transpose() + process_noise_;
}

void IMUFilter::UpdateFromAccel(double ax, double ay, double az) {
    Eigen::Vector3d measured_angles;
    // Pitch计算
    measured_angles(0) = atan2(-ax, sqrt(ay*ay + az*az));
    // Roll计算
    measured_angles(1) = atan2(ay, sqrt(ax*ax + az*az));
    // Yaw无法从加速度计获得，保持当前估计
    measured_angles(2) = state_(2);
    
    // 权重越小 → 噪声越大 → 卡尔曼增益越小 → 加速度计影响越小
    double accel_weight = 0.001; 
    Eigen::Vector3d noise_vector;
    
    // 基础噪声值
    const double base_pitch_noise = 0.1;
    const double base_roll_noise = 0.1;
    const double base_yaw_noise = 10.0;
    
    // 计算实际噪声 = 基础噪声 / 权重
    noise_vector(0) = base_pitch_noise / accel_weight;
    noise_vector(1) = base_roll_noise / accel_weight;
    noise_vector(2) = base_yaw_noise; // yaw噪声保持不变
    
    measurement_noise_ = noise_vector.asDiagonal();
    
    // 计算卡尔曼增益
    Eigen::Matrix3d K = covariance_ * (covariance_ + measurement_noise_).inverse();
    
    // 状态更新 - 添加角度更新限制
    Eigen::Vector3d angle_diff = measured_angles - state_;
    
    // 限制单次更新幅度（最大±15°）
    const double max_update = M_PI / 12; // 15°
    for (int i = 0; i < 3; i++) {
        if (fabs(angle_diff[i]) > max_update) {
            angle_diff[i] = (angle_diff[i] > 0) ? max_update : -max_update;
        }
    }
    
    state_ = state_ + K * angle_diff;
    state_ = NormalizeState(state_);
    
    // 协方差更新
    covariance_ = (Eigen::Matrix3d::Identity() - K) * covariance_;
}

// 规范化欧拉角状态
Eigen::Vector3d IMUFilter::NormalizeState(const Eigen::Vector3d& state) {
    return Eigen::Vector3d(
        NormalizeAngle(state.x()),  // pitch
        NormalizeAngle(state.y()),  // roll
        NormalizeAngle(state.z())   // yaw
    );
}

} // namespace fescue_iox