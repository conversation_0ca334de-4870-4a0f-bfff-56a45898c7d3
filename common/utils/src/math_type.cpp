#include "utils/math_type.hpp"

#include <algorithm>
#include <cmath>
#include <random>

namespace fescue_iox
{

std::default_random_engine random_engine;

struct AngleWithRangeInfo
{
    float angle;
    int direction;
    int sum;
    AngleWithRangeInfo() {}
    AngleWithRangeInfo(const float &angle_in, const int &direction_in)
        : angle(angle_in)
        , direction(direction_in)
    {
    }
};

bool CompareAngleWithRangeInfo(const AngleWithRangeInfo &i, const AngleWithRangeInfo &j)
{
    return i.angle < j.angle;
}

int UniformDistr(const int &lower, const int &upper)
{
    std::uniform_int_distribution<int> param(lower, upper);
    return param(random_engine);
}

float UniformDistr(const float &lower, const float &upper)
{
    std::uniform_real_distribution<float> param(lower, upper);
    return param(random_engine);
}

float NormalDistr(const float &mean, const float &std_dev)
{
    std::normal_distribution<float> param(mean, std_dev);
    return param(random_engine);
}

float UnifyAngle(const float &angle)
{
    return angle < -M_PI ? (angle + 2 * M_PI) : (angle > M_PI ? (angle - 2 * M_PI) : angle);
}

double UnifyAngle(const double &angle)
{
    return angle < -M_PI ? (angle + 2 * M_PI) : (angle > M_PI ? (angle - 2 * M_PI) : angle);
}

float MinorArc(const float &angle_base, const float &angle)
{
    float angle_temp = angle;
    float angle_delta = angle - angle_base;
    if (angle_delta > M_PI)
    {
        angle_temp -= 2 * M_PI;
    }
    else if (angle_delta < -M_PI)
    {
        angle_temp += 2 * M_PI;
    }
    return angle_temp;
}

VecXYW G2L(const VecXYW &robot_pose, const VecXYW &in)
{
    VecXYW out;
    VecXYW temp;
    temp.x = in.x - robot_pose.x;
    temp.y = in.y - robot_pose.y;
    temp.w = in.w;

    out.x = temp.x * cosf(robot_pose.w) + temp.y * sinf(robot_pose.w);
    out.y = -temp.x * sinf(robot_pose.w) + temp.y * cosf(robot_pose.w);
    out.w = UnifyAngle(temp.w - robot_pose.w);
    return out;
}

VecXYW L2G(const VecXYW &robot_pose, const VecXYW &in)
{
    VecXYW out;
    out.x = in.x * cosf(robot_pose.w) - in.y * sinf(robot_pose.w) + robot_pose.x;
    out.y = in.x * sinf(robot_pose.w) + in.y * cosf(robot_pose.w) + robot_pose.y;
    out.w = UnifyAngle(in.w + robot_pose.w);
    return out;
}

float Sat(const float &x, const float &lower, const float &upper)
{
    return (x > upper) ? upper : ((x < lower) ? lower : x);
}

VecXYW World2GridMap(const VecXYW &origin_in_map, const VecXYW &in, const float &reso)
{
    VecXYW out;
    VecXYW in_temp = in;
    VecXYW origin_in_map_temp = origin_in_map;
    origin_in_map_temp.y = -origin_in_map.y;
    in_temp.x /= reso;
    in_temp.y /= reso;
    out = L2G(origin_in_map_temp, in_temp);
    out.y = -out.y;
    return out;
}

VecXYW GridMap2World(const VecXYW &origin_in_map, const VecXYW &in, const float &reso)
{
    VecXYW out;
    VecXYW in_temp = in;
    VecXYW origin_in_map_temp = origin_in_map;
    origin_in_map_temp.y = -origin_in_map.y;
    in_temp.y = -in.y;
    out = G2L(origin_in_map_temp, in_temp);
    out.x *= reso;
    out.y *= reso;
    return out;
}

int Sign(const float &x)
{
    return (x > 1e-6) ? 1 : ((x < -1e-6) ? -1 : 0);
}

float Point2LineDist(const VecXY &point, const VecXY &start, const VecXY &end)
{
    VecXY start_end_vec;
    VecXY start_curr_vec;
    float distance;

    start_end_vec.x = end.x - start.x;
    start_end_vec.y = end.y - start.y;
    start_curr_vec.x = point.x - start.x;
    start_curr_vec.y = point.y - start.y;

    distance = (start_end_vec.x * start_curr_vec.y - start_curr_vec.x * start_end_vec.y) / hypotf(start_end_vec.x, start_end_vec.y);

    return distance;
}

float Point2LineDist(const VecXY &point, const VecXY &start, const VecXY &end, VecXY &projection)
{
    VecXYW start_with_theta(start.x, start.y, atan2f(end.y - start.y, end.x - start.x));
    // VecXYW end_rel_start = G2L(start_with_theta, VecXYW(end.x, end.y, 0));
    VecXYW point_rel_start = G2L(start_with_theta, VecXYW(point.x, point.y, 0));
    VecXYW projection_rel_start(point_rel_start.x, 0, 0);
    VecXYW projection_with_theta = L2G(start_with_theta, projection_rel_start);
    projection = projection_with_theta;
    return point_rel_start.y;
}

std::vector<VecXY> ExtractLine(const std::vector<VecXY> &intensive_points, const float &dist_thr)
{
    std::vector<VecXY> intensive_points_l;
    std::vector<VecXY> intensive_points_r;
    std::vector<VecXY> sparse_points_l;
    std::vector<VecXY> sparse_points_r;
    std::vector<VecXY> sparse_points;

    if (intensive_points.size() > 2)
    {
        float max_dist = 0;
        float dist;
        std::vector<VecXY>::const_iterator index;

        for (std::vector<VecXY>::const_iterator it = intensive_points.begin() + 1; it < intensive_points.end() - 1; it++)
        {
            dist = fabs(Point2LineDist(*it, intensive_points.front(), intensive_points.back()));
            if (dist > max_dist)
            {
                max_dist = dist;
                index = it;
            }
        }
        if (max_dist > dist_thr)
        {
            sparse_points_l = ExtractLine(std::vector<VecXY>(intensive_points.begin(), index + 1), dist_thr);
            sparse_points_r = ExtractLine(std::vector<VecXY>(index, intensive_points.end()), dist_thr);
            sparse_points.insert(sparse_points.begin(), sparse_points_l.begin(), sparse_points_l.end() - 1);
            sparse_points.insert(sparse_points.end(), sparse_points_r.begin(), sparse_points_r.end());
            return sparse_points;
        }
    }
    sparse_points.insert(sparse_points.begin(), intensive_points.front());
    sparse_points.insert(sparse_points.end(), intensive_points.back());
    return sparse_points;
}

float Point2LineSegmentDist(const VecXY &point, const VecXY &start, const VecXY &end, VecXY &projection)
{
    VecXYW start_with_theta(start.x, start.y, atan2f(end.y - start.y, end.x - start.x));
    VecXYW end_rel_start = G2L(start_with_theta, VecXYW(end.x, end.y, 0));
    VecXYW point_rel_start = G2L(start_with_theta, VecXYW(point.x, point.y, 0));
    VecXYW projection_rel_start(point_rel_start.x, 0, 0);

    if (point_rel_start.x < 0)
    {
        projection = start;
        return Sign(point_rel_start.y) * hypotf(point.x - start.x, point.y - start.y);
    }
    else if (point_rel_start.x > end_rel_start.x)
    {
        projection = end;
        return Sign(point_rel_start.y) * hypotf(point.x - end.x, point.y - end.y);
    }
    else
    {
        projection = L2G(start_with_theta, projection_rel_start);
        return point_rel_start.y;
    }
}

bool LineSegmentsIntersection(const VecXY &line1_p1, const VecXY &line1_p2,
                              const VecXY &line2_p1, const VecXY &line2_p2,
                              VecXY &intersection)
{
    float s123;
    float s124;
    float s341;
    float s342;
    float t;

    s123 = (line1_p1.x - line2_p1.x) * (line1_p2.y - line2_p1.y) - (line1_p1.y - line2_p1.y) * (line1_p2.x - line2_p1.x);
    s124 = (line1_p1.x - line2_p2.x) * (line1_p2.y - line2_p2.y) - (line1_p1.y - line2_p2.y) * (line1_p2.x - line2_p2.x);
    if (Sign(s123) * Sign(s124) != -1)
    {
        return false;
    }
    s341 = (line2_p1.x - line1_p1.x) * (line2_p2.y - line1_p1.y) - (line2_p1.y - line1_p1.y) * (line2_p2.x - line1_p1.x);
    s342 = s341 + s123 - s124;
    if (Sign(s341) * Sign(s342) != -1)
    {
        return false;
    }
    t = s341 / (s124 - s123);
    intersection.x = line1_p1.x + t * (line1_p2.x - line1_p1.x);
    intersection.y = line1_p1.y + t * (line1_p2.y - line1_p1.y);
    return true;
}

std::vector<Region> MergeAngles(const std::vector<Region> &range_in)
{
    std::vector<AngleWithRangeInfo> angles;

    for (const auto &it : range_in)
    {
        angles.emplace_back(it.begin, 1);
        angles.emplace_back(it.begin - 2 * M_PI, 1);
        angles.emplace_back(it.end, -1);
        angles.emplace_back(it.end - 2 * M_PI, -1);
    }
    std::sort(angles.begin(), angles.end(), CompareAngleWithRangeInfo);
    int sum = 0;
    int min_sum = 0;
    for (auto &it : angles)
    {
        sum += it.direction;
        it.sum = sum;
        if (min_sum > sum)
        {
            min_sum = sum;
        }
    }

    std::vector<float> start_out;
    std::vector<float> end_out;
    for (const auto &it : angles)
    {
        if (it.direction == 1 && it.sum == min_sum + 1 && it.angle > -M_PI && it.angle < M_PI)
        {
            start_out.push_back(it.angle);
        }
        else if (it.direction == -1 && it.sum == min_sum && it.angle > -M_PI && it.angle < M_PI)
        {
            end_out.push_back(it.angle);
        }
    }
    if (start_out.size() > 0 && end_out.size() > 0 && start_out[0] > end_out[0])
    {
        float temp = end_out[0];
        int len = end_out.size();
        for (int i = 0; i < len; ++i)
        {
            if (i != len - 1)
            {
                end_out[i] = end_out[i + 1];
            }
            else
            {
                end_out[i] = temp + 2 * M_PI;
            }
        }
    }
    std::vector<Region> range_out;
    for (size_t i = 0; i < start_out.size(); ++i)
    {
        range_out.emplace_back(start_out[i], end_out[i]);
    }
    if (range_in.size() != 0 && range_out.size() == 0)
    {
        range_out.emplace_back(-M_PI, M_PI);
    }
    return range_out;
}

std::vector<VecXY> LineSegmentCircleIntersection(const VecXY &line_p1, const VecXY &line_p2,
                                                 const VecXY &circle_center, const float &circle_radius)
{
    VecXY point;
    float dx;
    float dy;
    float cx;
    float cy;
    float a;
    float b;
    float c;
    float det;
    std::vector<VecXY> intersections;

    dx = line_p2.x - line_p1.x;
    dy = line_p2.y - line_p1.y;
    cx = line_p1.x - circle_center.x;
    cy = line_p1.y - circle_center.y;
    a = dx * dx + dy * dy;
    b = 2 * (dx * cx + dy * cy);
    c = cx * cx + cy * cy - circle_radius * circle_radius;
    det = b * b - 4 * a * c;

    if ((a < 1e-6) || (det < 0))
    {
        return intersections;
    }

    if (det < 1e-6)
    {
        float t = (-b) / (2 * a);
        if (t > 0 && t < 1)
        {
            point.x = line_p1.x + t * dx;
            point.y = line_p1.y + t * dy;
            intersections.push_back(point);
        }
    }
    else
    {
        float t1 = (-b + sqrtf(det)) / (2 * a);
        float t2 = (-b - sqrtf(det)) / (2 * a);
        if (t1 > 0 && t1 < 1)
        {
            point.x = line_p1.x + t1 * dx;
            point.y = line_p1.y + t1 * dy;
            intersections.push_back(point);
        }
        if (t2 > 0 && t2 < 1)
        {
            point.x = line_p1.x + t2 * dx;
            point.y = line_p1.y + t2 * dy;
            intersections.push_back(point);
        }
    }
    return intersections;
}

double NormalizeAngle(double angle) 
{
    const double PI = M_PI;
    const double TWO_PI = 2.0 * PI;
    angle = fmod(angle + PI, TWO_PI);
    if (angle < 0) {
        angle += TWO_PI;
    }
    return angle - PI;
}

} // namespace fescue_iox
