cmake_minimum_required(VERSION 3.5)
project(ob_utils)

# set(CMAKE_CXX_STANDARD 17)
# set(CMAKE_C_STANDARD 11)
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC -O3")
set(CMAKE_C_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG}  -fPIC -g -O3")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC -O3")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -fPIC -g -O3")
# set(CMAKE_BUILD_TYPE "Debug")
set(CMAKE_BUILD_TYPE "Release")
#set(CMAKE_VERBOSE_MAKEFILE ON)

# if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU" OR CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
#     add_compile_options(-Wall -Wextra -Wpedantic)
# endif()

set(library_name ${PROJECT_NAME})

#[0]设置共享库的版本号
set(PROJECT_VERSION_MAJOR 0)
set(PROJECT_VERSION_MINOR 2)
set(PROJECT_VERSION_PATCH 0)
set(LIB_VERSION_MAJOR ${PROJECT_VERSION_MAJOR})
set(LIB_VERSION_MINOR ${PROJECT_VERSION_MINOR})
set(LIB_VERSION_PATCH ${PROJECT_VERSION_PATCH})

#[1]指定头文件查找路径
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)
include_directories(${CMAKE_SOURCE_DIR}/thirdparty/math/eigen3)

#[2] 指定库查找路径
link_directories(
    ${CMAKE_SOURCE_DIR}/thirdparty/yaml/lib/${HOST_PLATFORM}/${SOC_NAME}/
)

#[2]指定编译源文件
aux_source_directory(src SRC)
if (HOST_PLATFORM STREQUAL "x64")
    list(REMOVE_ITEM SRC "src/signal_handler.cpp")
endif()

#[3]指定编译输出动态库
add_library(${library_name} SHARED
    ${SRC}
)

#[4]设置共享库的属性
set_target_properties(${library_name} PROPERTIES
    VERSION ${LIB_VERSION_MAJOR}.${LIB_VERSION_MINOR}.${LIB_VERSION_PATCH}
    SOVERSION ${LIB_VERSION_MAJOR}  # 主版本号，用于软链接
    OUTPUT_NAME ${library_name}             # 输出文件名
)

#[5]链接库
target_link_libraries(${library_name}
    ${CMAKE_SOURCE_DIR}/thirdparty/yaml-cpp/lib/${HOST_PLATFORM}/${SOC_NAME}/libyaml-cpp.a
)

#[6]安装
install(TARGETS ${library_name}
    ARCHIVE DESTINATION ${CMAKE_SOURCE_DIR}/install/${SOFTWARE_INSTALL_DIR}/lib
    LIBRARY DESTINATION ${CMAKE_SOURCE_DIR}/install/${SOFTWARE_INSTALL_DIR}/lib
    RUNTIME DESTINATION ${CMAKE_SOURCE_DIR}/install/${SOFTWARE_INSTALL_DIR}/bin
)
