#pragma once

#include "data_type.hpp"
#include "lawnmower_pose_api.h"
#include "mower_msgs/msg/slope_detection_result.hpp"
#include "ob_mower_fuse.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "ob_mower_msgs/perception_localization_alg_ctrl.h"
#include "utils/logger.hpp"
#include "utils/utils.hpp"

namespace fescue_iox
{

struct FusionOptData
{
    bool opt_status{false};
    GrassRegionResult opt_result;
};

void DoFusionSlopeImprove(const FuseObstacleResult &data,
                          const mower_msgs::msg::LawnmowerSlopeStatus &slope_result,
                          FusionOptData &opt_data);

} // namespace fescue_iox