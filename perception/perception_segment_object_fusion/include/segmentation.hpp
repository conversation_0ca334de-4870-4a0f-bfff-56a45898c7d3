#ifndef PERCEPTION_SEGMENTER_HPP
#define PERCEPTION_SEGMENTER_HPP

#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "mower_msgs/msg/soc_exception.hpp"
#include "ob_mower_msgs/segmenter_result_struct.h"
#include "ob_mower_seg_api.h"
#include "opencv2/opencv.hpp"
#include "segmentation_config.hpp"
#include "sensor_msgs/image__struct.h"
#include "utils/iceoryx_publisher_mower.hpp"

#include <string>
#include <vector>

namespace fescue_iox
{

struct CameraBevParam
{
    float blind_zone_dist; // 盲区距离(单位: m).
    ObPoint lLine_ptStart; // 左侧车道线起点(此处"起点"是指车道线上远离图片底边的一个点)
    ObPoint lLine_ptEnd;   // 左侧车道线终点(此处"终点"是指车道线上靠近图片底边的一个点)
    ObPoint rLine_ptStart; // 右侧车道线起点.
    ObPoint rLine_ptEnd;   // 右侧车道线终点.
};

class PerceptionSegmenter
{
    using iox_image_publisher = iox::popo::Publisher<sensor_msgs__msg__Image_iox>;
    using iox_segmenter_result_publisher = iox::popo::Publisher<fescue_msgs__msg__SegmenterResult>;

public:
    PerceptionSegmenter(bool save_img_enable, const std::string &save_img_dir, const std::string &conf_file,
                        const CameraBevParam &bev_param, bool bev_param_result);
    ~PerceptionSegmenter();
    bool DoSegmenter(const sensor_msgs__msg__Image_iox &image);
    void GetSegmenterInitResult(SegRgbInitResult &seg_init_result);
    bool SetSegmenterParam(SegInitParams &param, SegRgbInitResult &seg_init_result);
    SegInitParams GetSegmenterParam();
    const char *GetSegmenterAlgVersion();

private:
    void InitPublisher();
    void InitAlgParam();
    bool InitAlg(const CameraBevParam &bev_param, bool bev_param_result);
    void PrePerceptionSegmenter(const sensor_msgs__msg__Image_iox &image);
    void PostPerceptionSegmenter(const sensor_msgs__msg__Image_iox &image);
    void PublishResult();
    void PublishImage(const std::unique_ptr<iox_image_publisher> &pub_ptr,
                      const std::string &encoding,
                      const SegImageBuffer &image);
    void PublishOriginColorImage(const std::unique_ptr<iox_image_publisher> &pub_ptr,
                                 const sensor_msgs__msg__Image_iox &image,
                                 const std::string &encoding);
    void PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);
    void SaveImage(const sensor_msgs__msg__Image_iox &image);
    void ConfigParam2AlgParam(const SegmentAlgConfig &config, SegInitParams &param);
    void AlgParam2ConfigParam(const SegInitParams &param, SegmentAlgConfig &config);

private:
    bool save_img_enable_{false};
    std::string save_img_dir_{""};
    std::string conf_file_{""};

    std::unique_ptr<iox_image_publisher> pub_color_{nullptr};        // 只做debug
    std::unique_ptr<iox_image_publisher> pub_color_mask_;            // 只做debug
    std::unique_ptr<iox_image_publisher> pub_inverse_perspect_mask_; // 只做debug
    std::unique_ptr<iox_image_publisher> pub_inverse_perspect_rgb_;  // 只做debug
    std::unique_ptr<iox_image_publisher> pub_seg_img_result_{nullptr};
    std::unique_ptr<iox_segmenter_result_publisher> pub_segmenter_result_; // 语义分割算法结果发布
    std::unique_ptr<IceoryxPublisherMower<mower_msgs::msg::SocException>> pub_exception_{nullptr};

    uint64_t sec_;
    uint64_t nanosec_;
    uint64_t timestamp_ms_;
    std::string frame_id_;
    std::string encoding_;

    AVOIDOBS_HANDLE seg_handle_{nullptr}; // 语义分割计算的句柄
    SegImageBuffer input_image_;
    SegImageBuffer result_;
    SegImageBuffer debug_result_;
    BoundaryCode seg_result_;
    SegRgbInitResult seg_init_result_;

    static constexpr int32_t MAX_IMG_BUFF_SIZE = 1280 * 800 * 3;
    cv::Mat bgr_img_;
};

} // namespace fescue_iox

#endif
