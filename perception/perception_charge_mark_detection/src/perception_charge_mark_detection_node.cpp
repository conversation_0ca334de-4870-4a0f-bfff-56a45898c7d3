#include "perception_charge_mark_detection_node.hpp"

#include "mower_sdk_version.h"
#include "perception_charge_mark_detection_node_config.hpp"
#include "utils/algo_ctrl.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <cmath>      // for std::labs()
#include <filesystem> //c++17

namespace fescue_iox
{

PerceptionChargeMarkDetectionNode::PerceptionChargeMarkDetectionNode(const std::string &node_name)
    : node_name_(node_name)
{
    InitWorkingDirectory();
    InitHeartbeat();
    InitParam();
    InitLogger();
    InitAlg();
    InitSubscriber();
    InitService();
}

PerceptionChargeMarkDetectionNode::~PerceptionChargeMarkDetectionNode()
{
    LOG_WARN("PerceptionChargeMarkDetectionNode stop success!");
}

void PerceptionChargeMarkDetectionNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void PerceptionChargeMarkDetectionNode::InitParam()
{
    const std::string conf_file{"conf/perception_charge_mark_detection_node/perception_charge_mark_detection_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("Perception perception_charge_mark_detection_node create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("Perception perception_charge_mark_detection_node create config path failed!!!");
        }
    }
    if (!Config<PerceptionChargeMarkDetectionNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init perception_charge_mark_detection_node config parameters failed!");
    }
    PerceptionChargeMarkDetectionNodeConfig config = Config<PerceptionChargeMarkDetectionNodeConfig>::GetConfig();

    LOG_INFO("[perception_charge_mark_detection_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[perception_charge_mark_detection_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[perception_charge_mark_detection_node] compile time: {}", _COMPILE_TIME_);
    LOG_INFO("{}", config.toString().c_str());

    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    alg_conf_file_ = config.alg_conf_file;

    if (!Config<PerceptionChargeMarkDetectionNodeConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set perception_charge_mark_detection_node config parameters failed!");
    }

    CreateDirectories(log_dir_);
}

void PerceptionChargeMarkDetectionNode::InitLogger()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void PerceptionChargeMarkDetectionNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

void PerceptionChargeMarkDetectionNode::InitAlg()
{
    alg_ptr_ = std::make_unique<ChargeMarkDetectionAlg>(alg_conf_file_);
}

void PerceptionChargeMarkDetectionNode::InitSubscriber()
{
    sub_rgb_640x360_img_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "camera_color_640x360_result", 1, [this](const sensor_msgs__msg__Image_iox &data, const std::string &event) {
            (void)event;
            DealChargeMarkDetect(data);
        });
    sub_algo_ctrl_ = std::make_unique<IceoryxSubscriberMower<ob_mower_msgs::PerceptionLocalizationAlgCtrl>>(
        "perception_localization_alg_ctrl", 5, [this](const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data, const std::string &event) {
            (void)event;
            DealAlgCtrl(data);
        });
}

void PerceptionChargeMarkDetectionNode::InitService()
{
    service_get_node_param_ = std::make_unique<IceoryxServerMower<get_node_param_request, get_node_param_response>>(
        "get_perception_charg_mark_detection_node_param_request", 10U,
        [this](const get_node_param_request &request, get_node_param_response &response) {
            (void)request;
            response.success = DealGetNodeParam(response.data);
            LOG_INFO("Get perception_charge_mark_detection_node node params execute {}", response.success);
        });
    service_set_node_param_ = std::make_unique<IceoryxServerMower<set_node_param_request, set_node_param_response>>(
        "set_perception_charg_mark_detection_node_param_request", 10U,
        [this](const set_node_param_request &request, set_node_param_response &response) {
            response.success = DealSetNodeParam(request.data);
            LOG_INFO("Set perception_charge_mark_detection_node node params execute {}", response.success);
        });

    service_get_alg_param_ = std::make_unique<IceoryxServerMower<get_alg_param_request, get_alg_param_response>>(
        "get_charge_mark_detect_alg_param_request", 10U, [this](const get_alg_param_request &request, get_alg_param_response &response) {
            (void)request;
            response.success = DealGetAlgParam(response.data);
            LOG_INFO("Get perception_charge_mark_detection_node alg params execute {}", response.success);
        });
    service_set_alg_param_ = std::make_unique<IceoryxServerMower<set_alg_param_request, set_alg_param_response>>(
        "set_charge_mark_detect_alg_param_request", 10U, [this](const set_alg_param_request &request, set_alg_param_response &response) {
            response.success = DealSetAlgParam(request.data);
            LOG_INFO("Set perception_charge_mark_detection_node alg params execute {}", response.success);
        });

    service_get_algo_version_ = std::make_unique<IceoryxServerMower<ob_mower_srvs::GetAlgoVersionRequest, ob_mower_srvs::GetAlgoVersionResponse>>(
        "get_charge_mark_detection_algo_version", 10U,
        [this](const ob_mower_srvs::GetAlgoVersionRequest &request, ob_mower_srvs::GetAlgoVersionResponse &response) {
            (void)request;
            response.success = DealGetAlgVersion(response.data);
            response.timestamp = GetTimestampMs();
            LOG_INFO("Get perception_charge_mark_detection_node algo version execute {}", response.success);
        });
}

bool PerceptionChargeMarkDetectionNode::DealGetAlgParam(ob_mower_srvs::ChargeMarkDetectAlgParam &data)
{
    if (!alg_ptr_)
    {
        return false;
    }

    ChargeMarkInputParams param = alg_ptr_->GetAlgParam();
    data.chargeDetectProbThreshold = param.charge_params.prob_threshold;
    data.chargeDetectNMSThreshold = param.charge_params.nms_threshold;
    data.chargeDetectDirectionThreshold = param.charge_params.direction_threshold;
    data.poseThreshold = param.charge_params.pose_threshold;
    data.poseThresholdX = param.charge_params.pose_threshold_x;
    data.poseThresholdY = param.charge_params.pose_threshold_y;
    data.headRange0 = param.charge_params.head_range_0;
    data.stationRange0 = param.charge_params.station_range_0;
    data.poseMode = param.charge_params.pose_mode;
    data.rangeMode = param.charge_params.range_mode;
    data.markDetectProbThreshold = param.mark_params.prob_threshold;
    data.markDetectNMSThreshold = param.mark_params.nms_threshold;
    data.markDetectDirectionThreshold = param.mark_params.direction_threshold;
    data.areaThreshold = param.mark_params.area_threshold;
    data.showImage = param.show_image;
    data.logLevel = param.log_level;
    data.saveMode = param.save_mode;
    data.ignoreNum = param.ignore_num;
    return true;
}

bool PerceptionChargeMarkDetectionNode::DealSetAlgParam(const ob_mower_srvs::ChargeMarkDetectAlgParam &data)
{
    if (!alg_ptr_)
    {
        return false;
    }

    ChargeMarkInputParams param = alg_ptr_->GetAlgParam();
    param.charge_params.prob_threshold = data.chargeDetectProbThreshold;
    param.charge_params.nms_threshold = data.chargeDetectNMSThreshold;
    param.charge_params.direction_threshold = data.chargeDetectDirectionThreshold;
    param.charge_params.pose_threshold = data.poseThreshold;
    param.charge_params.pose_threshold_x = data.poseThresholdX;
    param.charge_params.pose_threshold_y = data.poseThresholdY;
    param.charge_params.head_range_0 = data.headRange0;
    param.charge_params.station_range_0 = data.stationRange0;
    param.charge_params.pose_mode = data.poseMode;
    param.charge_params.range_mode = data.rangeMode;
    param.mark_params.prob_threshold = data.markDetectProbThreshold;
    param.mark_params.nms_threshold = data.markDetectNMSThreshold;
    param.mark_params.direction_threshold = data.markDetectDirectionThreshold;
    param.mark_params.area_threshold = data.areaThreshold;
    param.show_image = data.showImage;
    param.log_level = data.logLevel;
    param.save_mode = data.saveMode;
    param.ignore_num = data.ignoreNum;
    return alg_ptr_->SetAlgParam(param);
}

bool PerceptionChargeMarkDetectionNode::DealGetNodeParam(ob_mower_srvs::NodeParamData &data)
{
    PerceptionChargeMarkDetectionNodeConfig config = Config<PerceptionChargeMarkDetectionNodeConfig>::GetConfig();
    data.console_log_level.unsafe_assign(config.common_conf.console_log_level.c_str());
    data.file_log_level.unsafe_assign(config.common_conf.file_log_level.c_str());
    return true;
}

bool PerceptionChargeMarkDetectionNode::DealSetNodeParam(const ob_mower_srvs::NodeParamData &data)
{
    console_log_level_ = std::string(data.console_log_level.c_str());
    file_log_level_ = std::string(data.file_log_level.c_str());
    InitLogger();
    PerceptionChargeMarkDetectionNodeConfig config = Config<PerceptionChargeMarkDetectionNodeConfig>::GetConfig();
    config.common_conf.console_log_level = console_log_level_;
    config.common_conf.file_log_level = file_log_level_;
    Config<PerceptionChargeMarkDetectionNodeConfig>::SetConfig(config);
    LOG_INFO("New PerceptionChargeMarkDetectionNodeConfig params: {}", config.toString().c_str());

    return true;
}

bool PerceptionChargeMarkDetectionNode::DealGetAlgVersion(ob_mower_srvs::AlgoVersionDataVect &data)
{
    if (alg_ptr_)
    {
        ob_mower_srvs::AlgoVersionData version_data;
        version_data.algo_name.unsafe_assign("charge_mark_detection");
        version_data.version.unsafe_assign(alg_ptr_->GetAlgVersion());
        data.version_data_vect.push_back(version_data);
    }
    return true;
}

void PerceptionChargeMarkDetectionNode::DealChargeMarkDetect(const sensor_msgs__msg__Image_iox &data)
{
    if (!alg_enable_)
    {
        LOG_DEBUG("Charge mark detection alg is disabled!");
        return;
    }
    if (alg_ptr_)
    {
        alg_ptr_->DoChargeMarkDetect(data);
    }
}

void PerceptionChargeMarkDetectionNode::DealAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data)
{
    using namespace ob_mower_msgs;
    for (size_t i = 0; i < data.ctrl_list.size(); i++)
    {
        if (data.ctrl_list[i].alg_type == PerceptionLocalizationAlgType::PERCEPTION_CHARGE_MARK_DETECTION)
        {
            alg_enable_ = (data.ctrl_list[i].alg_state == PerceptionLocalizationAlgState::ENABLE ? true : false);
            LOG_WARN("{} {}", asStringLiteral(data.ctrl_list[i].alg_type).c_str(),
                     asStringLiteral(data.ctrl_list[i].alg_state).c_str());
            break;
        }
    }
}

} // namespace fescue_iox