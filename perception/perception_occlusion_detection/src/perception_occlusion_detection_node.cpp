#include "perception_occlusion_detection_node.hpp"

#include "mower_sdk_version.h"
#include "perception_occlusion_detection_node_config.hpp"
#include "utils/algo_ctrl.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <cmath>      // for std::labs()
#include <filesystem> //c++17

namespace fescue_iox
{

PerceptionOcclusionDetectionNode::PerceptionOcclusionDetectionNode(const std::string &node_name)
    : node_name_(node_name)
{
    InitWorkingDirectory();
    InitParam();
    InitLogger();
    InitAlg();
    InitSubscriber();
    InitService();
    InitHeartbeat();
}

PerceptionOcclusionDetectionNode::~PerceptionOcclusionDetectionNode()
{
    LOG_WARN("PerceptionOcclusionDetectionNode stop success!");
}

void PerceptionOcclusionDetectionNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void PerceptionOcclusionDetectionNode::InitParam()
{
    const std::string conf_file{"conf/perception_occlusion_detection_node/perception_occlusion_detection_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("Perception occlusion node create config path failed! {}", conf_file.c_str());
        }
    }
    if (!Config<PerceptionOcclusionDetectionNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init occlusion node config parameters failed!");
    }
    PerceptionOcclusionDetectionNodeConfig config = Config<PerceptionOcclusionDetectionNodeConfig>::GetConfig();

    LOG_INFO("[perception_occlusion_detection_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[perception_occlusion_detection_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[perception_occlusion_detection_node] compile time: {}", _COMPILE_TIME_);
    LOG_INFO("{}", config.toString().c_str());

    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    occlusion_alg_conf_file_ = config.occlusion_alg_conf_file;

    if (!Config<PerceptionOcclusionDetectionNodeConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set occlusion node config parameters failed!");
    }

    CreateDirectories(log_dir_);
}

void PerceptionOcclusionDetectionNode::InitLogger()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void PerceptionOcclusionDetectionNode::InitAlg()
{
    occlusion_detectiong_alg_ = std::make_unique<PerceptionOcclusionDetectionAlg>(occlusion_alg_conf_file_);
}

void PerceptionOcclusionDetectionNode::InitSubscriber()
{
    sub_rgb_640x360_img_ = std::make_unique<IceoryxSubscriberMower<sensor_msgs__msg__Image_iox>>(
        "camera_color_640x360_result", 1, [this](const sensor_msgs__msg__Image_iox &data, const std::string &event) {
            (void)event;
            DealOcclusionDetection(data);
        });

    sub_algo_ctrl_ = std::make_unique<IceoryxSubscriberMower<ob_mower_msgs::PerceptionLocalizationAlgCtrl>>(
        "perception_localization_alg_ctrl", 5, [this](const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data, const std::string &event) {
            (void)event;
            DealAlgCtrl(data);
        });
}

void PerceptionOcclusionDetectionNode::InitService()
{
    service_get_node_param_ = std::make_unique<IceoryxServerMower<get_node_param_request, get_node_param_response>>(
        "get_perception_occlusion_node_param_request", 10U, [this](const get_node_param_request &request, get_node_param_response &response) {
            (void)request;
            response.success = GetNodeParam(response.data);
            LOG_INFO("Get perception occlusion detection node params execute {}", response.success);
        });
    service_set_node_param_ = std::make_unique<IceoryxServerMower<set_node_param_request, set_node_param_response>>(
        "set_perception_occlusion_node_param_request", 10U, [this](const set_node_param_request &request, set_node_param_response &response) {
            response.success = SetNodeParam(request.data);
            LOG_INFO("Set perception occlusion detection node params execute {}", response.success);
        });

    service_get_alg_param_ = std::make_unique<IceoryxServerMower<get_alg_param_request, get_alg_param_response>>(
        "get_perception_occlusion_alg_param_request", 10U, [this](const get_alg_param_request &request, get_alg_param_response &response) {
            (void)request;
            response.success = GetOcclusionDetectionAlgParam(response.data);
            LOG_INFO("Get perception occlusion detection alg params execute {}", response.success);
        });
    service_set_alg_param_ = std::make_unique<IceoryxServerMower<set_alg_param_request, set_alg_param_response>>(
        "set_perception_occlusion_alg_param_request", 10U, [this](const set_alg_param_request &request, set_alg_param_response &response) {
            response.success = SetOcclusionDetectionAlgParam(request.data);
            LOG_INFO("Set perception occlusion detection alg params execute {}", response.success);
        });

    service_get_algo_version_ = std::make_unique<IceoryxServerMower<ob_mower_srvs::GetAlgoVersionRequest, ob_mower_srvs::GetAlgoVersionResponse>>(
        "get_perception_occlusion_algo_version", 10U, [this](const ob_mower_srvs::GetAlgoVersionRequest &request, ob_mower_srvs::GetAlgoVersionResponse &response) {
            (void)request;
            response.success = GetOcclusionDetectionAlgVersion(response.data);
            response.timestamp = GetTimestampMs();
            LOG_INFO("Get perception occlusion detection alg version execute {}", response.success);
        });
}

void PerceptionOcclusionDetectionNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

bool PerceptionOcclusionDetectionNode::GetOcclusionDetectionAlgVersion(ob_mower_srvs::AlgoVersionDataVect &data)
{
    if (!occlusion_detectiong_alg_)
    {
        return false;
    }
    ob_mower_srvs::AlgoVersionData version_data;
    version_data.algo_name.unsafe_assign("occlusion_detection");
    version_data.version.unsafe_assign(occlusion_detectiong_alg_->GetOcclusionAlgVersion());
    data.version_data_vect.push_back(version_data);
    return true;
}
bool PerceptionOcclusionDetectionNode::GetOcclusionDetectionAlgParam(fescue_msgs__msg__PerceptionOcclusionDetectionAlgParam &data)
{
    if (!occlusion_detectiong_alg_)
    {
        return false;
    }

    OcclusionClsInputParams param = occlusion_detectiong_alg_->GetOcclusionAlgParam();
    data.class_num = param.class_num;
    data.prob_threshold = param.prob_threshold;
    data.debug_mode = param.debug_mode;
    data.ignore_num = param.ignore_num;
    data.total_num = param.total_num;
    data.occlusion_num = param.occlusion_num;
    data.log_level = param.log_level;
    data.frame_interval = param.frame_interval;
    data.save_log = param.save_log;
    data.test_mode = param.test_mode;
    LOG_INFO("Get perception occlusion param frame_interval: {}", param.frame_interval);
    return true;
}

bool PerceptionOcclusionDetectionNode::SetOcclusionDetectionAlgParam(const fescue_msgs__msg__PerceptionOcclusionDetectionAlgParam &data)
{
    if (!occlusion_detectiong_alg_)
    {
        return false;
    }

    OcclusionClsInputParams param = occlusion_detectiong_alg_->GetOcclusionAlgParam();
    param.class_num = data.class_num;
    param.prob_threshold = data.prob_threshold;
    param.debug_mode = data.debug_mode;
    param.ignore_num = data.ignore_num;
    param.total_num = data.total_num;
    param.occlusion_num = data.occlusion_num;
    param.log_level = data.log_level;
    param.frame_interval = data.frame_interval;
    param.save_log = data.save_log;
    param.test_mode = data.test_mode;
    LOG_INFO("Set perception occlusion param frame_interval: {}", param.frame_interval);
    return occlusion_detectiong_alg_->SetOcclusionAlgParam(param);
}

bool PerceptionOcclusionDetectionNode::GetNodeParam(ob_mower_srvs::NodeParamData &data)
{
    PerceptionOcclusionDetectionNodeConfig config = Config<PerceptionOcclusionDetectionNodeConfig>::GetConfig();
    data.console_log_level.unsafe_assign(config.common_conf.console_log_level.c_str());
    data.file_log_level.unsafe_assign(config.common_conf.file_log_level.c_str());
    return true;
}

bool PerceptionOcclusionDetectionNode::SetNodeParam(const ob_mower_srvs::NodeParamData &data)
{
    console_log_level_ = std::string(data.console_log_level.c_str());
    file_log_level_ = std::string(data.file_log_level.c_str());

    PerceptionOcclusionDetectionNodeConfig config = Config<PerceptionOcclusionDetectionNodeConfig>::GetConfig();
    config.common_conf.console_log_level = console_log_level_;
    config.common_conf.file_log_level = file_log_level_;
    Config<PerceptionOcclusionDetectionNodeConfig>::SetConfig(config);
    LOG_INFO("New PerceptionOcclusionDetectionNodeConfig params: {}", config.toString().c_str());

    InitLogger();

    return true;
}

void PerceptionOcclusionDetectionNode::DealOcclusionDetection(const sensor_msgs__msg__Image_iox &data)
{
    if (!occlusion_alg_enable_)
    {
        LOG_DEBUG("Occlusion detection alg is disabled by config!");
        return;
    }
    if (occlusion_detectiong_alg_)
    {
        occlusion_detectiong_alg_->DoOcclusionDetection(data);
    }
}

void PerceptionOcclusionDetectionNode::DealAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data)
{
    using namespace ob_mower_msgs;
    for (size_t i = 0; i < data.ctrl_list.size(); i++)
    {
        if (data.ctrl_list[i].alg_type == PerceptionLocalizationAlgType::PERCEPTION_OCCLUSION_DETECTION)
        {
            occlusion_alg_enable_ = (data.ctrl_list[i].alg_state == PerceptionLocalizationAlgState::ENABLE ? true : false);
            LOG_WARN("{} {}", asStringLiteral(data.ctrl_list[i].alg_type).c_str(), asStringLiteral(data.ctrl_list[i].alg_state).c_str());
            break;
        }
    }
}

} // namespace fescue_iox
